package com.iqiyi.vip.present.history;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Set;

import com.qiyi.vip.trade.dataservice.client.dto.OrderDto;

/**
 * @Author: <PERSON>
 * @Date: 2022/4/18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderExpandDto extends OrderDto {

    /**
     * 无需赠送的会员类型
     */
    private Set<Integer> notNeedPresentVipTypeSet = Sets.newHashSet();
}
