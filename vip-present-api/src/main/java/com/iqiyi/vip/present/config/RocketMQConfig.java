package com.iqiyi.vip.present.config;

import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * Created at: 2022-04-16
 *
 * <AUTHOR>
 */
@Configuration
public class RocketMQConfig {

    @Bean
    @Profile({"!i18n"})
    @ConfigurationProperties(prefix = "exist.gitv.need.present.order.producer")
    public RocketMQProperties gitvNeedPresentOrderProducerProperties() {
        return new RocketMQProperties();
    }

    @Profile({"!i18n"})
    @Bean(name = "gitvNeedPresentOrderProducer", initMethod = "start", destroyMethod = "shutdown")
    public DefaultMQProducer gitvNeedPresentOrderProducer() {
        RocketMQProperties properties = gitvNeedPresentOrderProducerProperties();
        DefaultMQProducer producer = new DefaultMQProducer(properties.getGroupname());
        producer.setNamesrvAddr(properties.getAddress());
        producer.setToken(properties.getToken());
        return producer;
    }

}
