package com.iqiyi.vip.present.producer;

import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.Message;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.List;

import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.utils.RMQMsgSender;

/**
 * @Author: Lin Peihui
 * @Date: 2022/4/16
 */
@Profile({"!i18n"})
@Component
public class GitvNeedPresentOrderMsgSender extends RMQMsgSender {

    private static final String GITV_NEED_PRESENT_ORDER_TOPIC = "exist_gitv_user_need_present_order";

    @Resource
    DefaultMQProducer gitvNeedPresentOrderProducer;

    public boolean doSend(List<OrderMessage> orderMessages) {
        if (CollectionUtils.isEmpty(orderMessages)) {
            return false;
        }
        for (OrderMessage orderMessage : orderMessages) {
            Message message = new Message(GITV_NEED_PRESENT_ORDER_TOPIC, JSON.toJSONBytes(orderMessage));
            message.setKeys(orderMessage.getOrderCode());
            doSend(gitvNeedPresentOrderProducer, message, (mqs, msg, arg) -> {
                Long id = (Long) arg;
                int index = Math.abs(id.hashCode() % mqs.size());
                // 分区顺序：同一个模值的消息在同一个队列中
                return mqs.get(index);
            }, orderMessage.getUid());
        }
        return true;
    }

}
