package com.iqiyi.vip.present.history;

import com.google.common.collect.Lists;

import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.utils.DateUtils;
import com.qiyi.vip.trade.dataservice.client.dto.OrderDto;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.*;
import java.util.List;


/**
 * 与数据库交互
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class OrderTidbJdbcTool {

    @Value("${order.tidb.jdbc.url}")
    private String url;
    @Value("${order.tidb.jdbc.username}")
    private String username;
    @Value("${order.tidb.jdbc.password}")
    private String password;

    @Resource
    private CloudConfig cloudConfig;

    public List<OrderExpandDto> selectUserOrderList(Connection connection, long userId) {
        List<OrderExpandDto> orderExpandDtoList = Lists.newArrayList();
        String historyPresentPids = cloudConfig.getProperty("history_present_pids", "");
        StringBuilder sqlBuilder = new StringBuilder(1024);
        sqlBuilder.append(
            "select user_id, order_code, trade_code, start_time, deadline, pay_time, fv, name, amount, auto_renew, real_fee, product_id, status, " +
                "pay_type,pay_time,platform,type,charge_type,refer,partner from orders where user_id = ? and product_id in (")
            .append(historyPresentPids).append(")")
            .append(" and status in (1,6,12) and pay_time >= ? and pay_time < ? ");

        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        String historyPresentOrderPayTimeGE = cloudConfig.getProperty("history_present_order_pay_time_GE", "");
        String historyPresentOrderPayTimeLT = cloudConfig.getProperty("history_present_order_pay_time_LT", "");
        if (StringUtils.isBlank(historyPresentOrderPayTimeGE) || StringUtils.isBlank(historyPresentOrderPayTimeLT)) {
            log.error("Select user order list error. historyPresentOrderPayTimeGE or historyPresentOrderPayTimeLT is empty. uid:{}", userId);
            return null;
        }
        try {
            preparedStatement = connection.prepareStatement(sqlBuilder.toString());
            preparedStatement.setLong(1, userId);
            preparedStatement.setString(2, historyPresentOrderPayTimeGE);
            preparedStatement.setString(3, historyPresentOrderPayTimeLT);
            resultSet = preparedStatement.executeQuery();
            while (resultSet.next()) {
                OrderExpandDto order = new OrderExpandDto();
                order.setUserId(resultSet.getLong("user_id"));
                order.setOrderCode(resultSet.getString("order_code"));
                order.setTradeCode(resultSet.getString("trade_code"));
                order.setStartTime(resultSet.getTimestamp("start_time"));
                order.setDeadline(resultSet.getTimestamp("deadline"));
                order.setPayTime(resultSet.getTimestamp("pay_time"));
                order.setFv(resultSet.getString("fv"));
                order.setName(resultSet.getString("name"));
                order.setAmount(resultSet.getInt("amount"));
                order.setAutoRenew(resultSet.getInt("auto_renew"));
                order.setRealFee(resultSet.getInt("real_fee"));
                order.setProductId(resultSet.getLong("product_id"));
                order.setStatus(resultSet.getInt("status"));
                order.setPayType(resultSet.getInt("pay_type"));
                order.setPayTime(resultSet.getTimestamp("pay_time"));
                order.setPlatform(resultSet.getLong("platform"));
                order.setType(resultSet.getInt("type"));
                order.setChargeType(resultSet.getInt("charge_type"));
                order.setRefer(resultSet.getString("refer"));
                order.setPartner(resultSet.getString("partner"));
                orderExpandDtoList.add(order);
            }
        } catch (Exception e) {
            log.error("Select user order list error. uid:{}", userId, e);
        } finally {
            close(preparedStatement, resultSet);
        }
        return orderExpandDtoList;
    }

    public OrderExpandDto selectByOrderCode(Connection connection, String orderCode) {
        List<OrderExpandDto> orderList = Lists.newArrayList();
        StringBuilder sqlBuilder = new StringBuilder(1024);
        sqlBuilder.append(
            "select user_id, order_code, trade_code, start_time, deadline, pay_time, fv, name, amount, auto_renew, real_fee, product_id, status, " +
                "pay_type,pay_time,platform,type,charge_type,refer,partner from orders where order_code = ?");

        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        try {
            preparedStatement = connection.prepareStatement(sqlBuilder.toString());
            preparedStatement.setString(1, orderCode);
            resultSet = preparedStatement.executeQuery();
            while (resultSet.next()) {
                OrderExpandDto order = new OrderExpandDto();
                order.setUserId(resultSet.getLong("user_id"));
                order.setOrderCode(resultSet.getString("order_code"));
                order.setTradeCode(resultSet.getString("trade_code"));
                order.setStartTime(resultSet.getTimestamp("start_time"));
                order.setDeadline(resultSet.getTimestamp("deadline"));
                order.setPayTime(resultSet.getTimestamp("pay_time"));
                order.setFv(resultSet.getString("fv"));
                order.setName(resultSet.getString("name"));
                order.setAmount(resultSet.getInt("amount"));
                order.setAutoRenew(resultSet.getInt("auto_renew"));
                order.setRealFee(resultSet.getInt("real_fee"));
                order.setProductId(resultSet.getLong("product_id"));
                order.setStatus(resultSet.getInt("status"));
                order.setPayType(resultSet.getInt("pay_type"));
                order.setPayTime(resultSet.getTimestamp("pay_time"));
                order.setPlatform(resultSet.getLong("platform"));
                order.setType(resultSet.getInt("type"));
                order.setChargeType(resultSet.getInt("charge_type"));
                order.setRefer(resultSet.getString("refer"));
                order.setPartner(resultSet.getString("partner"));
                orderList.add(order);
            }
        } catch (Exception e) {
            log.error("Select user order list error. orderCode:{}", orderCode, e);
        } finally {
            close(preparedStatement, resultSet);
        }
        if (CollectionUtils.isEmpty(orderList)) {
            return null;
        } else {
            return orderList.get(0);
        }
    }

    public OrderExpandDto selectByTradeCode(Connection connection, String tradeCode) {
        List<OrderExpandDto> orderExpandDtoList = Lists.newArrayList();
        StringBuilder sqlBuilder = new StringBuilder(1024);
        sqlBuilder.append("select user_id, order_code, trade_code, status from orders where trade_code = ?");

        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        try {
            preparedStatement = connection.prepareStatement(sqlBuilder.toString());
            preparedStatement.setString(1, tradeCode);
            resultSet = preparedStatement.executeQuery();
            while (resultSet.next()) {
                OrderExpandDto order = new OrderExpandDto();
                order.setUserId(resultSet.getLong("user_id"));
                order.setOrderCode(resultSet.getString("order_code"));
                order.setTradeCode(resultSet.getString("trade_code"));
                order.setStatus(resultSet.getInt("status"));
                orderExpandDtoList.add(order);
            }
        } catch (Exception e) {
            log.error("Select user order list error. tradeCode:{}", tradeCode, e);
        } finally {
            close(preparedStatement, resultSet);
        }
        if (CollectionUtils.isEmpty(orderExpandDtoList)) {
            return null;
        } else {
            return orderExpandDtoList.get(0);
        }
    }

    private Connection open() throws Exception {
        Class.forName("com.mysql.jdbc.Driver");
        return DriverManager.getConnection(url, username, password);
    }

    private void close(PreparedStatement preparedStatement, ResultSet resultSet) {
        try {
            if (resultSet != null) {
                resultSet.close();
            }
            if (preparedStatement != null) {
                preparedStatement.close();
            }
        } catch (SQLException e) {
            log.error("", e);
        }
    }


}
