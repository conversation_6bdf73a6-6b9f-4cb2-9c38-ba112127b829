package com.iqiyi.vip.present;

import com.alibaba.csp.sentinel.init.InitExecutor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

@SpringBootApplication
@MapperScan("com.iqiyi.vip.present.mapper")
@ComponentScan(value = {"com.iqiyi.vip"},
        excludeFilters = {
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.iqiyi\\.vip\\.uitls\\.task\\..*")
                , @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.iqiyi\\.vip\\.uitls\\.utils\\..*")}
)
public class VipPresentApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        triggerSentinelInit();
        SpringApplication.run(VipPresentApplication.class, args);
    }

    private static void triggerSentinelInit() {
        new Thread(() -> InitExecutor.doInit()).start();
    }
}