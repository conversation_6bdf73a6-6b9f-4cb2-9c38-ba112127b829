package com.iqiyi.vip.present.aspect;

import com.alibaba.csp.sentinel.annotation.aspectj.SentinelResourceAspect;
import com.alibaba.csp.sentinel.datasource.apollo.Constants;
import com.alibaba.csp.sentinel.datasource.apollo.SentinelApolloDataSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/7/2
 * @apiNote
 */
@Configuration
public class SentinelAspectConfiguration {
    @Bean
    public SentinelResourceAspect sentinelResourceAspect() {
        return new SentinelResourceAspect();
    }

    @Bean
    public SentinelApolloDataSource sentinelApolloDataSource() {
        SentinelApolloDataSource sentinelApolloDataSource = new SentinelApolloDataSource();
        // 各规则的配置中心动态加载按需分别开启
        // 限流
        sentinelApolloDataSource.flowRuleSwitch(Constants.ON);
        // 降级
        sentinelApolloDataSource.degradeRuleSwitch(Constants.ON);
        // 授权
        sentinelApolloDataSource.authorityRuleSwitch(Constants.ON);
        // 系统保护
        sentinelApolloDataSource.systemRuleSwitch(Constants.ON);
        // 参数限流
        sentinelApolloDataSource.paramFlowRuleSwitch(Constants.ON);
        // 网关 普通服务需关闭此项
        //sentinelApolloDataSource.gatewayRuleSwitch(Constants.ON);
        return sentinelApolloDataSource;
    }
}
