package com.iqiyi.vip.present.history;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.qiyi.vip.trade.dataservice.client.dto.OrderDto;
import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.vip.present.consts.VipTypeEnum;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.model.PresentProduct;
import com.iqiyi.vip.present.producer.GitvNeedPresentOrderMsgSender;
import com.iqiyi.vip.present.service.PresentProductService;
import com.iqiyi.vip.present.utils.ApplicationContextUtil;
import com.iqiyi.vip.present.utils.DateUtils;

/**
 * @Author: Lin Peihui
 * @Date: 2022/4/14
 */
@Slf4j
public class PrepareOrderPresentMsgTask implements Runnable {

    OrderTidbJdbcTool orderTidbJdbcTool = (OrderTidbJdbcTool) ApplicationContextUtil.getBean(OrderTidbJdbcTool.class);
    VipPresentJdbcTool vipPresentJdbcTool = (VipPresentJdbcTool) ApplicationContextUtil.getBean(VipPresentJdbcTool.class);
    TradeOrderService tradeOrderService = (TradeOrderService) ApplicationContextUtil.getBean(TradeOrderService.class);
    PresentProductService productService = (PresentProductService) ApplicationContextUtil.getBean(PresentProductService.class);
    QiYueDbJdbcTool qiYueDbJdbcTool = (QiYueDbJdbcTool) ApplicationContextUtil.getBean(QiYueDbJdbcTool.class);
    private CloudConfig cloudConfig = (CloudConfig) ApplicationContextUtil.getBean(CloudConfig.class);
    private GitvNeedPresentOrderMsgSender msgSender = (GitvNeedPresentOrderMsgSender) ApplicationContextUtil.getBean(GitvNeedPresentOrderMsgSender.class);

    private PrepareOrderPresentMsgReq req;

    public PrepareOrderPresentMsgTask(PrepareOrderPresentMsgReq req) {
        this.req = req;
    }

    @Override
    public void run() {
        String threadName = Thread.currentThread().getName();
        log.info("[{}] [starting]", threadName);
        List<Integer> uidSuffixList = Lists.newArrayList();
        Connection vipUserDbConnection = null;
        long start = System.currentTimeMillis();
        try {
            vipUserDbConnection = getVipUserDbConnection();
            for (int i = req.getUidSuffixStart(); i <= req.getUidSuffixEnd(); i++) {
                uidSuffixList.add(i);
            }
            if (req.getDoneFlag() == null) {
                req.setDoneFlag(0);
            }
            ExecutorService threadPoolExecutor = getExecutorService();
            boolean stopHistoryPresent = cloudConfig.getBooleanProperty("stop_history_present", true);
            Long presentUserNumUpper = cloudConfig.getLongProperty("present_user_num_upper", Long.MAX_VALUE);
            long presentUserNum = 0L;
            while (!stopHistoryPresent && presentUserNum < presentUserNumUpper) {
                String vipRightDeadlineGT = DateUtils.getCurrentTimeStr();
                Integer historyPresentBatchLimit = cloudConfig.getIntProperty("history_present_batch_limit", 10000);
                if (historyPresentBatchLimit > presentUserNumUpper) {
                    historyPresentBatchLimit = presentUserNumUpper.intValue();
                }
                List<UserInfo> vipUsers;
                if (CollectionUtils.isNotEmpty(req.getUidWhiteList())) {
                    vipUsers = vipPresentJdbcTool.selectByUidWhiteList(vipUserDbConnection, req.getUidWhiteList(), vipRightDeadlineGT,
                        req.getBadReason(), req.getDoneFlag(), historyPresentBatchLimit);
                } else {
                    vipUsers = vipPresentJdbcTool.selectUsers(vipUserDbConnection, uidSuffixList, vipRightDeadlineGT,
                        req.getBadReason(), req.getDoneFlag(), historyPresentBatchLimit);
                }

                if (CollectionUtils.isEmpty(vipUsers)) {
                    log.error("[empty users] [uidSuffixList:{}]", uidSuffixList);
                    break;
                }
                log.info("[get users done] [count:{}] [cost:{}ms]", vipUsers.size(), (System.currentTimeMillis() - start));
                Integer corePoolSize = cloudConfig.getIntProperty("CORE_POOL_SIZE", 10);
                // 将用户分成多组，多线程执行捞取订单及发消息逻辑
                List<List<UserInfo>> partitions = Lists.partition(vipUsers, corePoolSize);
                CountDownLatch latch = new CountDownLatch(partitions.size());
                for (List<UserInfo> partition : partitions) {
                    threadPoolExecutor.submit(new LoadOrderAndSendFinishedMsgTask(partition, latch, msgSender));
                    //local debug
//                    LoadOrderAndSendFinishedMsgTask task = new LoadOrderAndSendFinishedMsgTask(partition, latch, msgSender);
//                    task.run();
                }
                try {
                    latch.await();
                    presentUserNum += vipUsers.size();
                } catch (InterruptedException e) {
                    log.error("Check refresh progress error. params:{}", req, e);
                    break;
                }
                stopHistoryPresent = cloudConfig.getBooleanProperty("stop_history_present", true);
                presentUserNumUpper = cloudConfig.getLongProperty("present_user_num_upper", Long.MAX_VALUE);
            }
            log.info("[{}] finished", threadName);
        } catch (Exception e) {
            log.error("[{}] [exception]", threadName, e);
        } finally {
            close(vipUserDbConnection);
        }

        log.info("[{}] [done] [cost:{}ms]", threadName, (System.currentTimeMillis() - start));
    }

    private ExecutorService getExecutorService() {
        Integer corePoolSize = cloudConfig.getIntProperty("CORE_POOL_SIZE", 10);
        Integer maxPoolSize = cloudConfig.getIntProperty("MAX_POOL_SIZE", 10);
        Integer keepAlive = cloudConfig.getIntProperty("KEEP_ALIVE_TIME", 60);
        return new ThreadPoolExecutor(corePoolSize, maxPoolSize, keepAlive, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>());
    }

    private void packBadUser(UserInfo badUser, BadUserEnum badUserEnum) {
        log.warn("[bad user] [uid:{}] [reason:{}]", badUser.getUserId(), badUserEnum.getReason());
        badUser.setBadReason(badUserEnum.getReason());
    }

    private Connection getOrderTiDbConnection() throws Exception {
        String url = ApplicationContextUtil.getEnvProperty("order.tidb.jdbc.url");
        String username = ApplicationContextUtil.getEnvProperty("order.tidb.jdbc.username");
        String password = ApplicationContextUtil.getEnvProperty("order.tidb.jdbc.password");

        Class.forName("com.mysql.jdbc.Driver");
        return DriverManager.getConnection(url, username, password);
    }

    private Connection getVipUserDbConnection() throws Exception {
        String url = ApplicationContextUtil.getEnvProperty("history.db.url");
        String username = ApplicationContextUtil.getEnvProperty("history.db.username");
        String password = ApplicationContextUtil.getEnvProperty("history.db.password");

        Class.forName("com.mysql.jdbc.Driver");
        return DriverManager.getConnection(url, username, password);
    }

    private void close(Connection connection) {
        try {
            if (connection != null) {
                connection.close();
            }
        } catch (SQLException e) {
            log.error("Close connection error", e);
        }
    }

    @NoArgsConstructor
    @AllArgsConstructor
    class LoadOrderAndSendFinishedMsgTask implements Runnable {

        private List<UserInfo> userInfoList;
        private CountDownLatch latch;
        private GitvNeedPresentOrderMsgSender msgSender;

        @Override
        public void run() {
            String threadName = Thread.currentThread().getName();
            Connection orderTidbConnection;
            Connection vipUserConnection = null;
            try {
                orderTidbConnection = getOrderTiDbConnection();
                vipUserConnection = getVipUserDbConnection();
                for (UserInfo vipUser : userInfoList) {
                    try {
                        // 校验通过
                        log.info("[{}] [start checking] [uid:{}]", threadName, vipUser.getUserId());
                        List<OrderExpandDto> validOrders = getValidOrders(vipUser, orderTidbConnection, vipUserConnection, threadName);
                        boolean sendSuccess = false;
                        if (CollectionUtils.isNotEmpty(validOrders)) {
                            //排序
                            validOrders.sort(Comparator.comparing(OrderDto::getStartTime));
                            // 校验通过
                            log.info("[{}] [pass checked] [uid:{}] [validOrdersSize:{}]", threadName, vipUser.getUserId(), validOrders.size());
                            vipUser.setPresentTime(new Timestamp(System.currentTimeMillis()));

                            msgSender.doSend(buildOrderMessage(validOrders));
                            sendSuccess = true;
                        }
                        //更新vipUser
                        if (StringUtils.isNotBlank(req.getBadReason())) {
                            if (sendSuccess) {
                                vipUser.setBadReason(BadUserEnum.RETRY_SUCCESS.getReason());
                            } else {
                                vipUser.setBadReason(vipUser.getBadReason() + "_R");
                            }
                        }
                        vipPresentJdbcTool.updateUser(vipUserConnection, vipUser);
                    } catch (Exception e) {
                        log.error("[{}] [uid:{}] [exception]", threadName, vipUser.getUserId(), e);
                    }
                }
            } catch (Exception e) {
                log.error("[{}] [exception]", threadName, e);
            } finally {
                latch.countDown();
                close(vipUserConnection);
            }
        }
    }

    private List<OrderMessage> buildOrderMessage(List<OrderExpandDto> orderExpandDtoList) {
        List<OrderMessage> orderMessages = new ArrayList<>();
        for (OrderExpandDto orderExpandDto : orderExpandDtoList) {
            try {
                PresentProduct product = productService.getById(orderExpandDto.getProductId());
                String platformCode = qiYueDbJdbcTool.getPlatformCodeById(orderExpandDto.getPlatform());
                OrderMessage orderMessage = new OrderMessage();
                orderMessage.setUid(orderExpandDto.getUserId());
                orderMessage.setOrderCode(orderExpandDto.getOrderCode());
                orderMessage.setPayType(orderExpandDto.getPayType());
                if (product != null) {
                    orderMessage.setPid(product.getCode());
                    orderMessage.setProductSubtype(Objects.toString(product.getSubType(), null));
                    if (product.getSourceSubType() != null) {
                        orderMessage.setSourceType(product.getSourceSubType().toString());
                    }
                }
                if (platformCode != null) {
                    orderMessage.setPlatformCode(platformCode);
                }
                orderMessage.setPayTime(orderExpandDto.getPayTime());
                orderMessage.setStartTime(orderExpandDto.getStartTime());
                orderMessage.setEndTime(orderExpandDto.getDeadline());
                orderMessage.setAmount(orderExpandDto.getAmount());
                orderMessage.setFv(orderExpandDto.getFv());
                orderMessage.setType(Objects.toString(orderExpandDto.getType(), null));
                orderMessage.setChargeType(orderExpandDto.getChargeType());
                orderMessage.setAutoRenew(Objects.toString(orderExpandDto.getAutoRenew(), null));
                try {
                    String rawRefer = orderExpandDto.getRefer();
                    if (StringUtils.isNotBlank(rawRefer) && rawRefer.contains("giftBatchNo")) {
                        if (rawRefer.contains("\"\"")) {
                            rawRefer = rawRefer.replace("\"\"", "\"");
                        }
                        JSONObject jsonObject = JSON.parseObject(rawRefer);
                        if (jsonObject != null && jsonObject.containsKey("giftBatchNo")) {
                            orderMessage.setGiftBatchNo(jsonObject.getString("giftBatchNo"));
                        }
                    }
                } catch (Exception e) {
                    log.error("Parse refer error. uid:{}, orderCode:{}", orderExpandDto.getUserId(), orderExpandDto.getOrderCode(), e);
                }
                orderMessage.setHistoryPresent(true);
                orderMessage.setNotNeedPresentVipTypeSet(orderExpandDto.getNotNeedPresentVipTypeSet());
                orderMessages.add(orderMessage);
            } catch (Exception e) {
                log.error("Build order message. uid:{}, orderCode:{}", orderExpandDto.getUserId(), orderExpandDto.getOrderCode(), e);
                throw e;
            }

        }
        return orderMessages;
    }

    List<OrderExpandDto> getValidOrders(UserInfo vipUser, Connection orderTidbConnection, Connection vipUserConnection, String threadName) {
        Timestamp current = DateUtils.getCurrentTimestamp();
        log.info("[{}] [checking] [uid:{}]", threadName, vipUser.getUserId());

        vipUser.setPlatinumDiffDay(null);
        vipUser.setGoldDiffDay(null);

        // 查询用户的奇异果白金订单
        List<OrderExpandDto> kiwiPlatinumOrders = orderTidbJdbcTool.selectUserOrderList(orderTidbConnection, vipUser.getUserId());
        if (CollectionUtils.isEmpty(kiwiPlatinumOrders)) {
            packBadUser(vipUser, BadUserEnum.NO_ORDERS);
            return null;
        }

        // 过滤无效订单（已过期、退单）
        List<OrderExpandDto> validOrders = tradeOrderService.trimInvalidOrderMessages(kiwiPlatinumOrders, current, orderTidbConnection);
        if (CollectionUtils.isEmpty(validOrders)) {
            packBadUser(vipUser, BadUserEnum.NO_VALID_ORDERS);
            return null;
        }

        int tvExclusiveAmountByOrders = 0;
        int platinumAmountByOrders = 0;
        for (OrderExpandDto validOrder : validOrders) {
            long start = validOrder.getStartTime().getTime();
            if (start < current.getTime()) {
                start = current.getTime();
                validOrder.setStartTime(current);
            }
            int presentAmount = HistoryUtils.calculateFloorDays(start, validOrder.getDeadline().getTime());
            tvExclusiveAmountByOrders += presentAmount;
            if (!validOrder.getNotNeedPresentVipTypeSet().contains(VipTypeEnum.IQIYI_PLATINUM.getVipType())) {
                platinumAmountByOrders += presentAmount;
            }
        }

        // 判断补赠的权益时长 与 用户剩余白金权益时长 的差异
        Integer tvExclusivePresentTolerantAmount = cloudConfig.getIntProperty("tv_exclusive_present_tolerant_amount", 1);
        int platinumAmountByUser = HistoryUtils.calculateFloorDays(current.getTime(), vipUser.getDeadline().getTime());
        int platinumDiffDay = tvExclusiveAmountByOrders - platinumAmountByUser;
        if (platinumDiffDay > tvExclusivePresentTolerantAmount) {
            vipUser.setPlatinumDiffDay(platinumDiffDay);
            packBadUser(vipUser, BadUserEnum.PRESENT_AMOUNT_GREATER_THAN_PLATINUM);
            log.warn("Present amount is greater than user platinum vip right. uid:{}, platinumAmountByOrders:{}, amountByUser:{}, platinumDiffDay:{}",
                vipUser.getUserId(), tvExclusiveAmountByOrders, platinumAmountByUser, platinumDiffDay);
            List<OrderExpandDto> allocatedOrderList = tradeOrderService.allocateVipRight(validOrders, platinumAmountByUser, current);
            return allocatedOrderList;
        }
        if (platinumDiffDay < tvExclusivePresentTolerantAmount) {
            vipUser.setPlatinumDiffDay(platinumDiffDay);
            packBadUser(vipUser, BadUserEnum.PRESENT_AMOUNT_LESS_THAN_PLATINUM);
            log.warn("Present amount is less than user platinum vip right. uid:{}, platinumAmountByOrders:{}, amountByUser:{}, platinumDiffDay:{}",
                vipUser.getUserId(), tvExclusiveAmountByOrders, platinumAmountByUser, platinumDiffDay);
        }

        // 判断补赠的权益时长 与 用户剩余黄金权益时长 的差异
        Integer platinumPresentTolerantAmount = cloudConfig.getIntProperty("platinum_present_tolerant_amount", 30);
        int goldAmountByUser = 0;
        UserInfo goldUser = vipPresentJdbcTool.selectGoldUser(vipUserConnection, vipUser.getUserId());
        if (goldUser != null) {
            goldAmountByUser = HistoryUtils.calculateFloorDays(current.getTime(), goldUser.getDeadline().getTime());
        }
        int goldDiffDay = platinumAmountByOrders - goldAmountByUser;
        if (goldDiffDay > platinumPresentTolerantAmount) {
            vipUser.setGoldDiffDay(goldDiffDay);
            packBadUser(vipUser, BadUserEnum.PRESENT_AMOUNT_GREATER_THAN_GOLD);
            log.warn("Present amount is greater than user gold vip right. uid:{}, platinumAmountByOrders:{}, amountByUser:{}, goldDiffDay:{}",
                vipUser.getUserId(), platinumAmountByOrders, goldAmountByUser, goldDiffDay);
        }

        return validOrders;
    }

}
