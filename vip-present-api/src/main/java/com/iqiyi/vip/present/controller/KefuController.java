package com.iqiyi.vip.present.controller;

import com.iqiyi.vip.present.apirequest.PageQueryPresentOrderReqVo;
import com.iqiyi.vip.present.apiresponse.Page;
import com.iqiyi.vip.present.apiresponse.PresentOrderVO;
import com.iqiyi.vip.present.model.TradeOrder;
import com.iqiyi.vip.present.service.PresentOrderService;
import com.qiyi.vip.commons.constant.QueryConstants;
import com.qiyi.vip.commons.web.dto.WebResult;
import com.qiyi.vip.trade.dataservice.client.DataServiceClient;
import com.qiyi.vip.trade.dataservice.client.request.QueryOrdersRequest;
import com.qiyi.vip.trade.dataservice.client.response.QueryOrdersResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.Map;

/**
 * 为客服提供查询接口
 *
 * <AUTHOR>
 * @date 2019/7/9 14:42
 */
@Controller
@RequestMapping("/kefu")
@Slf4j
public class KefuController {

    @Autowired
    private PresentOrderService presentOrderService;

    @Autowired
    private DataServiceClient dataServiceClient;

    @ResponseBody
    @RequestMapping("/getPresentOrders")
    public WebResult<Page<PresentOrderVO>> getPresentOrders(PageQueryPresentOrderReqVo pageQueryPresentOrderReqVo) {
        log.info("[step:enter] [params:{}]", pageQueryPresentOrderReqVo);
        if (StringUtils.isEmpty(pageQueryPresentOrderReqVo.getUid())
            && StringUtils.isEmpty(pageQueryPresentOrderReqVo.getOrderCode())) {
            return WebResult.newSuccess(new Page<>());
        }
        if (StringUtils.isEmpty(pageQueryPresentOrderReqVo.getUid())) {
            pageQueryPresentOrderReqVo.setUid(String.valueOf(getUidByOrderCode(pageQueryPresentOrderReqVo.getOrderCode())));
        }

        Page<PresentOrderVO> orderPage = presentOrderService.findCardBatchPage(buildParam(pageQueryPresentOrderReqVo), pageQueryPresentOrderReqVo.getPresentCode());

        return WebResult.newSuccess(orderPage);
    }

    private Long getUidByOrderCode(String orderCodtrade) {
        TradeOrder order = getOrderFromViptrade(orderCodtrade);
        return order.getUserId();
    }

    private Map<String, Object> buildParam(PageQueryPresentOrderReqVo pageQueryPresentOrderReqVo) {
        Map<String, Object> map = new HashMap<>();
        map.put("presentCode", pageQueryPresentOrderReqVo.getPresentCode());
        map.put("uid", Long.valueOf(pageQueryPresentOrderReqVo.getUid()));
        if (StringUtils.isNotEmpty(pageQueryPresentOrderReqVo.getOrderCode())) {
            map.put("orderCode", pageQueryPresentOrderReqVo.getOrderCode());
        }
        map.put("order", pageQueryPresentOrderReqVo.getOrder());
        map.put("sortField", pageQueryPresentOrderReqVo.getOrderBy());
        map.put("pageSize", pageQueryPresentOrderReqVo.getPageSize());
        map.put("start", (pageQueryPresentOrderReqVo.getPageNo() - 1) * pageQueryPresentOrderReqVo.getPageSize());
        return map;
    }

    private TradeOrder getOrderFromViptrade(String orderCode) {
        QueryOrdersRequest ordersQueryRequest = new QueryOrdersRequest();
        ordersQueryRequest.addParam("orderCode", orderCode);
        ordersQueryRequest.addParam(QueryConstants.QUERY_PARAM_LIMIT, String.valueOf(1));
        QueryOrdersResponse response = new QueryOrdersResponse();
        for (int i = 0; i < 2; i++) {
            response = dataServiceClient.execute(ordersQueryRequest);
            if (response.isSuccessful() && !CollectionUtils.isEmpty(response.getData())) {
                break;
            }
        }
        TradeOrder order = new TradeOrder();
        if (!response.isSuccessful() || CollectionUtils.isEmpty(response.getData())) {
            log.error("[SpOrderEventHandler getOrderFromViptrade data empty][orderCode:{}]", orderCode);
            return order;
        }
        if (response.getData().get(0) != null) {
            BeanUtils.copyProperties(response.getData().get(0), order);
            return order;
        }
        return order;
    }

}