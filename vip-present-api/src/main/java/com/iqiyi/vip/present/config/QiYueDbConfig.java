package com.iqiyi.vip.present.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.sql.Connection;
import java.sql.DriverManager;

/**
 * Created at: 2022-04-16
 *
 * <AUTHOR>
 */
@Configuration
public class QiYueDbConfig {

    @Value("${ds.qiyue.jdbc.url}")
    private String url;
    @Value("${ds.qiyue.jdbc.username}")
    private String username;
    @Value("${ds.qiyue.jdbc.password}")
    private String password;

    @Bean(name = "qiyueDbConnection")
    public Connection getConnection() throws Exception {
        Class.forName("com.mysql.jdbc.Driver");
        return DriverManager.getConnection(url, username, password);
    }

}
