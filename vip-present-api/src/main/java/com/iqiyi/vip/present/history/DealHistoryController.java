package com.iqiyi.vip.present.history;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import com.iqiyi.vip.present.apiresponse.BaseResponse;

/**
 * 历史钻石买赠处理，补赠垂线权益
 *
 * <AUTHOR>
 * @date 2020/3/24 10:52
 */
@RestController
@Slf4j
@RequestMapping("/history")
public class DealHistoryController {

    @Resource
    private DealHistoryService dealHistoryService;

    /**
     * 检测比对程序
     */
    @RequestMapping("/check")
    public BaseResponse check(int[] uidSuffix, String dateFlag, String badReason) {
        log.info("check enter. uidSuffix:{}, dateFlag:{}, badReason:{}", uidSuffix, dateFlag, badReason);
        if (ArrayUtils.isEmpty(uidSuffix) && StringUtils.isBlank(badReason)) {
            return BaseResponse.createParamError();
        }
        long start = System.currentTimeMillis();
        dealHistoryService.check(uidSuffix, dateFlag, badReason);
        log.info("check exit. uidSuffix:{}, dateFlag:{}, badReason:{}, cost:{}", uidSuffix, dateFlag, badReason, System.currentTimeMillis() - start);
        return BaseResponse.createSuccess();
    }

    /**
     * 读取check的结果，写入买赠订单，任务触发赠送
     */
    @RequestMapping("/present")
    public BaseResponse present(Integer offset, Integer limit, Integer totalSizeLimit) {
        log.info("Present enter. offset:{}, limit:{}, totalSizeLimit:{}", offset, limit, totalSizeLimit);
        if (offset == null || offset < 0) {
            return BaseResponse.createParamError();
        }
        long start = System.currentTimeMillis();
        dealHistoryService.present(offset, limit, totalSizeLimit);
        log.info("Present exit. offset:{}, limit:{}, totalSizeLimit:{}, cost:{}", offset, limit, totalSizeLimit, System.currentTimeMillis() - start);
        return BaseResponse.createSuccess();
    }

    /**
     * 接口wiki: http://wiki.qiyi.domain/pages/viewpage.action?pageId=1318391279
     *
     * 捞取订单并发送订单完成消息
     */
    @RequestMapping("/prepare/order/present/msg")
    public BaseResponse prepareOrderPresentMsg(PrepareOrderPresentMsgReq req) {
        log.info("Prepare order present msg enter. req:{} ", req);
        if (req.getUidSuffixStart() > req.getUidSuffixEnd()) {
            return BaseResponse.createParamError();
        }
        String result = dealHistoryService.prepareOrderPresentMsg(req);
        log.info("Prepare order present msg exit. req: {}, result:{} ", req, result);
        return BaseResponse.createSuccess(result);
    }

}
