package com.iqiyi.vip.present.history;

import com.google.common.base.Strings;

import java.sql.Timestamp;

import com.qiyi.vip.trade.dataservice.client.dto.OrderDto;
import com.iqiyi.vip.present.consts.StringConstants;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.utils.CalAmountUtils;
import com.iqiyi.vip.present.utils.DateUtils;

/**
 * 工具类
 *
 * <AUTHOR> (<EMAIL>)
 * @date 2020/3/30 13:52
 */
public class HistoryUtils {

    public static int calculateFloorDays(long current, OrderDto orderDto) {
        long start = orderDto.getStartTime().getTime();
        if (current > start) {
            start = current;
        }
        long totalTimeInMillis = orderDto.getDeadline().getTime() - start;
        return CalAmountUtils.calFloorAmountByDayMills(totalTimeInMillis);
    }

    public static int calculateFloorDays(long current, long vipDeadline) {
        long total = vipDeadline - current;
        return CalAmountUtils.calFloorAmountByDayMills(total);
    }

    public static String getFv(OrderDto orderDto) {
        return Strings.isNullOrEmpty(orderDto.getFv()) ? StringConstants.DEAULT_FV : orderDto.getFv();
    }

    /**
     * 1-连包，0-普通
     */
    public static Integer getBuyPidType(OrderDto orderDto) {
        Integer autoRenew = orderDto.getAutoRenew();
        boolean isAutoRenewProduct =
            autoRenew != null && (autoRenew == 1 || autoRenew == 2 || autoRenew == 3);
        return isAutoRenewProduct ? 1 : 0;
    }

    public static String createTradeOrderCode(OrderInfo orderInfo, PresentConfig presentConfig) {
        return orderInfo.getOrderCode() + "_" + presentConfig.getPresentCode();
    }

    public static Timestamp calculateTime(Timestamp start, int incrDay) {
        return DateUtils.calculateTime(start, incrDay + "d");
    }
}
