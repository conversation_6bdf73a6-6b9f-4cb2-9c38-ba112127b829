package com.iqiyi.vip.present.aspect;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Aspect
@Component
@Slf4j
public class LogAspect {
    @Pointcut("@annotation(com.iqiyi.vip.present.annotation.LogAnnotation)")
    public void logPointcut() {

    }

    @Before("logPointcut()")
    @SuppressWarnings("all")
    public void before(JoinPoint joinPoint) {
        Object args = joinPoint.getArgs()[0];
        Signature signature = joinPoint.getSignature();
        String controller = signature.getDeclaringType().getSimpleName();
        String method = signature.getName();
        log.info("enter {}.{},request:{}", controller, method, args);
    }

    @AfterReturning(value = "logPointcut()", returning = "returnVal")
    @SuppressWarnings("all")
    public void after(JoinPoint joinPoint, Object returnVal) {
        Signature signature = joinPoint.getSignature();
        String controller = signature.getDeclaringType().getSimpleName();
        String method = signature.getName();
        String returnValStr = Optional.ofNullable(returnVal).map(JSON::toJSONString).orElse(null);
        log.info("exit {}.{},response:{}", controller, method, returnValStr);
    }

}
