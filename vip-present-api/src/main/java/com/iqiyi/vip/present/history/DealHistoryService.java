package com.iqiyi.vip.present.history;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.vip.present.consts.PresentConstants;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.iqiyi.vip.present.model.PresentCondition;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.service.PresentConditionService;
import com.iqiyi.vip.present.service.PresentConfigService;
import com.iqiyi.vip.present.utils.DateUtils;

/**
 * 具体逻辑
 *
 * <AUTHOR>
 * @date 2020/3/25 23:08
 */
@Slf4j
@Component
public class DealHistoryService {

    private PresentConfig presentConfig = new PresentConfig();
    private Map<Long, PresentCondition> presentConditionMap = Maps.newHashMap();

    private static final int THREAD_DATA_SIZE = 10000;
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Resource
    private VipPresentJdbcTool jdbcTool;
    @Resource
    private PresentConfigService presentConfigService;
    @Resource
    private PresentConditionService presentConditionService;
    @Resource
    private CloudConfig cloudConfig;

    private Future<?> dataRefreshFuture;
    private volatile LocalDateTime jobStartTime;

    /**
     * 提交任务的线程池
     */
    private final ExecutorService submitJobThreadPool = new ThreadPoolExecutor(1, 1, 10, TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(10),
        new ThreadFactoryBuilder().setNameFormat("submit-job-pool-%d").build(),
        new ThreadPoolExecutor.CallerRunsPolicy());

    public String prepareOrderPresentMsg(PrepareOrderPresentMsgReq req) {
        if (dataRefreshFuture != null && !dataRefreshFuture.isDone()) {
            return "Last job is created at: " + FORMATTER.format(jobStartTime);
        }
        jobStartTime = LocalDateTime.now();
        dataRefreshFuture = submitJobThreadPool.submit(new PrepareOrderPresentMsgTask(req));
        return "Prepare msg success";
    }

    public void check(int[] uidSuffix, String dateFlag, String badReason) {
        long start = System.currentTimeMillis();
        ExecutorService threadPoolExecutor = getExecutorService();
        // 获取未过期的钻石会员
        if (StringUtils.isBlank(dateFlag)) {
            dateFlag = DateUtils.getCurrentTimeStr();
        }
        List<UserInfo> vipUsers = jdbcTool.selectUsers(uidSuffix, dateFlag, badReason);
        if (CollectionUtils.isEmpty(vipUsers)) {
            log.error("[empty users] [uidSuffix:{}]", uidSuffix);
            return;
        }
        log.info("[get users done] [count:{}] [cost:{}ms]", vipUsers.size(), (System.currentTimeMillis() - start));

        // 每个线程跑1w的用户
        List<List<UserInfo>> partitions = Lists.partition(vipUsers, THREAD_DATA_SIZE);
        for (List<UserInfo> partition : partitions) {
            threadPoolExecutor.submit(new DealCheckTask(partition));
        }
    }

    public void present(Integer offset, Integer limit, Integer totalSizeLimit) {
        long start = System.currentTimeMillis();
        ExecutorService threadPoolExecutor = getExecutorService();
        int count = 0;
        List<OrderInfo> orderInfoList;
        boolean stopPresent;
        do {
            stopPresent = cloudConfig.getBooleanProperty("stop_present", false);
            orderInfoList = jdbcTool.selectOrdersByPage(offset, limit);
            if (CollectionUtils.isEmpty(orderInfoList)) {
                log.info("Order info list is empty. break present. offset:{}, limit:{}, totalSizeLimit:{}", offset, limit, totalSizeLimit);
                break;
            }
            log.info("[Obtain orders count:{}] [cost:{}ms]", orderInfoList.size(), (System.currentTimeMillis() - start));

            submitTaskWithQPSLimit(threadPoolExecutor, orderInfoList);

            offset += limit;
            count += orderInfoList.size();
        } while (!stopPresent && count < totalSizeLimit);

        long cost = System.currentTimeMillis() - start;
        log.info("Present loop end. count:{}, cost:{}", count, cost);
    }

    private void submitTaskWithQPSLimit(ExecutorService threadPoolExecutor, List<OrderInfo> orderInfoList) {
        Integer diamondPresentQiyuPartitionSize = cloudConfig.getIntProperty("diamond_present_qiyu_partition_size", 500);
        List<List<OrderInfo>> partitions = Lists.partition(orderInfoList, diamondPresentQiyuPartitionSize);
        for (List<OrderInfo> partition : partitions) {
            threadPoolExecutor.submit(new DealPresentTask(partition, presentConfig, presentConditionMap));
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.error("Sleep error", e);
            }
        }
    }

    @PostConstruct
    public void initPresentConfigList() {
        Long diamondPresentQiyuConfigId = cloudConfig.getLongProperty("diamond_present_qiyu_config_id", PresentConstants.PRESENT_CONFIG_ID_DEFAULT);
        if (diamondPresentQiyuConfigId == null || diamondPresentQiyuConfigId <= 0) {
            return;
        }
        presentConfig = presentConfigService.queryConfigById(diamondPresentQiyuConfigId);

        List<PresentCondition> presentConditions = presentConditionService.queryConditionByIds(presentConfig.getConditionIds());
        for (PresentCondition presentCondition : presentConditions) {
            presentConditionMap.put(presentCondition.getId(), presentCondition);
        }
        log.info("[history order present] [config:{}]", JSON.toJSONString(presentConfig));
        log.info("----------------------------------------------------------");
        log.info("[history order present] [condition:{}]", JSON.toJSONString(presentConditionMap));
    }

    private ExecutorService getExecutorService() {
        Integer corePoolSize = cloudConfig.getIntProperty("CORE_POOL_SIZE", 10);
        Integer maxPoolSize = cloudConfig.getIntProperty("MAX_POOL_SIZE", 10);
        Integer keepAlive = cloudConfig.getIntProperty("KEEP_ALIVE_TIME", 60);
        return new ThreadPoolExecutor(corePoolSize, maxPoolSize, keepAlive, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>());
    }

}
