package com.iqiyi.vip.present.aspect;

import com.alibaba.csp.sentinel.adapter.spring.webmvc.SentinelWebInterceptor;
import com.alibaba.csp.sentinel.adapter.spring.webmvc.config.SentinelWebMvcConfig;
import com.alibaba.fastjson.JSON;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/7/2
 * @apiNote
 */
@Configuration
public class SentinelInterceptorConfig extends WebMvcConfigurerAdapter {
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        SentinelWebMvcConfig config = new SentinelWebMvcConfig();
        // Enable the HTTP method prefix.
        config.setHttpMethodSpecify(true);
        config.setBlockExceptionHandler((request, response, e) -> {
            response.setHeader("Content-Type", "application/json;charset=UTF-8");
            PrintWriter out = response.getWriter();
            Map map = new HashMap();
            map.put("code", "Q00449");
            map.put("msg", "被限流了");
            out.print(JSON.toJSONString(map));
            out.flush();
            out.close();
        });
        // Add to the interceptor list.
        registry.addInterceptor(new SentinelWebInterceptor(config)).addPathPatterns("/**");
    }
}
