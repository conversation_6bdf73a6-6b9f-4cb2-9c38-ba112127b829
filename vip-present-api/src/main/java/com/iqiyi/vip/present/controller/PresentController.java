package com.iqiyi.vip.present.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.iqiyi.kiwi.utils.ServletUtils;
import com.iqiyi.vip.present.annotation.LogAnnotation;
import com.iqiyi.vip.present.apirequest.PresentRequest;
import com.iqiyi.vip.present.apirequest.QueryPresentReq;
import com.iqiyi.vip.present.apiresponse.BaseResponse;
import com.iqiyi.vip.present.apiresponse.PresentResponse;
import com.iqiyi.vip.present.consts.EnumOrderTypeCode;
import com.iqiyi.vip.present.consts.EnumResultCode;
import com.iqiyi.vip.present.consts.PresentConstants;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.data.PresentOrderData;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.model.PresentOrderRes;
import com.iqiyi.vip.present.model.SupplyPresentRecord;
import com.iqiyi.vip.present.service.PresentOrderService;
import com.iqiyi.vip.present.service.PresentService;
import com.iqiyi.vip.present.service.SupplyPresentRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * 接口文档：http://wiki.qiyi.domain/pages/viewpage.action?pageId=308741035
 */
@Controller
@RequestMapping("/buypresent")
@Slf4j
public class PresentController {

    @Autowired
    private PresentService presentService;

    @Autowired
    private PresentOrderService presentOrderService;
    @Resource
    private SupplyPresentRecordService supplyPresentRecordService;


    @RequestMapping("qualification")
    @ResponseBody
    @LogAnnotation
    public void qualification(HttpServletRequest servletRequest, HttpServletResponse httpResponse, PresentRequest request) {
        log.info("[qualification][PresentRequest:{}]", JSONObject.toJSONString(request));
        BaseResponse<List<PresentResponse>> response = BaseResponse.createSuccess();
        try {
            commonDealProcess(servletRequest, request, response);
        } catch (Exception e) {
            log.error("[PresentController qualification exception]", e);
            response = BaseResponse.createSystemError();
        }
        returnJsonByResponse(JSON.toJSONString(response), response.getCode(), servletRequest, httpResponse, request);
    }

    private void returnJsonByResponse(String response, String code, HttpServletRequest request, HttpServletResponse httpResponse, PresentRequest presentRequest) {
        String callback = request.getParameter("callback");
        if (callback != null && !callback.equals("null")) {
            response = "try{window." + callback + "({\"code\":\"" + code + "\",\"data\":" + response + "});}catch(e){};";
        }
        log.info("[returnJsonByResponse][presentRequest:{}][returnJsonByResponse:{}]", presentRequest, JSONObject.toJSONString(response));
        //设置headers参数
        String fullContentType = "text/plain;charset=UTF-8";
        httpResponse.setContentType(fullContentType);
        ServletUtils.setNoCacheHeader(httpResponse);
        try {
            httpResponse.getWriter().write(response);
            httpResponse.getWriter().flush();
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    @RequestMapping("get")
    @ResponseBody
    @LogAnnotation
    public void get(HttpServletRequest servletRequest, HttpServletResponse httpResponse, PresentRequest request) {
        log.info("[get][PresentRequest:{}]", JSONObject.toJSONString(request));
        BaseResponse<List<PresentResponse>> response = BaseResponse.createSuccess();
        try {
            List<PresentOrderData> orderDataList = commonDealProcess(servletRequest, request, response);
            if (!BaseResponse.isSuccess(response.getCode())) {
                returnJsonByResponse(JSON.toJSONString(response), response.getCode(), servletRequest, httpResponse, request);
                return;

            }
            presentService.presentVip(request, response, orderDataList);
        } catch (DuplicateKeyException e) {
            log.error("[PresentController get DuplicateKeyException]", e);
            response.setResultCode(EnumResultCode.NOT_ALL_SUCCESS);
        } catch (Exception e) {
            log.error("[PresentController get Exception]", e);
            response.setResultCode(EnumResultCode.SYSTEM_ERROR);
        }
        returnJsonByResponse(JSON.toJSONString(response), response.getCode(), servletRequest, httpResponse, request);
    }

    @RequestMapping("isAlreadyGet")
    @ResponseBody
    @LogAnnotation
    public void isAlreadyGet(HttpServletRequest servletRequest, HttpServletResponse httpResponse, PresentRequest request) {
        log.info("[isAlreadyGet][PresentRequest:{}]", JSONObject.toJSONString(request));
        BaseResponse response = BaseResponse.createSuccess();
        try {
            validateParamAndGetUid(servletRequest, request, response);
            if (!BaseResponse.isSuccess(response.getCode())) {
                returnJsonByResponse(JSON.toJSONString(response), response.getCode(), servletRequest, httpResponse, request);
                return;
            }
            presentService.judgeReceiveBuyPresent(request, response);
        } catch (Exception e) {
            log.error("[PresentController isAlreadyGet Exception]", e);
        }
        returnJsonByResponse(JSON.toJSONString(response), response.getCode(), servletRequest, httpResponse, request);
    }


    @RequestMapping("bcpcheck")
    @ResponseBody
    public BaseResponse bcpCheck(String message) {
        BaseResponse response = BaseResponse.createSuccess();
        if (StringUtils.isEmpty(message)) {
            return response;
        }
        OrderMessage orderMessage = JSONObject.parseObject(message, OrderMessage.class);
        Integer status = orderMessage.getStatus();

        if (PresentConstants.PAID_STATUS.equals(status)) {
            List<Long> needPresent = presentService.checkOrderNeedPresent(orderMessage);
            if (CollectionUtils.isEmpty(needPresent)) {
                return response;
            }
            for (Long id : needPresent) {
                PresentOrder presentOrder = new PresentOrder();
                presentOrder.setOrderCode(orderMessage.getOrderCode());
                presentOrder.setUid(orderMessage.getUid());
                presentOrder.setPresentConditionId(id);
                List<PresentOrder> presentOrders = presentOrderService.queryOrderByParams(presentOrder);
                if (CollectionUtils.isEmpty(presentOrders)) {
                    return BaseResponse.createParamError();
                }
            }
        }

        return response;
    }

    private List<PresentOrderData> commonDealProcess(HttpServletRequest servletRequest, PresentRequest request,
                                                     BaseResponse<List<PresentResponse>> response) {
        validateParamAndGetUid(servletRequest, request, response);
        if (!BaseResponse.isSuccess(response.getCode())) {
            return Lists.newArrayList();
        }
        List<PresentOrderData> orderDataList = presentService.queryVip(request, response);
        return orderDataList;
    }

    private void validateParamAndGetUid(HttpServletRequest servletRequest, PresentRequest request, BaseResponse response) {
        //验证入参
        presentService.validateParam(request, response);
        if (!BaseResponse.isSuccess(response.getCode())) {
            return;
        }
        //找到uid
        Long uid = presentService.getUid(servletRequest, response, request);
        if (uid == null) {
            response.setResultCode(EnumResultCode.NO_USER);
            return;
        }
        request.setUid(uid);
    }

    /**
     * 权益转增 黄金单查询对应基础会员
     *
     * @param queryPresentReq
     * @return
     */
    @RequestMapping("/getOrderCode")
    @ResponseBody
    public BaseResponse queryPresentOrderCode(QueryPresentReq queryPresentReq) {
        log.info("queryPresentOrderCode req:{}", queryPresentReq);
        PresentOrder presentOrderRequest = new PresentOrder();
        presentOrderRequest.setOrderCode(queryPresentReq.getOrderCode());
        presentOrderRequest.setUid(queryPresentReq.getUid());
        presentOrderRequest.setOrderType(EnumOrderTypeCode.YES.getCode());
        List<PresentOrder> presentOrderList = presentOrderService.queryOrderByParams(presentOrderRequest);
        log.info("queryOrderByParams result:{}", presentOrderList);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(presentOrderList)) {
            String orderCode = presentOrderList.stream().filter(t -> Objects.equals(String.valueOf(PresentConstants.BASIC_VIP_TYPE), t.getPresentType())).findFirst().get().getOrderCode();
            return BaseResponse.createSuccess(PresentOrderRes.builder().orderCode(orderCode).build());
        }

        //查询补增
        SupplyPresentRecord supplyPresentRecord = SupplyPresentRecord.builder().uid(queryPresentReq.getUid()).build();
        List<SupplyPresentRecord> supplyPresentRecords = supplyPresentRecordService.selectByUid(supplyPresentRecord);
        log.info("supplyPresentRecord select result:{}", supplyPresentRecords);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(supplyPresentRecords)) {
            SupplyPresentRecord supplyPresentRecordVo = supplyPresentRecords.stream().filter(t -> Objects.equals(t.getSource(), queryPresentReq.getSource())
                    && org.apache.commons.lang3.StringUtils.isNotBlank(t.getOrderCode())).findFirst().orElse(null);
            return BaseResponse.createSuccess(supplyPresentRecordVo != null ? PresentOrderRes.builder().orderCode(supplyPresentRecordVo.getOrderCode()).build() : null);
        }
        log.info("queryPresentOrderCode result:{}", queryPresentReq);
        return BaseResponse.createSuccess();
    }

}
