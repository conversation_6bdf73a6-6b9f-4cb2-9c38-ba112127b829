package com.iqiyi.vip.present.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

import com.qiyi.vip.commons.web.dto.WebResult;
import com.iqiyi.vip.present.apirequest.ManualPresentReq;
import com.iqiyi.vip.present.service.ManualPresentService;

/**
 * @Author: <PERSON>
 * @Date: 2021/9/30
 */
@Controller
@RequestMapping("/manual")
@Slf4j
public class ManualController {

    @Resource
    private ManualPresentService manualPresentService;

    @ResponseBody
    @RequestMapping("/present")
    public WebResult<String> present(ManualPresentReq manualPresentReq) {
        log.info("[Manual present] enter. param: {}", manualPresentReq);
        manualPresentService.manualPresent(manualPresentReq);
        return WebResult.newSuccess("success");
    }

}
