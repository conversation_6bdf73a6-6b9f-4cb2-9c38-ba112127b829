package com.iqiyi.vip.present.history;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import com.iqiyi.vip.present.service.PresentProductService;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.util.List;

import com.qiyi.vip.trade.dataservice.client.dto.OrderDto;

import javax.annotation.Resource;


/**
 * 与数据库交互
 *
 * <AUTHOR> (<EMAIL>)
 * @date 2020/3/25 22:45
 */
@Slf4j
@Component
public class VipPresentJdbcTool {

    @Value("${history.db.url}")
    private String url;
    @Value("${history.db.username}")
    private String username;
    @Value("${history.db.password}")
    private String password;

    @Resource
    private PresentProductService presentProductService;

    public List<UserInfo> selectByUidWhiteList(Connection connection, List<Long> uidWhiteList, String dateFlag, String badReason, Integer doneFlag, Integer limit) {
        List<UserInfo> vipUsers = Lists.newArrayList();

        StringBuilder sqlBuilder = new StringBuilder(1024);
        sqlBuilder.append("select user_id, deadline, bad_reason from history_vip_user where done_flag = ")
            .append(doneFlag)
            .append(" and deadline >= \'")
            .append(dateFlag).append("\'");
        if (CollectionUtils.isNotEmpty(uidWhiteList)) {
            sqlBuilder.append(" and user_id in (");
            for (long uid : uidWhiteList) {
                sqlBuilder.append(uid).append(",");
            }
            sqlBuilder.deleteCharAt(sqlBuilder.lastIndexOf(",")).append(")");
        }
        if (StringUtils.isNotBlank(badReason)) {
            sqlBuilder.append(" and bad_reason = \'").append(badReason).append("\'");
        }
        sqlBuilder.append(" limit ").append(limit);

        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        try {
            preparedStatement = connection.prepareStatement(sqlBuilder.toString());
            resultSet = preparedStatement.executeQuery();
            while (resultSet.next()) {
                UserInfo vipUser = new UserInfo();
                vipUser.setUserId(resultSet.getLong("user_id"));
                vipUser.setDeadline(resultSet.getTimestamp("deadline"));
                vipUser.setBadReason(resultSet.getString("bad_reason"));
                vipUsers.add(vipUser);
            }
        } catch (Exception e) {
            log.error("execute sql failed", e);
        } finally {
            close(preparedStatement, resultSet);
        }
        return vipUsers;
    }

    public List<UserInfo> selectUsers(Connection connection, List<Integer> uidSuffixList, String dateFlag, String badReason, Integer doneFlag, Integer limit) {
        List<UserInfo> vipUsers = Lists.newArrayList();

        StringBuilder sqlBuilder = new StringBuilder(1024);
        sqlBuilder.append("select user_id, deadline, bad_reason from history_vip_user where done_flag = ")
            .append(doneFlag)
            .append(" and deadline >= \'")
            .append(dateFlag).append("\'");
        if (CollectionUtils.isNotEmpty(uidSuffixList)) {
            sqlBuilder.append(" and suffix in (");
            for (int suffix : uidSuffixList) {
                sqlBuilder.append(suffix).append(",");
            }
            sqlBuilder.deleteCharAt(sqlBuilder.lastIndexOf(",")).append(")");
        }
        if (StringUtils.isNotBlank(badReason)) {
            sqlBuilder.append(" and bad_reason = \'").append(badReason).append("\'");
        }
        sqlBuilder.append(" limit ").append(limit);

        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        try {
            preparedStatement = connection.prepareStatement(sqlBuilder.toString());
            resultSet = preparedStatement.executeQuery();
            while (resultSet.next()) {
                UserInfo vipUser = new UserInfo();
                vipUser.setUserId(resultSet.getLong("user_id"));
                vipUser.setDeadline(resultSet.getTimestamp("deadline"));
                vipUser.setBadReason(resultSet.getString("bad_reason"));
                vipUsers.add(vipUser);
            }
        } catch (Exception e) {
            log.error("execute sql failed. uidList:{}", uidSuffixList, e);
        } finally {
            close(preparedStatement, resultSet);
        }
        return vipUsers;
    }

    public void updateUser(Connection connection, UserInfo user) {
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        try {
            connection.setAutoCommit(false);
            preparedStatement = connection.prepareStatement(
                "UPDATE history_vip_user SET present_time = ?, bad_reason = ?, platinum_diff_day = ?, gold_diff_day = ?, "
                    + " done_flag = 1 WHERE user_id = ?");

            preparedStatement.setTimestamp(1, user.getPresentTime());
            preparedStatement.setString(2, user.getBadReason());
            if (user.getPlatinumDiffDay() == null) {
                preparedStatement.setNull(3, Types.INTEGER);
            } else {
                preparedStatement.setInt(3, user.getPlatinumDiffDay());
            }
            if (user.getGoldDiffDay() == null) {
                preparedStatement.setNull(4, Types.INTEGER);
            } else {
                preparedStatement.setInt(4, user.getGoldDiffDay());
            }
            preparedStatement.setLong(5, user.getUserId());
            preparedStatement.executeUpdate();
            connection.commit();
        } catch (Exception e) {
            try {
                connection.rollback();
            } catch (SQLException e1) {
                log.error("connection rollback error. uid:{}", user.getUserId(), e1);
            }
            log.error("Update user error. uid:{}", user.getUserId(), e);
        } finally {
            close(preparedStatement, resultSet);
        }
    }

    public UserInfo selectGoldUser(Connection connection, Long uid) {
        List<UserInfo> vipUsers = Lists.newArrayList();

        StringBuilder sqlBuilder = new StringBuilder(1024);
        sqlBuilder.append("select user_id, deadline from history_vip_gold_user where user_id = ").append(uid);

        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        try {
            preparedStatement = connection.prepareStatement(sqlBuilder.toString());
            resultSet = preparedStatement.executeQuery();
            while (resultSet.next()) {
                UserInfo vipUser = new UserInfo();
                vipUser.setUserId(resultSet.getLong("user_id"));
                vipUser.setDeadline(resultSet.getTimestamp("deadline"));
                vipUsers.add(vipUser);
            }
        } catch (Exception e) {
            log.error("execute sql failed. uid:{}", uid, e);
        } finally {
            close(preparedStatement, resultSet);
        }
        if (CollectionUtils.isEmpty(vipUsers)) {
            return null;
        }
        return vipUsers.get(0);
    }

    public List<UserInfo> selectUsers(int[] uidSuffix, String dateFlag, String badReason) {
        List<UserInfo> vipUsers = Lists.newArrayList();

        StringBuilder sqlBuilder = new StringBuilder(1024);
        sqlBuilder.append("select user_id, deadline from history_vip_user where status = 1 and present_time is null and deadline >= \'")
            .append(dateFlag)
            .append("\'");
        if (ArrayUtils.isNotEmpty(uidSuffix)) {
            sqlBuilder.append(" and suffix in (");
            for (int suffix : uidSuffix) {
                sqlBuilder.append(suffix).append(",");
            }
            sqlBuilder.deleteCharAt(sqlBuilder.lastIndexOf(",")).append(")");
        }
        if (StringUtils.isNotBlank(badReason)) {
            sqlBuilder.append(" and bad_reason = \'").append(badReason).append("\'");
        }
        // local test
//        sqlBuilder.append(" limit 100");

        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        try {
            connection = open();
            preparedStatement = connection.prepareStatement(sqlBuilder.toString());
            resultSet = preparedStatement.executeQuery();
            while (resultSet.next()) {
                UserInfo vipUser = new UserInfo();
                vipUser.setUserId(resultSet.getLong("user_id"));
                vipUser.setDeadline(resultSet.getTimestamp("deadline"));
                vipUsers.add(vipUser);
            }
        } catch (Exception e) {
            log.error("Select users failed. ", e);
        } finally {
            close(connection, preparedStatement, resultSet);
        }
        return vipUsers;
    }

    public void updateUsersAndOrders(List<UserInfo> users, List<OrderDto> orders) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        try {
            connection = open();
            connection.setAutoCommit(false);
            preparedStatement = connection.prepareStatement("UPDATE history_vip_user SET present_time = ?, bad_reason = ? WHERE user_id = ?");
            for (UserInfo user : users) {
                preparedStatement.setTimestamp(1, user.getPresentTime());
                preparedStatement.setString(2, user.getBadReason());
                preparedStatement.setLong(3, user.getUserId());
                preparedStatement.executeUpdate();
            }
            if (CollectionUtils.isNotEmpty(orders)) {
                for (OrderDto orderDto : orders) {
                    preparedStatement = connection.prepareStatement("INSERT INTO `order_info` ("
                        + "`user_id`,"
                        + "`order_code`,"
                        + "`present_amount`,"
                        + "`start_time`,"
                        + "`end_time`,"
                        + "`pay_time`,"
                        + "`fv`,"
                        + "`buy_pid`,"
                        + "`buy_pid_amt`,"
                        + "`buy_pid_type`,"
                        + "`charge_type`"
                        + ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    preparedStatement.setLong(1, orderDto.getUserId());
                    preparedStatement.setString(2, orderDto.getOrderCode());
                    // 从暂存字段中获取赠送天数
                    preparedStatement.setInt(3, orderDto.getCenterPayService());
                    preparedStatement.setTimestamp(4, orderDto.getStartTime());
                    preparedStatement.setTimestamp(5, orderDto.getDeadline());
                    preparedStatement.setTimestamp(6, orderDto.getPayTime());
                    preparedStatement.setString(7, HistoryUtils.getFv(orderDto));
                    preparedStatement.setString(8, getProductCode(orderDto.getProductId()));
                    preparedStatement.setInt(9, orderDto.getAmount());
                    preparedStatement.setInt(10, HistoryUtils.getBuyPidType(orderDto));
                    preparedStatement.setInt(11, orderDto.getRealFee() > 0 ? 1 : 0);
                    preparedStatement.executeUpdate();
                }
            }
            connection.commit();
        } catch (Exception e) {
            try {
                connection.rollback();
            } catch (SQLException e1) {
                log.error("", e1);
            }
            log.error("", e);
        } finally {
            close(connection, preparedStatement, resultSet);
        }
    }

    public List<OrderInfo> selectOrdersByPage(int offset, int size) {
        List<OrderInfo> orders = Lists.newArrayList();
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        try {
            connection = open();
            preparedStatement = connection.prepareStatement("select * from order_info where done_flag = 0 limit ?, ?");
            preparedStatement.setInt(1, offset);
            preparedStatement.setInt(2, size);
            resultSet = preparedStatement.executeQuery();
            while (resultSet.next()) {
                OrderInfo orderInfo = new OrderInfo();
                orderInfo.setOrderCode(resultSet.getString("order_code"));
                orderInfo.setUserId(resultSet.getLong("user_id"));
                orderInfo.setPresentAmount(resultSet.getInt("present_amount"));
                orderInfo.setStartTime(resultSet.getTimestamp("start_time"));
                orderInfo.setEndTime(resultSet.getTimestamp("end_time"));
                orderInfo.setPayTime(resultSet.getTimestamp("pay_time"));
                orderInfo.setFv(resultSet.getString("fv"));
                orderInfo.setBuyPid(resultSet.getString("buy_pid"));
                orderInfo.setBuyPidAmt(resultSet.getInt("buy_pid_amt"));
                orderInfo.setBuyPidType(resultSet.getInt("buy_pid_type"));
                orderInfo.setChargeType(resultSet.getInt("charge_type"));
                orders.add(orderInfo);
            }
        } catch (Exception e) {
            log.error("", e);
        } finally {
            close(connection, preparedStatement, resultSet);
        }
        return orders;
    }

    public void updateOrders(List<OrderInfo> orderInfoList) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        try {
            connection = open();
            connection.setAutoCommit(false);
            preparedStatement = connection.prepareStatement("UPDATE order_info SET done_flag = 1 WHERE order_code = ?");
            for (OrderInfo orderInfo : orderInfoList) {
                preparedStatement.setString(1, orderInfo.getOrderCode());
                preparedStatement.executeUpdate();
            }
            connection.commit();
        } catch (Exception e) {
            log.error("", e);
        } finally {
            close(connection, preparedStatement, resultSet);
        }
    }

    private Connection open() throws Exception {
        Class.forName("com.mysql.jdbc.Driver");
        return DriverManager.getConnection(url, username, password);
    }

    private void close(Connection connection, PreparedStatement preparedStatement, ResultSet resultSet) {
        try {
            if (resultSet != null) {
                resultSet.close();
            }
            if (preparedStatement != null) {
                preparedStatement.close();
            }
            if (connection != null) {
                connection.close();
            }
        } catch (SQLException e) {
            log.error("", e);
        }
    }

    private void close(PreparedStatement preparedStatement, ResultSet resultSet) {
        try {
            if (resultSet != null) {
                resultSet.close();
            }
            if (preparedStatement != null) {
                preparedStatement.close();
            }
        } catch (SQLException e) {
            log.error("Close connection failed. ", e);
        }
    }

    private String getProductCode(Long productId) {
        String productCode = presentProductService.getCodeById(productId);
        return Strings.nullToEmpty(productCode);
    }

}
