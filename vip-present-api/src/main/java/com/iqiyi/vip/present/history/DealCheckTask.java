package com.iqiyi.vip.present.history;

import com.google.common.collect.Lists;
import com.iqiyi.solar.config.client.CloudConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.sql.*;
import java.util.List;

import com.qiyi.vip.trade.dataservice.client.dto.OrderDto;
import com.iqiyi.vip.present.utils.ApplicationContextUtil;
import com.iqiyi.vip.present.utils.DateUtils;

/**
 * 线程执行类
 *
 * <AUTHOR> (<EMAIL>)
 * @date 2020/3/25 23:14
 */
@Slf4j
public class DealCheckTask implements Runnable {


    private List<UserInfo> users;

    public DealCheckTask(List<UserInfo> users) {
        this.users = users;
    }

    @Override
    public void run() {
        String threadName = Thread.currentThread().getName();
        log.info("[{}] [starting]", threadName);
        long start = System.currentTimeMillis();
        Connection connection = null;
        try {
            OrderTidbJdbcTool orderTidbJdbcTool = (OrderTidbJdbcTool) ApplicationContextUtil.getBean(OrderTidbJdbcTool.class);
            TradeOrderService tradeOrderService = (TradeOrderService) ApplicationContextUtil.getBean(TradeOrderService.class);
            CloudConfig cloudConfig = (CloudConfig) ApplicationContextUtil.getBean(CloudConfig.class);
            Integer faultTolerantPresentAmount = cloudConfig.getIntProperty("fault_tolerant_present_amount", 1);
            // 待创建的买赠订单
            List<OrderDto> toPresentOrders = Lists.newArrayList();
            connection = getConnection();
            for (UserInfo vipUser : users) {
                try {
                    Timestamp current = DateUtils.getCurrentTimestamp();
                    log.info("[{}] [checking] [uid:{}]", threadName, vipUser.getUserId());
                    // 查询uid的钻石订单
                    List<OrderExpandDto> diamondOrders = orderTidbJdbcTool.selectUserOrderList(connection, vipUser.getUserId());
                    if (CollectionUtils.isEmpty(diamondOrders)) {
                        packBadUser(vipUser, BadUserEnum.NO_ORDERS);
                        continue;
                    }

                    // 过滤无效订单（已过期、退单）
                    List<OrderDto> validOrders = tradeOrderService.trimInvalidOrders(diamondOrders, current);
                    if (CollectionUtils.isEmpty(validOrders)) {
                        packBadUser(vipUser, BadUserEnum.NO_VALID_ORDERS);
                        continue;
                    }

                    // 计算每个订单应该补赠的权益，到天即可
                    int presentAmountByOrders = 0;
                    for (OrderDto validOrder : validOrders) {
                        // 计算赠送天数，暂存到centerPayService
                        int presentAmount = HistoryUtils.calculateFloorDays(current.getTime(), validOrder);
                        validOrder.setCenterPayService(presentAmount);
                        presentAmountByOrders += presentAmount;
                    }
                    int amountByUser = HistoryUtils.calculateFloorDays(current.getTime(), vipUser.getDeadline().getTime());
                    if (presentAmountByOrders - amountByUser > faultTolerantPresentAmount) {
                        packBadUser(vipUser, BadUserEnum.PRESENT_AMOUNT_EXCESS);
                        log.warn("Present amount is large than user vip right. uid:{}, presentAmountByOrders:{}, amountByUser:{}",
                                vipUser.getUserId(), presentAmountByOrders, amountByUser);
                        continue;
                    }

                    // 校验通过
                    log.info("[{}] [pass checked] [uid:{}] [validOrders:{}]", threadName, vipUser.getUserId(), validOrders.size());
                    vipUser.setPresentTime(current);
                    toPresentOrders.addAll(validOrders);
                } catch (Exception e) {
                    log.error("[{}] [uid:{}] [exception]", threadName, vipUser.getUserId(), e);
                }
            }

            // 输出对比结果
            outputResult(users, toPresentOrders);
            log.info("[{}] [result] [toPresentOrders:{}]", threadName, toPresentOrders.size());
        } catch (Exception e) {
            log.error("[{}] [exception]", threadName, e);
        } finally {
            close(connection);
        }

        log.info("[{}] [done] [cost:{}ms]", threadName, (System.currentTimeMillis() - start));
    }

    private void packBadUser(UserInfo badUser, BadUserEnum badUserEnum) {
        log.warn("[bad user] [uid:{}] [reason:{}]", badUser.getUserId(), badUserEnum.getReason());
        badUser.setBadReason(badUserEnum.getReason());
    }

    private void outputResult(List<UserInfo> users, List<OrderDto> toPresentOrders) {
        VipPresentJdbcTool jdbcTool = (VipPresentJdbcTool) ApplicationContextUtil.getBean(VipPresentJdbcTool.class);
        jdbcTool.updateUsersAndOrders(users, toPresentOrders);
    }

    private Connection getConnection() throws Exception {
        String url = ApplicationContextUtil.getEnvProperty("order.tidb.jdbc.url");
        String username = ApplicationContextUtil.getEnvProperty("order.tidb.jdbc.username");
        String password = ApplicationContextUtil.getEnvProperty("order.tidb.jdbc.password");

        Class.forName("com.mysql.jdbc.Driver");
        return DriverManager.getConnection(url, username, password);
    }

    private void close(Connection connection) {
        try {
            if (connection != null) {
                connection.close();
            }
        } catch (SQLException e) {
            log.error("Close connection error", e);
        }
    }

}
