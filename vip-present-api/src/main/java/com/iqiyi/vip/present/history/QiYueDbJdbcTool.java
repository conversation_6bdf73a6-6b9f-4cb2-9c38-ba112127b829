package com.iqiyi.vip.present.history;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import com.iqiyi.vip.present.config.GuavaCacheConfig;

/**
 * Created at: 2022-04-16
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@CacheConfig(cacheManager = GuavaCacheConfig.GUAVA_CACHE_MANAGER)
public class QiYueDbJdbcTool {

    @Resource
    private Connection qiyueDbConnection;

    @Cacheable(value = "getPlatformCodeById", keyGenerator = "customKeyGenerator")
    public String getPlatformCodeById(Long platformId) {
        return getCodeById("select code from qiyue_platform where id = ?", platformId);
    }

    @Cacheable(value = "getProductCodeById", keyGenerator = "customKeyGenerator")
    public String getProductCodeById(Long productId) {
        return getCodeById("select code from qiyue_product_new where id = ?", productId);
    }

    private String getCodeById(String sql, Long id) {
        String platformCode = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        try {
            preparedStatement = qiyueDbConnection.prepareStatement(sql);
            preparedStatement.setLong(1, id);
            resultSet = preparedStatement.executeQuery();
            while (resultSet.next()) {
                platformCode = resultSet.getString("code");
            }
        } catch (Exception e) {
            log.error("execute sql failed", e);
        } finally {
            close(preparedStatement, resultSet);
        }
        return platformCode;
    }

    private void close(PreparedStatement preparedStatement, ResultSet resultSet) {
        try {
            if (resultSet != null) {
                resultSet.close();
            }
            if (preparedStatement != null) {
                preparedStatement.close();
            }
        } catch (SQLException e) {
            log.error("", e);
        }
    }

}
