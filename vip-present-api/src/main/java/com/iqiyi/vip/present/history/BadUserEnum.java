package com.iqiyi.vip.present.history;

import lombok.Getter;

/**
 * 无效用户
 *
 * <AUTHOR> (x<PERSON><EMAIL>)
 * @date 2020/3/25 22:54
 */
@Getter
public enum BadUserEnum {

    NO_ORDERS("NO_ORDERS", "无订单"),
    NO_VALID_ORDERS("NO_VALID_ORDERS", "无有效订单"),
    PRESENT_AMOUNT_EXCESS("PRESENT_AMOUNT_EXCESS", "赠送数量过多"),
    PRESENT_AMOUNT_GREATER_THAN_PLATINUM("PRESENT_AMOUNT_GREATER_THAN_PLATINUM", "赠送的数量大于白金权益到期时间"),
    PRESENT_AMOUNT_LESS_THAN_PLATINUM("PRESENT_AMOUNT_LESS_THAN_PLATINUM", "赠送的数量小于白金权益到期时间"),
    PRESENT_AMOUNT_GREATER_THAN_GOLD("PRESENT_AMOUNT_GREATER_THAN_GOLD", "赠送的数量大于黄金权益到期时间"),
    RETRY_SUCCESS("RETRY_SUCCESS", "重试成功");
    private String reason;
    private String desc;

    BadUserEnum(String reason, String desc) {
        this.reason = reason;
        this.desc = desc;
    }

}
