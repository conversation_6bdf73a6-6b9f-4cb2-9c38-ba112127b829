package com.iqiyi.vip.present.history;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.present.consts.*;
import com.iqiyi.vip.present.model.PresentCondition;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.out.PresentVipRequest;
import com.iqiyi.vip.present.service.PresentOrderService;
import com.iqiyi.vip.present.task.PresentVipAsyncTask;
import com.iqiyi.vip.present.utils.ApplicationContextUtil;
import com.iqiyi.vip.present.utils.DateUtils;
import com.iqiyi.vip.present.utils.UUidUtils;

/**
 * 处理赠送task
 *
 * <AUTHOR> (<EMAIL>)
 * @date 2020/4/1 9:22
 */
@Slf4j
public class DealPresentTask implements Runnable {

    private List<OrderInfo> orderInfoList;

    private List<PresentConfig> presentConfigList;

    private PresentConfig presentConfig;

    private Map<Long, PresentCondition> presentConditionMap;

    private PresentOrderService presentOrderService = (PresentOrderService) ApplicationContextUtil.getBean(PresentOrderService.class);
    private VipPresentJdbcTool jdbcTool = (VipPresentJdbcTool) ApplicationContextUtil.getBean(VipPresentJdbcTool.class);

    public DealPresentTask(List<OrderInfo> orderInfoList, PresentConfig presentConfig, Map<Long, PresentCondition> presentConditionMap) {
        this.orderInfoList = orderInfoList;
        this.presentConfig = presentConfig;
        this.presentConditionMap = presentConditionMap;
    }

    @Override
    public void run() {
        try {
            String threadName = Thread.currentThread().getName();
            long start = System.currentTimeMillis();
            List<PresentVipAsyncTask.TaskData> taskDataList = Lists.newArrayList();

            log.info("[{}] [start] [to present count:{}]", threadName, orderInfoList.size());
            for (OrderInfo orderInfo : orderInfoList) {
                try {
                    // 已赠送忽略
                    if (hasPresentOrders(orderInfo, presentConfig)) {
                        log.info("[{}] [has presented] [orderCode:{}] [configId:{}]", threadName, orderInfo.getOrderCode(), presentConfig
                                .getId());
                        continue;
                    }
                    taskDataList.add(packTaskData(orderInfo, presentConfig));
                } catch (Exception e) {
                    log.error("Deal present task error. orderCode:{}, uid:{}", orderInfo.getOrderCode(), orderInfo.getUserId(), e);
                }
            }

//            if (CollectionUtils.isNotEmpty(taskDataList)) {
//                List<List<PresentVipAsyncTask.TaskData>> partition = Lists.partition(taskDataList, 1000);
//                for (List<PresentVipAsyncTask.TaskData> list : partition) {
//                    presentOrderService.saveOrderPresentAndTask(list);
//                    log.info("[{}] [present partition count:{}] [cost:{}ms]", threadName, list.size(), (
//                        System.currentTimeMillis() - start));
//                    Thread.sleep(1000);
//                }
//            }

            presentOrderService.saveOrderPresentAndTask(taskDataList);
            log.info("[{}] [save orders and tasks] [cost:{}]", threadName, (System.currentTimeMillis() - start));
            jdbcTool.updateOrders(orderInfoList);
            log.info("[{}] [done] [present count:{}] [cost:{}ms]", threadName, taskDataList.size(), (System.currentTimeMillis() - start));
        } catch (Exception e) {
            log.error("", e);
        }
    }

    public boolean hasPresentOrders(OrderInfo orderInfo, PresentConfig presentConfig) {
        PresentOrder presentOrder = new PresentOrder();
        presentOrder.setUid(orderInfo.getUserId());
        presentOrder.setOrderCode(orderInfo.getOrderCode());
        presentOrder.setPresentConfigId(presentConfig.getId());
        List<PresentOrder> presentOrders = presentOrderService.queryOrderByParams(presentOrder);
        return CollectionUtils.isNotEmpty(presentOrders);
    }

    public PresentVipAsyncTask.TaskData packTaskData(OrderInfo orderInfo, PresentConfig presentConfig) {
        PresentVipAsyncTask.TaskData taskData = new PresentVipAsyncTask.TaskData();
        taskData.setRequest(packPresentVipRequest(orderInfo, presentConfig));
        taskData.setPresentOrder(packPresentOrder(orderInfo, presentConfig));
        return taskData;
    }

    public PresentVipRequest packPresentVipRequest(OrderInfo orderInfo, PresentConfig presentConfig) {
        PresentVipRequest request = new PresentVipRequest();
        request.setUid(orderInfo.getUserId());
        request.setPid(presentConfig.getPresentCode());
        request.setAmount(orderInfo.getPresentAmount());
        request.setActCode(PresentConstants.ACTCODE);
        request.setTradeCode(HistoryUtils.createTradeOrderCode(orderInfo, presentConfig));
        request.setPayType(presentConfig.getPayType());
        request.setOriginalOrderCode(orderInfo.getOrderCode());
        request.setFv(Strings.isNullOrEmpty(orderInfo.getFv()) ? StringConstants.DEAULT_FV : orderInfo.getFv());
        if (!Strings.isNullOrEmpty(orderInfo.getPlatform())) {
            request.setPlatform(orderInfo.getPlatform());
        }
        request.setBuyPid(orderInfo.getBuyPid());
        request.setBuyPidAmt(orderInfo.getBuyPidAmt());
        request.setBuyPidType(orderInfo.getBuyPidType());
        request.setChargeType(orderInfo.getChargeType());
        return request;
    }

    public PresentOrder packPresentOrder(OrderInfo orderInfo, PresentConfig presentConfig) {
        PresentOrder presentOrder = new PresentOrder();
        presentOrder.setMsgId(UUidUtils.getUUid());
        presentOrder.setOrderCode(orderInfo.getOrderCode());
        presentOrder.setPresentTradeCode(HistoryUtils.createTradeOrderCode(orderInfo, presentConfig));
        presentOrder.setBuyType(String.valueOf(VipTypeEnum.diamonds.getVipType()));
        presentOrder.setPresentType(presentConfig.getPvipType());
        presentOrder.setProductAmount(orderInfo.getPresentAmount());
        presentOrder.setStatus(EnumOrderStatusCode.NO_PRESENT.getCode());
        presentOrder.setDeadlineStartTime(orderInfo.getStartTime());
        presentOrder.setDeadlineEndTime(orderInfo.getEndTime());
        presentOrder.setPayTime(orderInfo.getPayTime());
        Date currentDate = DateUtils.getCurrentTime();
        presentOrder.setReceiveTime(currentDate);
        presentOrder.setUpdateTime(currentDate);
        presentOrder.setCreateTime(currentDate);
        presentOrder.setUid(orderInfo.getUserId());
        presentOrder.setOrderType(EnumOrderTypeCode.YES.getCode());
        presentOrder.setFv(orderInfo.getFv());
        presentOrder.setPresentConfigId(presentConfig.getId());
        Long presentConditionId = Long.valueOf(presentConfig.getConditionIds());
        presentOrder.setPresentConditionId(presentConditionId);
        presentOrder.setPresentPerformId(presentConditionMap.get(presentConditionId).getPerformId());
        return presentOrder;
    }
}
