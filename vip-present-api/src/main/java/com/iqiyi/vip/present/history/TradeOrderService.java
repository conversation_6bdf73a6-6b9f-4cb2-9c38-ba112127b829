package com.iqiyi.vip.present.history;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;

import com.qiyi.vip.commons.enums.OrderStatusEnum;
import com.qiyi.vip.trade.dataservice.client.dto.OrderDto;
import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.vip.present.consts.VipTypeEnum;

/**
 * 交易订单交互逻辑
 *
 * <AUTHOR> (<EMAIL>)
 * @date 2020/3/26 0:31
 */
@Slf4j
@Component
public class TradeOrderService {

    @Resource
    private CloudConfig cloudConfig;
    @Resource
    OrderTidbJdbcTool orderTidbJdbcTool;

    /**
     * 过滤掉到期的、已退的订单、对外合作订单中非指定的合作方；找到赠送单对应的实付订单
     */
    public List<OrderExpandDto> trimInvalidOrderMessages(List<OrderExpandDto> orderExpandDtoList, Timestamp current, Connection connection) {
        // 正单
        List<OrderExpandDto> paidOrders = Lists.newArrayList();
        // 负单
        List<OrderExpandDto> refundOrders = Lists.newArrayList();
        // 按状态分类
        for (OrderExpandDto order : orderExpandDtoList) {
            if (order.getStatus().equals(OrderStatusEnum.ORDER_STATUS_DELIVERED.getStatus())) {
                paidOrders.add(order);
            } else {
                refundOrders.add(order);
            }
        }

        // 从正单中移除负单
        if (CollectionUtils.isNotEmpty(refundOrders)) {
            for (OrderDto refundOrder : refundOrders) {
                for (OrderDto paidOrder : paidOrders) {
                    // 正单的订单号为负单的交易号
                    if (refundOrder.getTradeCode().equals(paidOrder.getOrderCode())) {
                        paidOrders.remove(paidOrder);
                        break;
                    }
                }
            }
        }

        // 再从正单中去除过期的
        paidOrders.removeIf(paidOrder -> paidOrder.getDeadline() == null || paidOrder.getDeadline().before(current));

        String[] partnerArray = cloudConfig.getArrayProperty("history_possible_present_gold_partners", ",", new String[]{});
        Set<String> partnerSet = Sets.newHashSet(partnerArray);
        List<OrderExpandDto> toRemovedOrderList = Lists.newArrayList();
        List<OrderExpandDto> toAddedOrderList = Lists.newArrayList();
        for (OrderExpandDto order : paidOrders) {

            //找到用户真实付费的那笔订单
            String boughtOrderCode = "";
            OrderExpandDto boughtOrderDto = null;
            if (StringUtils.isNotBlank(order.getTradeCode()) && order.getTradeCode().contains("_")) {
                List<String> tradeCodeSplitList = Lists.newArrayList(Splitter.on("_").split(order.getTradeCode()));
                if (NumberUtils.isNumber(tradeCodeSplitList.get(0)) && tradeCodeSplitList.get(0).length() >= 21) {
                    boughtOrderCode = tradeCodeSplitList.get(0);
                }
            }
            if (StringUtils.isNotBlank(boughtOrderCode)) {
                boughtOrderDto = orderTidbJdbcTool.selectByOrderCode(connection, boughtOrderCode);
            }

            //对外合作非赠送单&&非激活码订单
            if (boughtOrderDto == null && StringUtils.isNotBlank(order.getPartner()) && order.getPayType() != 6) {
                boolean notNeedPresentIQiyiPlatinum = true;
                if (partnerSet.contains(order.getPartner())) {
                    String tradeCode = order.getOrderCode() + "_a47bac390c51df6a";
                    OrderExpandDto presentOrderDto = orderTidbJdbcTool.selectByTradeCode(connection, tradeCode);
                    if (presentOrderDto != null) {
                        notNeedPresentIQiyiPlatinum = false;
                    }
                }
                if (notNeedPresentIQiyiPlatinum) {
                    order.getNotNeedPresentVipTypeSet().add(VipTypeEnum.IQIYI_PLATINUM.getVipType());
                }
            }

            //激活码非赠送单
            if (boughtOrderDto == null && order.getPayType() == 6) {
                String tradeCode = order.getOrderCode() + "_a47bac390c51df6a";
                OrderExpandDto presentOrderDto = orderTidbJdbcTool.selectByTradeCode(connection, tradeCode);
                if (presentOrderDto == null) {
                    order.getNotNeedPresentVipTypeSet().add(VipTypeEnum.IQIYI_PLATINUM.getVipType());
                }
            }
            //不补赠爱奇艺白金的场景：支付方式为免费批次赠送；奇异果黄金会员月卡_不送爱奇艺黄金权益 的pid
            if (order.getPayType() == 305 || order.getProductId() == 1720705) {
                order.getNotNeedPresentVipTypeSet().add(VipTypeEnum.IQIYI_PLATINUM.getVipType());
            }

            //赠送单，需要找到买的那笔订单
            if (boughtOrderDto != null) {
                //设置权益时间，对齐补赠的权益
                boughtOrderDto.setStartTime(order.getStartTime());
                boughtOrderDto.setDeadline(order.getDeadline());
                //设置是否赠送的标识
                boughtOrderDto.setNotNeedPresentVipTypeSet(order.getNotNeedPresentVipTypeSet());
                toRemovedOrderList.add(order);
                toAddedOrderList.add(boughtOrderDto);
            }
        }

        if (CollectionUtils.isNotEmpty(toRemovedOrderList)) {
            paidOrders.removeAll(toRemovedOrderList);
        }
        if (CollectionUtils.isNotEmpty(toAddedOrderList)) {
            paidOrders.addAll(toAddedOrderList);
        }

        return paidOrders;
    }

    /**
     * 过滤掉到期的、已退的订单
     */
    public List<OrderDto> trimInvalidOrders(List<OrderExpandDto> diamondOrders, Timestamp current) {
        // 正单
        List<OrderDto> paidOrders = Lists.newArrayList();
        // 负单
        List<OrderDto> refundOrders = Lists.newArrayList();

        // 按状态分类
        for (OrderDto diamondOrder : diamondOrders) {
            if (diamondOrder.getStatus().equals(OrderStatusEnum.ORDER_STATUS_DELIVERED.getStatus())) {
                paidOrders.add(diamondOrder);
            } else {
                refundOrders.add(diamondOrder);
            }
        }

        List<OrderDto> toRemove = Lists.newArrayList();

        // 从正单中移除负单
        if (CollectionUtils.isNotEmpty(refundOrders)) {
            for (OrderDto refundOrder : refundOrders) {
                for (OrderDto paidOrder : paidOrders) {
                    // 正单的订单号为负单的交易号
                    if (refundOrder.getTradeCode().equals(paidOrder.getOrderCode())) {
                        toRemove.add(paidOrder);
                        break;
                    }
                }
            }
            paidOrders.removeAll(toRemove);
        }

        toRemove.clear();

        // 再从正单中去除过期的
        for (OrderDto paidOrder : paidOrders) {
            if (paidOrder.getDeadline() == null || paidOrder.getDeadline().before(current)) {
                toRemove.add(paidOrder);
            }
        }
        paidOrders.removeAll(toRemove);

        return paidOrders;
    }

    public List<OrderExpandDto> allocateVipRight(List<OrderExpandDto> orderExpandDtoList, int userPlatinumDay, Timestamp current) {
        List<OrderExpandDto> allocatedOrderList = new ArrayList<>();
        int userRemainDay = userPlatinumDay;
        //排序
        orderExpandDtoList.sort((o1, o2) -> o2.getStartTime().compareTo(o1.getStartTime()));

        for (OrderExpandDto order : orderExpandDtoList) {
            if (order.getStartTime().before(current)) {
                order.setStartTime(current);
            }
            int orderPlatinumDay = HistoryUtils.calculateFloorDays(order.getStartTime().getTime(), order.getDeadline().getTime());
            if (orderPlatinumDay > userRemainDay) {
                Timestamp deadline = HistoryUtils.calculateTime(current, userRemainDay);
                order.setDeadline(deadline);
                order.setStartTime(current);
                allocatedOrderList.add(order);
                break;
            }
            allocatedOrderList.add(order);
            userRemainDay -= orderPlatinumDay;
        }
        return allocatedOrderList;
    }

}
