<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">

    <springProperty scope="context" name="loggingPath" source="logging.path"
                    defaultValue="/data/logs/vip-present-api"/>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <Pattern>%highlight([%-5p]) [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%tid] [%t] [%C{1}:%M:%L] %m%n</Pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="ERROR_DAILY_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${loggingPath}/error.log</file>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%tid] [%t] [%C{1}:%M:%L] %m%n</pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${loggingPath}/error.log.%d{yyyy-MM-dd_HH}</fileNamePattern>
            <!--日志文件保留天数，fileNamePattern按小时归档 -->
            <!--因为fileNamePattern中时间正则最小单位是H（小时）所以maxHistory的单元是小时-->
            <maxHistory>360</maxHistory>
            <!--保留15天的历史记录，但最多10GB-->
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    <appender name="ASYNC_ERROR_DAILY_ROLLING_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <includeCallerData>true</includeCallerData>
        <neverBlock>true</neverBlock>
        <appender-ref ref="ERROR_DAILY_ROLLING_FILE"/>
    </appender>

    <appender name="DAILY_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${loggingPath}/api.log</file>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%tid] [%t] [%C{1}:%M:%L] %m%n</pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${loggingPath}/api.log.%d{yyyy-MM-dd_HH}</fileNamePattern>
            <!--日志文件保留天数，fileNamePattern按小时归档 -->
            <!--因为fileNamePattern中时间正则最小单位是H（小时）所以maxHistory的单元是小时-->
            <maxHistory>360</maxHistory>
            <!--保留15天的历史记录，但最多10GB-->
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    <appender name="ASYNC_DAILY_ROLLING_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <includeCallerData>true</includeCallerData>
        <neverBlock>true</neverBlock>
        <appender-ref ref="DAILY_ROLLING_FILE"/>
    </appender>


    <appender name="INFO_ASYNC_TASK_DAILY_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${loggingPath}/async_task.log</file>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%tid] [%t] [%C{1}:%M:%L] %m%n</pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${loggingPath}/async_task.log.%d{yyyy-MM-dd_HH}</fileNamePattern>
            <!--日志文件保留天数，fileNamePattern按小时归档 -->
            <!--因为fileNamePattern中时间正则最小单位是H（小时）所以maxHistory的单元是小时-->
            <maxHistory>360</maxHistory>
            <!--保留15天的历史记录，但最多10GB-->
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    <appender name="DAILY_APP_ACCESS_LOG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${loggingPath}/httptrace.log</file>
        <encoder>
            <pattern>[%d{yyyy-MM-dd HH:mm:ss}] %m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${loggingPath}/httptrace.log.%d{yyyy-MM-dd_HH}</fileNamePattern>
            <!--日志文件保留天数，fileNamePattern按小时归档 -->
            <!--因为fileNamePattern中时间正则最小单位是H（小时）所以maxHistory的单元是小时-->
            <maxHistory>360</maxHistory>
            <!--保留15天的历史记录，但最多10GB-->
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="APP_ACCESS_LOG_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <!--<discardingThreshold >0</discardingThreshold>-->
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>640</queueSize>
        <includeCallerData>true</includeCallerData>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="DAILY_APP_ACCESS_LOG_FILE"/>
    </appender>

    <springProfile name="bj,zjy,zyx,pre,i18n">
        <logger name="APP_ACCESS_LOG" level="INFO" additivity="false">
            <appender-ref ref="APP_ACCESS_LOG_FILE"/>
        </logger>
        <root level="INFO">
            <appender-ref ref="ASYNC_DAILY_ROLLING_FILE"/>
            <appender-ref ref="ASYNC_ERROR_DAILY_ROLLING_FILE"/>
        </root>
        <logger name="async_task" level="INFO" additivity="false">
            <appender-ref ref="INFO_ASYNC_TASK_DAILY_ROLLING_FILE"/>
        </logger>
    </springProfile>

    <springProfile name="dev,test">
        <logger name="APP_ACCESS_LOG" level="INFO" additivity="false">
            <appender-ref ref="APP_ACCESS_LOG_FILE"/>
        </logger>
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_DAILY_ROLLING_FILE"/>
            <appender-ref ref="ASYNC_ERROR_DAILY_ROLLING_FILE"/>
        </root>
        <logger name="async_task" level="INFO" additivity="false">
            <appender-ref ref="INFO_ASYNC_TASK_DAILY_ROLLING_FILE"/>
        </logger>
    </springProfile>
</configuration>