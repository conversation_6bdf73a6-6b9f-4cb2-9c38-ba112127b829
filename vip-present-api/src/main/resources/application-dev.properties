# \u547D\u540D\u683C\u5F0F\uFF1A\u5E94\u7528\u540D-test
spring.application.name=vip-present-api-test
server.port=8080

eureka.instance.ip-address=${spring.cloud.client.ipAddress}
eureka.instance.prefer-ip-address=true
eureka.instance.instance-id=${eureka.instance.ip-address}:${server.port}
# \u79DF\u671F\u66F4\u65B0\u65F6\u95F4\u95F4\u9694\uFF08\u9ED8\u8BA430\u79D2\uFF09
eureka.instance.lease-renewal-interval-in-seconds=5
# \u79DF\u671F\u5230\u671F\u65F6\u95F4\uFF08\u9ED8\u8BA490\u79D2\uFF09
eureka.instance.lease-expiration-duration-in-seconds=10
# \u662F\u5426\u6CE8\u518C\u5230\u6CE8\u518C\u4E2D\u5FC3\uFF0C\u5982\u679C\u4E0D\u9700\u8981\u53EF\u4EE5\u8BBE\u7F6E\u4E3Afalse
eureka.client.register-with-eureka=true
# \u6CE8\u518C\u4E2D\u5FC3\u914D\u7F6E
eureka.client.serviceUrl.defaultZone=http://test-eureka.vip.qiyi.domain:8080/eureka/
# \u5F00\u542F\u5065\u5EB7\u68C0\u67E5\uFF08\u9700\u8981spring-boot-starter-actuator\u4F9D\u8D56\uFF09
eureka.client.healthcheck.enabled=true
spring.cloud.netflix.metrics.enabled=false

# actuator config
endpoints.health.sensitive=false
endpoints.enabled=false
management.endpoints.web.exposure.include=health,info
management.endpoints.web.exposure.exclude=
management.security.enabled=true
management.health.defaults.enabled=true
management.health.mail.enabled=false
management.health.redis.enabled=false
management.health.eureka.enabled=false
management.endpoint.health.show-details=always
management.info.git.mode=full

#\u6570\u636E\u5E93\u914D\u7F6E
spring.shardingsphere.datasource.names=ds
spring.shardingsphere.datasource.ds.type=com.zaxxer.hikari.HikariDataSource
spring.shardingsphere.datasource.ds.driver-class-name=com.mysql.jdbc.Driver
spring.shardingsphere.datasource.ds.jdbc-url=*******************************************************************************************
spring.shardingsphere.datasource.ds.username=vip_test
spring.shardingsphere.datasource.ds.password=rg_z_6UF)w=Y
spring.shardingsphere.datasource.ds.connection-test-query=select NOW()
spring.shardingsphere.datasource.ds.connection-timeout=3000
spring.shardingsphere.datasource.ds.idle-timeout=60000
spring.shardingsphere.datasource.ds.max-lifetime=18000

spring.shardingsphere.sharding.default-data-source-name=ds

spring.shardingsphere.sharding.tables.present_order.actual-data-nodes=ds.present_order_$->{0..19}
spring.shardingsphere.sharding.tables.present_order.table-strategy.standard.sharding-column=uid
spring.shardingsphere.sharding.tables.present_order.table-strategy.standard.precise-algorithm-class-name=com.iqiyi.vip.present.sharding.HashPreciseShardingAlgorithm

spring.shardingsphere.sharding.tables.present_record.actual-data-nodes=ds.present_record_$->{0..19}
spring.shardingsphere.sharding.tables.present_record.table-strategy.standard.sharding-column=buy_uid
spring.shardingsphere.sharding.tables.present_record.table-strategy.standard.precise-algorithm-class-name=com.iqiyi.vip.present.sharding.HashPreciseShardingAlgorithm

spring.shardingsphere.sharding.tables.supply_present_record.actual-data-nodes=ds.supply_present_record_${def tmp=[];(0..99).each {e->tmp.add(String.format("%02d",e))};return tmp}
spring.shardingsphere.sharding.tables.supply_present_record.table-strategy.standard.sharding-column=uid
spring.shardingsphere.sharding.tables.supply_present_record.table-strategy.standard.precise-algorithm-class-name=com.iqiyi.vip.present.sharding.HashPreciseShardingAlgorithm

#huiyuan_order db
ds.qiyue.jdbc.url=***************************************************************************************************************************************
ds.qiyue.jdbc.username=vip_test
ds.qiyue.jdbc.password=rg_z_6UF)w=Y

mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
internal.pay.key=123456
dopay.url=http://i.vip.qiyi.domain/api/internal/free-pay/dopay.action
vinfo.get.url=http://vinfo.vip.qiyi.domain/internal/vip_users
#vipTradeSdkSignKey=989c12e2b1772218b29ba025248eaf94
vipTradeSdkSignKey=b78dc854afc05d8062558b0bc22141be
tradeapiServerUrl=http://tradeapi.vip.qiyi.domain

#history.db.url=****************************************************************************************************************************************************************************
#history.db.username=vip_operation
#history.db.password=Vip_operation

history.db.url=*******************************************************************************************
history.db.username=vip_test
history.db.password=rg_z_6UF)w=Y

order.tidb.jdbc.url=jdbc:mysql://************:4000/viptrade_order_user?useUnicode=true&characterEncoding=UTF-8&characterSetResults=UTF-8&autoReconnect=true&useAffectedRows=true&useServerPrepStmts=false&useLocalSessionState=true&connectTimeout=30000&socketTimeout=10000
order.tidb.jdbc.username=viptrade_order_user
order.tidb.jdbc.password=+7D/8NslxTTO

# passport
passport.user.info=http://passport.qiyi.domain/apis/profile/info.action
passport.url.byUid=http://passport.qiyi.domain/apis/plaintext/byUid.action

# passport
#passport.user.info=http://intl-passport.qiyi.domain/intl/user/info.action
#passport.url.byUid=http://intl-passport.qiyi.domain/intl/inner/user/byUid.action

appEnv=dev
appName=vip-present
appRegion=default

#\u4E70\u8D60\u5206\u8868\u4E2A\u6570
table.present.order.total.count=20

#exist gitv user need present order producer rmq
exist.gitv.need.present.order.producer.address=dc-resource-fb597f31-10.qiyi.virtual:9876;dc-resource-fb597f31-8.qiyi.virtual:9876
exist.gitv.need.present.order.producer.groupname=PG-viptrade_exist_gitv_need_present_order
exist.gitv.need.present.order.producer.token=PT-553edf80-b36c-4efd-b9fe-5b98ea4240ce

spring.redisson.clusterServers=redis://viptradetest.1.qiyi.redis:7502,redis://viptradetest.2.qiyi.redis:7502
spring.redisson.password=ip61PDObJw63

# order
order.sharding.database.urls=*****************************************************************************,*****************************************************************************
order.sharding.database.username=vip_order_test
order.sharding.database.password=agh3!schingood5TR$
order.sharding.tableSize=2