# \u547D\u540D\u683C\u5F0F\uFF1A\u5E94\u7528\u540D-online
spring.application.name=vip-present-api-online
server.port=8080

eureka.instance.hostname=${HOST}
eureka.instance.non-secure-port=${PORT_8080}
eureka.instance.instance-id=${eureka.instance.hostname}:${eureka.instance.non-secure-port}

# \u5E94\u7528\u6240\u5728zone\uFF0C\u6839\u636E\u5E94\u7528\u673A\u623F\u586B\u5199
eureka.instance.metadata-map.zone=zone-pre
# \u79DF\u671F\u66F4\u65B0\u65F6\u95F4\u95F4\u9694\uFF08\u9ED8\u8BA430\u79D2\uFF09
eureka.instance.lease-renewal-interval-in-seconds=5
# \u79DF\u671F\u5230\u671F\u65F6\u95F4\uFF08\u9ED8\u8BA490\u79D2\uFF09
eureka.instance.lease-expiration-duration-in-seconds=15
# \u662F\u5426\u6CE8\u518C\u5230\u6CE8\u518C\u4E2D\u5FC3\uFF0C\u5982\u679C\u4E0D\u9700\u8981\u53EF\u4EE5\u8BBE\u7F6E\u4E3Afalse
eureka.client.register-with-eureka=true
# \u5E94\u7528\u6240\u5728region\uFF0C\u5317\u4EAC\u3001\u4E2D\u7ECF\u4E91\u3001\u4E2D\u4E91\u4FE1\u673A\u623F\u586B\u5199region-bj\uFF0C\u534E\u4E2D\u673A\u623F\u586B\u5199region-hz\uFF0C\u6D77\u5916\u673A\u623F\u586B\u5199region-sea
eureka.client.region=region-bj
# region\u5185\u53EF\u7528zone\u5217\u8868
eureka.client.availability-zones.region-bj=zone-pre
# \u6CE8\u518C\u4E2D\u5FC3\u914D\u7F6E\uFF0Cregion-bj\u4F7F\u7528\u5982\u4E0B\u914D\u7F6E
eureka.client.service-url.zone-zyx=http://pre-eureka.vip.qiyi.domain:8080/eureka

# \u5F00\u542F\u5065\u5EB7\u68C0\u67E5\uFF08\u9700\u8981spring-boot-starter-actuator\u4F9D\u8D56\uFF09
eureka.client.healthcheck.enabled=true
spring.cloud.netflix.metrics.enabled=false

# actuator config
endpoints.health.sensitive=false
endpoints.enabled=true
management.endpoints.web.exposure.include=health,info
management.endpoints.web.exposure.exclude=
management.security.enabled=true
management.health.defaults.enabled=true
management.health.mail.enabled=false
management.health.redis.enabled=false
management.health.eureka.enabled=false
management.endpoint.health.show-details=simple


spring.shardingsphere.datasource.names=ds
spring.shardingsphere.datasource.ds.type=com.zaxxer.hikari.HikariDataSource
spring.shardingsphere.datasource.ds.driver-class-name=com.mysql.jdbc.Driver
spring.shardingsphere.datasource.ds.jdbc-url=***************************************************************************************************************************************
spring.shardingsphere.datasource.ds.username=vip_present
spring.shardingsphere.datasource.ds.password=B@7fcd296b631
spring.shardingsphere.datasource.ds.connection-test-query=select NOW()
spring.shardingsphere.datasource.ds.connection-timeout=3000
spring.shardingsphere.datasource.ds.idle-timeout=60000
spring.shardingsphere.datasource.ds.max-lifetime=18000

spring.shardingsphere.sharding.default-data-source-name=ds
spring.shardingsphere.sharding.tables.supply_present_record.actual-data-nodes=ds.supply_present_record_${def tmp=[];(0..99).each {e->tmp.add(String.format("%02d",e))};return tmp}
spring.shardingsphere.sharding.tables.supply_present_record.table-strategy.standard.sharding-column=uid
spring.shardingsphere.sharding.tables.supply_present_record.table-strategy.standard.precise-algorithm-class-name=com.iqiyi.vip.present.sharding.HashPreciseShardingAlgorithm

spring.shardingsphere.sharding.tables.present_order.actual-data-nodes=ds.present_order_$->{0..99}
spring.shardingsphere.sharding.tables.present_order.table-strategy.standard.sharding-column=uid
spring.shardingsphere.sharding.tables.present_order.table-strategy.standard.precise-algorithm-class-name=com.iqiyi.vip.present.sharding.HashPreciseShardingAlgorithm

spring.shardingsphere.sharding.tables.present_record.actual-data-nodes=ds.present_record_$->{0..99}
spring.shardingsphere.sharding.tables.present_record.table-strategy.standard.sharding-column=buy_uid
spring.shardingsphere.sharding.tables.present_record.table-strategy.standard.precise-algorithm-class-name=com.iqiyi.vip.present.sharding.HashPreciseShardingAlgorithm

#huiyuan_order db
ds.qiyue.jdbc.url=*******************************************************************************************************************************************
ds.qiyue.jdbc.username=vip_trade_order
ds.qiyue.jdbc.password=qptg798%^$#*/QTP=!390.

dopay.url=http://VIPTRADE-FREE-ORDER/api/internal/free-pay/dopay.action
dopay.url.lb=true
internal.pay.key=1df3ec6fa2
vinfo.get.url=http://vinfo.vip.qiyi.domain/internal/vip_users
vipTradeSdkSignKey=b78dc854afc05d8062558b0bc22141be1
tradeapiServerUrl=http://tradeapi.vip.qiyi.domain

history.db.url=***************************************************************************************************************************************
history.db.username=vip_present
history.db.password=B@7fcd296b631

order.tidb.jdbc.url=*******************************************************************************************************************************************************************************************************************************************************
order.tidb.jdbc.username=huiyuan_order_user
order.tidb.jdbc.password=ZYwGZIpfptBlmdgu

# passport \uFFFD\uFFFD\u057E\uFFFD\u04FF\uFFFD
passport.user.info=http://passport.qiyi.domain/apis/profile/info.action
passport.url.byUid=http://passport.qiyi.domain/apis/plaintext/byUid.action

appEnv=pro
appName=vip-present
appRegion=default

#\u4F1A\u5458\u76D1\u63A7\u9E70\u773C
endpoints.prometheus.sensitive=false
management.context-path=/actuator
management.port=8099

table.present.order.total.count=100

#exist gitv user need present order producer rmq
exist.gitv.need.present.order.producer.address=vip-trade-rocketmq-online001-bdwg.qiyi.virtual:9876;vip-trade-rocketmq-online001-bjdxt9.qiyi.virtual:9876
exist.gitv.need.present.order.producer.groupname=PG-viptrade_exist_gitv_need_present_order
exist.gitv.need.present.order.producer.token=PT-f0539c66-2969-4c5f-84da-4707c0e9de14

spring.redisson.clusterServers=redis://viprightscentercache.1.qiyi.redis:8750,redis://viprightscentercache.2.qiyi.redis:8750
spring.redisson.password=3mjxH4N5MN9f

# order
order.sharding.database.urls=*************************************************************************,*************************************************************************,*************************************************************************,*************************************************************************,*************************************************************************,*************************************************************************,*************************************************************************,*************************************************************************,*************************************************************************,**************************************************************************,**************************************************************************,**************************************************************************,**************************************************************************,**************************************************************************,**************************************************************************,**************************************************************************
order.sharding.database.username=vip_order_pro
order.sharding.database.password=agujmw59!chinagooD8b%
order.sharding.tableSize=64