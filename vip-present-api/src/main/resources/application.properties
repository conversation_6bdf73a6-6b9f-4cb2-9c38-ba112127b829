mybatis.mapper-locations=classpath:/mybatis-mapper/*Mapper.xml
mybatis.type-aliases-package=com.iqiyi.vip.present.model
consumer.appname=vip-present-api
hikari.minimum-idle=10
hikari.maximum-pool-size=50
hikari.idle-timeout=30000
hikari.max-lifetime=1200000
hikari.connection-timeout=30000
hikari.connection-test-query=SELECT 1
hikari.validation-timeout=300000

############# task cluster config start ###############
async.task.execute=false
async.task.execute.cluster=false
async.task.table.name=async_task
async.task.save=true
async.task.failureTable.name=async_failure_task
############# task cluster config end #################
management.endpoints.enabled-by-default=false

spring.main.allow-bean-definition-overriding=true

# Redisson?????
spring.redisson.masterConnectionPoolSize=64
spring.redisson.slaveConnectionPoolSize=64
spring.redisson.masterConnectionMinimumIdleSize=10
spring.redisson.slaveConnectionMinimumIdleSize=10
spring.redisson.connectTimeout=10000
spring.redisson.timeout=3000
spring.redisson.retryAttempts=3
spring.redisson.retryInterval=1500
spring.redisson.pingConnectionInterval=30000
spring.redisson.idleConnectionTimeout=10000
