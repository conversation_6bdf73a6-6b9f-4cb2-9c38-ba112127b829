package com.iqiyi.vip.present;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Set;

import com.iqiyi.vip.present.utils.CloudConfigUtil;

/**
 * @Author: <PERSON>
 * @Date: 2022/4/28
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = VipPresentApplication.class)
public class CloudConfigUtilTest {

    static {
        System.setProperty("spring.profiles.active", "dev");
    }

    @Test
    public void testGetEffectiveNotNeedPresentPayTypeSet() {
        Set<Integer> payTypeSet = CloudConfigUtil.getEffectiveNotNeedPresentPayTypeSet();
        Assert.assertTrue(payTypeSet.size() > 0);
    }
}
