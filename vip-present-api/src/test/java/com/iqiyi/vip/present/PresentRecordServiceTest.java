package com.iqiyi.vip.present;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.present.model.PresentRecord;
import com.iqiyi.vip.present.service.PresentRecordService;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * @Author: <PERSON>
 * @Date: 2022/1/27
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class PresentRecordServiceTest {
    @Resource
    private PresentRecordService presentRecordService;

    static {
        System.setProperty("spring.profiles.active", "dev");
    }

    @Test
    public void testQueryRecordByParams() {
        PresentRecord record = new PresentRecord();
        record.setBuyUid(1474020332L);
        record.setBuyType("5");
        record.setPresentType("1");
        List<PresentRecord> presentRecords = presentRecordService.queryRecordByParams(record);
        assertNotNull(presentRecords);
        assertEquals(presentRecords.size(), 1);
    }
}
