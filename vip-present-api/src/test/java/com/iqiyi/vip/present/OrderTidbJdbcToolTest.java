package com.iqiyi.vip.present;

import com.iqiyi.vip.present.VipPresentApplication;
import com.iqiyi.vip.present.history.OrderTidbJdbcTool;
import com.iqiyi.vip.present.utils.ApplicationContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * @Author: <PERSON>
 * @Date: 2020/11/12
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = VipPresentApplication.class)
@ActiveProfiles("dev")
public class OrderTidbJdbcToolTest {
    @Resource
    private OrderTidbJdbcTool orderTidbJdbcTool;

    @Test
    public void testSelectUserOrderList() {
        Connection connection = null;
        try {
            connection = getConnection();
            orderTidbJdbcTool.selectUserOrderList(connection, 2391038306L);
        } catch (Exception ex) {
            log.error("Select user order list error", ex);
        } finally {
            close(connection);
        }

    }

    private Connection getConnection() throws Exception {
        String url = ApplicationContextUtil.getEnvProperty("order.tidb.jdbc.url");
        String username = ApplicationContextUtil.getEnvProperty("order.tidb.jdbc.username");
        String password = ApplicationContextUtil.getEnvProperty("order.tidb.jdbc.password");

        Class.forName("com.mysql.jdbc.Driver");
        return DriverManager.getConnection(url, username, password);
    }

    private void close(Connection connection) {
        try {
            if (connection != null) {
                connection.close();
            }
        } catch (SQLException e) {
            log.error("", e);
        }
    }

}
