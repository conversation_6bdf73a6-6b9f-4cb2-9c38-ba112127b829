package com.iqiyi.vip.present;

import com.iqiyi.vip.present.VipPresentApplication;
import com.iqiyi.vip.present.api.PassportApi;
import com.qiyi.vip.commons.web.context.UserInfo;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import javax.validation.constraints.AssertTrue;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = VipPresentApplication.class)
public class PassportApiTest {


    static {
        System.setProperty("spring.profiles.active", "dev");
    }

    @Resource
    private PassportApi passportApi;

    @Test
    public void testGetUid() {
        Long id = 1595584331L;
        String P00001 = "4aNrOVTiA0TfhaCOjzm1QuKunhBjgjQ4EA9kCzbtHm1bQu74sz5MuWXRlsMt6dgsOEGm106";
        Assert.assertEquals(id,passportApi.getUid(P00001));
    }

    @Test
    public void getByUid() {
        Long id = 1595584331L;
        String phone = "11100004450";
        UserInfo user = passportApi.getByUid(id);
        Assert.assertTrue(phone.equals(user.getPhone()));
    }
}