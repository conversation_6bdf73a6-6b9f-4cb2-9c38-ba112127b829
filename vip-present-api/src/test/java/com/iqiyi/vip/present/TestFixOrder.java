package com.iqiyi.vip.present;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.iqiyi.kiwi.utils.DateHelper;
import com.iqiyi.vip.present.consts.EnumOrderStatusCode;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.service.PresentOrderService;
import com.iqiyi.vip.present.utils.DateUtils;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = VipPresentApplication.class)
@Slf4j
public class TestFixOrder {

    @Autowired
    private PresentOrderService presentOrderService;

    @Test
    public void orderFix() throws Exception {
        String resultPath = "D:\\diff.txt";
        FileUtils.deleteQuietly(new File(resultPath));
        List<String> lines = FileUtils.readLines(
            new File("D:\\order_deadline_qiyue_order.txt"), "UTF-8");
        List<String> resultLines = Lists.newCopyOnWriteArrayList();
        int nThreads = 10;
        ExecutorService excetorService = new ThreadPoolExecutor(nThreads, nThreads,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>());

        for (String line : lines) {
            excetorService.submit(new Runnable() {
                @Override
                public void run() {
                    try {
                        dealLine(line, resultLines);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });
        }
        excetorService.shutdown();
        excetorService.awaitTermination(2, TimeUnit.DAYS);
        FileUtils.writeLines(new File(resultPath), resultLines);
    }

    private void dealLine(String line, List<String> resultLines) throws Exception {
        String[] datas = line.split(", ");
        String uid = datas[0];
        String orderCode = datas[1];
        String rightStart = datas[4];
        String rightEnd = datas[5];
        PresentOrder presentOrderRequest = new PresentOrder();
        presentOrderRequest.setUid(Long.parseLong(uid));
        presentOrderRequest.setOrderCode(orderCode);
        presentOrderRequest.setStatus(EnumOrderStatusCode.ALREDY_PRESENT.getCode());
        List<PresentOrder> orderList = presentOrderService.queryOrderByParams(presentOrderRequest);
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        if (orderList.size() > 1) {
            throw new Exception("orderCode" + orderCode + " not unique");
        }
        PresentOrder presentOrder = orderList.get(0);
        presentOrder.getProductAmount();

        Integer baseAmount = DateUtils.getDayInterval(
            DateHelper.getDateFromStr(rightStart, "yyyy-MM-dd HH:mm:ss"),
            DateHelper.getDateFromStr(rightEnd, "yyyy-MM-dd HH:mm:ss"));

        if (!presentOrder.getProductAmount().equals(baseAmount)) {
            String presentType = "";
            if ("1".equals(presentOrder.getPresentType())) {
                presentType = "黄金";
            } else if ("16".equals(presentOrder.getPresentType())) {
                presentType = "学生";
            }

            resultLines.add(
                "订单号: " + orderCode + ", userId: " + uid + ", 赠送类型: " + presentType + ", 已送: "
                    + presentOrder.getProductAmount() + "天, 实际应送: "
                    + baseAmount + "天");
        }
    }

    @Test
    public void orderGroup() throws Exception {
        List<String> lines = FileUtils.readLines(new File("D:\\result2.txt"), "UTF-8");
        lines.addAll(FileUtils.readLines(new File("D:\\diff.txt"), "UTF-8"));

        //  presentType --> (要补天数 --> UID列表)
        Map<String, Map<Integer, List<String>>> dataMap = Maps.newHashMap();
        for (String line : lines) {
            String[] datas = line.split(",");
            String orderCode = datas[0].split(" ")[1];
            String userId = datas[1].split(" ")[2];
            String presentType = datas[2].split(" ")[2];
            String havePresent = datas[3].split(" ")[2].replaceAll("天", "");
            String realPresent = datas[4].split(" ")[2].replaceAll("天", "");
            // 实际应该赠送的-已经赠送的 = 需要补的差值
            int diff = Integer.parseInt(realPresent) - Integer.parseInt(havePresent);
            int real = 0;
            if (diff % 15 == 0) {
                real = diff;
            } else if (diff > 0) {
                real = (diff / 15 + 1) * 15;
            } else if (diff < 0) {
                real = diff / 15 * 15;
            }
            if (real == 0) {
                continue;
            }
            if (dataMap.get(presentType) == null) {
                Map<Integer, List<String>> tmp = Maps.newHashMap();
                tmp.put(real, Lists.newArrayList(userId));
                dataMap.put(presentType, tmp);
            } else {
                Map<Integer, List<String>> tmp = dataMap.get(presentType);
                if (tmp.get(real) == null) {
                    tmp.put(real, Lists.newArrayList(userId));
                } else {
                    tmp.get(real).add(userId);
                }
                dataMap.put(presentType, tmp);
            }
        }

        for (Map.Entry<String, Map<Integer, List<String>>> entry : dataMap.entrySet()) {
            for (Map.Entry<Integer, List<String>> ee : entry.getValue().entrySet()) {
                File file = new File("D:\\买赠权益修复\\" + entry.getKey() + "#" + ee.getKey() + ".txt");
                FileUtils.writeLines(file, ee.getValue());
            }
        }
    }

    @Test
    public void orderUpdate() throws Exception {
        List<String> lines = FileUtils.readLines(
            new File("D:\\diff.txt"), "UTF-8");
        List<String> orderCodeList = Lists.newArrayList();
        for (String line : lines) {
            String[] datas = line.split(",");
            String orderCode = datas[0].split(" ")[1];
            orderCodeList.add(orderCode);
        }

        List<String> orderLines = FileUtils.readLines(
            new File("D:\\order_deadline_qiyue_order.txt"), "UTF-8");
        List<String> sqlList = Lists.newArrayList();
        for (String orderLine : orderLines) {
            String[] datas = orderLine.split(", ");
            String uid = datas[0];
            String orderCode = datas[1];
            String rightStart = datas[4];
            String rightEnd = datas[5];
            if (orderCodeList.contains(orderCode)) {
                String shardUid = String.format("%02d", Long.parseLong(uid) % 100);
                Integer baseAmount = DateUtils.getDayInterval(
                    DateHelper.getDateFromStr(rightStart, "yyyy-MM-dd HH:mm:ss"),
                    DateHelper.getDateFromStr(rightEnd, "yyyy-MM-dd HH:mm:ss"));
                String sql = "update present_order_" + shardUid + " set deadline_start_time='" + rightStart + "',"
                    + " deadline_end_time='" + rightEnd + "',"
                    + " product_amount=" + baseAmount
                    + " where uid = " + uid
                    + " and order_code='" + orderCode + "';";
                sqlList.add(sql);
            }
        }
        String filePath = "D:\\sql.txt";
        FileUtils.deleteQuietly(new File(filePath));
        FileUtils.writeLines(new File(filePath), sqlList);
    }

}
