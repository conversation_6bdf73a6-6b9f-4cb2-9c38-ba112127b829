package com.iqiyi.vip.present;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import com.iqiyi.vip.present.history.QiYueDbJdbcTool;

/**
 * Created at: 2022-04-16
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = VipPresentApplication.class)
public class QiYueDbJdbcToolTest {

    static {
        System.setProperty("spring.profiles.active", "dev");
    }

    @Resource
    QiYueDbJdbcTool qiYueDbJdbcTool;

    @Test
    public void doTest() {
        String productCodeById = qiYueDbJdbcTool.getProductCodeById(4L);
        Assert.assertNotNull(productCodeById);
        String platformCodeById = qiYueDbJdbcTool.getPlatformCodeById(1L);
        Assert.assertNotNull(platformCodeById);
    }
}
