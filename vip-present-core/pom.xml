<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>vip-present</artifactId>
        <groupId>com.iqiyi.vip.present</groupId>
        <version>${present.version}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>vip-present-core</artifactId>
    <packaging>jar</packaging>
    <version>${parent.version}</version>

    <dependencies>
        <dependency>
            <groupId>com.iqiyi.kit</groupId>
            <artifactId>http-client</artifactId>
            <version>1.3.1</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!-- Hystrix已被移除，使用Resilience4j替代 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-circuitbreaker-resilience4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat</groupId>
                    <artifactId>tomcat-jdbc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.qiyi.usercloud</groupId>
            <artifactId>passport-authcookie</artifactId>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>1.4.192</version>
        </dependency>
      <dependency>
        <groupId>com.qiyi.usercloud</groupId>
        <artifactId>enigma-uid</artifactId>
        <version>1.1.4</version>
          <exclusions>
              <exclusion>
                  <artifactId>jackson-databind</artifactId>
                  <groupId>com.fasterxml.jackson.core</groupId>
              </exclusion>
          </exclusions>
      </dependency>

        <dependency>
            <groupId>com.iqiyi.vip</groupId>
            <artifactId>async-task-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iqiyi.kiwi</groupId>
            <artifactId>kiwi-utils</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
      <dependency>
        <groupId>org.apache.shardingsphere</groupId>
        <artifactId>sharding-core-api</artifactId>
        <version>4.0.0-RC2</version>
        <scope>compile</scope>
      </dependency>
        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
            <version>1.3.2</version>
        </dependency>
        <dependency>
            <groupId>com.iqiyi.config</groupId>
            <artifactId>config-client</artifactId>
            <version>3.15.7</version>
        </dependency>
        <dependency>
            <groupId>com.iqiyi.v</groupId>
            <artifactId>v-spring-boot-starter-eagle</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iqiyi.lego</groupId>
            <artifactId>lego-spring-rocketmq</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-jdbc</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.16</version>
        </dependency>

        <dependency>
            <groupId>io.shardingjdbc</groupId>
            <artifactId>sharding-jdbc-core</artifactId>
            <version>2.0.3</version>
        </dependency>

        <dependency>
            <groupId>com.iqiyi.kit</groupId>
            <artifactId>kit-jdbc</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.7</version>
                <configuration>
                    <configurationFile>src/main/resources/generatorConfig.xml</configurationFile>
                    <includeCompileDependencies>true</includeCompileDependencies>
                </configuration>
                <executions>
                    <execution>
                        <id>Generate MyBatis Artifacts</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.6</version>
            </plugin>
        </plugins>
    </build>
</project>