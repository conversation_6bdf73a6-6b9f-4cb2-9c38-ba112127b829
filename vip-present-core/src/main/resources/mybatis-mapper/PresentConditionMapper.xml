<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.present.mapper.PresentConditionMapper">
  <resultMap id="BaseResultMap" type="com.iqiyi.vip.present.model.PresentCondition">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="condition" jdbcType="VARCHAR" property="condition" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="priority" jdbcType="INTEGER" property="priority" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="perform_id" jdbcType="BIGINT" property="performId"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, `condition`, `status`, priority, update_time, create_time, perform_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from present_condition
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from present_condition
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.iqiyi.vip.present.model.PresentCondition">
    insert into present_condition (id, `condition`, `status`,
      priority, update_time, create_time, 
      perform_id)
    values (#{id,jdbcType=BIGINT}, #{condition,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{priority,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{performId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.iqiyi.vip.present.model.PresentCondition">
    insert into present_condition
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="condition != null">
        `condition`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="priority != null">
        priority,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="performId != null">
        perform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="condition != null">
        #{condition,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="performId != null">
        #{performId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iqiyi.vip.present.model.PresentCondition">
    update present_condition
    <set>
      <if test="condition != null">
        `condition` = #{condition,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="priority != null">
        priority = #{priority,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="performId != null">
        perform_id = #{performId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.present.model.PresentCondition">
    update present_condition
    set `condition` = #{condition,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      priority = #{priority,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      perform_id = #{performId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryConditionByIds" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from present_condition
    where id in
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
      and `status`=1
      order by priority desc
  </select>


  <select id="queryConditions" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from present_condition
    where `status`=1
  </select>
</mapper>