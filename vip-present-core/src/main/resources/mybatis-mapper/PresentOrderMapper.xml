<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.present.mapper.PresentOrderMapper">
  <resultMap id="BaseResultMap" type="com.iqiyi.vip.present.model.PresentOrder">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="uid" jdbcType="BIGINT" property="uid"/>
    <result column="msg_id" jdbcType="VARCHAR" property="msgId"/>
    <result column="order_code" jdbcType="VARCHAR" property="orderCode"/>
    <result column="present_order_code" jdbcType="VARCHAR" property="presentOrderCode"/>
    <result column="present_trade_code" jdbcType="VARCHAR" property="presentTradeCode"/>
    <result column="refund_order_code" jdbcType="VARCHAR" property="refundOrderCode"/>
    <result column="buy_type" jdbcType="VARCHAR" property="buyType"/>
    <result column="present_type" jdbcType="VARCHAR" property="presentType"/>
    <result column="product_amount" jdbcType="INTEGER" property="productAmount"/>
    <result column="status" jdbcType="INTEGER" property="status"/>
    <result column="present_config_id" jdbcType="BIGINT" property="presentConfigId"/>
    <result column="present_condition_id" jdbcType="BIGINT" property="presentConditionId"/>
    <result column="present_perform_id" jdbcType="BIGINT" property="presentPerformId"/>
    <result column="deadline_start_time" jdbcType="TIMESTAMP" property="deadlineStartTime"/>
    <result column="deadline_end_time" jdbcType="TIMESTAMP" property="deadlineEndTime"/>
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
    <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime"/>
    <result column="receive_deadline_time" jdbcType="TIMESTAMP" property="receiveDeadlineTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="order_type" jdbcType="INTEGER" property="orderType"/>
    <result column="fv" jdbcType="VARCHAR" property="fv"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, uid, msg_id, order_code, present_order_code, present_trade_code, refund_order_code,
    buy_type, present_type, product_amount, `status`, present_config_id, present_condition_id,
    present_perform_id, deadline_start_time, deadline_end_time, pay_time, receive_time,
    receive_deadline_time, update_time, create_time, order_type, fv
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from present_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from present_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.iqiyi.vip.present.model.PresentOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into present_order (id, uid, msg_id,
    order_code, present_order_code, present_trade_code,
    refund_order_code, buy_type, present_type,
    product_amount, `status`, present_config_id,
    present_condition_id, present_perform_id, deadline_start_time,
    deadline_end_time, pay_time, receive_time,
    receive_deadline_time, update_time, create_time,
    order_type, fv)
    values (#{id,jdbcType=BIGINT}, #{uid,jdbcType=BIGINT}, #{msgId,jdbcType=VARCHAR},
    #{orderCode,jdbcType=VARCHAR}, #{presentOrderCode,jdbcType=VARCHAR}, #{presentTradeCode,jdbcType=VARCHAR},
    #{refundOrderCode,jdbcType=VARCHAR}, #{buyType,jdbcType=VARCHAR}, #{presentType,jdbcType=VARCHAR},
    #{productAmount,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{presentConfigId,jdbcType=BIGINT},
    #{presentConditionId,jdbcType=BIGINT}, #{presentPerformId,jdbcType=BIGINT}, #{deadlineStartTime,jdbcType=TIMESTAMP},
    #{deadlineEndTime,jdbcType=TIMESTAMP}, #{payTime,jdbcType=TIMESTAMP}, #{receiveTime,jdbcType=TIMESTAMP},
    #{receiveDeadlineTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP},
    #{orderType,jdbcType=INTEGER}, #{fv,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.iqiyi.vip.present.model.PresentOrder">
    insert into present_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="msgId != null">
        msg_id,
      </if>
      <if test="orderCode != null">
        order_code,
      </if>
      <if test="presentOrderCode != null">
        present_order_code,
      </if>
      <if test="presentTradeCode != null">
        present_trade_code,
      </if>
      <if test="refundOrderCode != null">
        refund_order_code,
      </if>
      <if test="buyType != null">
        buy_type,
      </if>
      <if test="presentType != null">
        present_type,
      </if>
      <if test="productAmount != null">
        product_amount,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="presentConfigId != null">
        present_config_id,
      </if>
      <if test="presentConditionId != null">
        present_condition_id,
      </if>
      <if test="presentPerformId != null">
        present_perform_id,
      </if>
      <if test="deadlineStartTime != null">
        deadline_start_time,
      </if>
      <if test="deadlineEndTime != null">
        deadline_end_time,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="receiveTime != null">
        receive_time,
      </if>
      <if test="receiveDeadlineTime != null">
        receive_deadline_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="fv != null">
        fv,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=BIGINT},
      </if>
      <if test="msgId != null">
        #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="orderCode != null">
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="presentOrderCode != null">
        #{presentOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="presentTradeCode != null">
        #{presentTradeCode,jdbcType=VARCHAR},
      </if>
      <if test="refundOrderCode != null">
        #{refundOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="buyType != null">
        #{buyType,jdbcType=VARCHAR},
      </if>
      <if test="presentType != null">
        #{presentType,jdbcType=VARCHAR},
      </if>
      <if test="productAmount != null">
        #{productAmount,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="presentConfigId != null">
        #{presentConfigId,jdbcType=BIGINT},
      </if>
      <if test="presentConditionId != null">
        #{presentConditionId,jdbcType=BIGINT},
      </if>
      <if test="presentPerformId != null">
        #{presentPerformId,jdbcType=BIGINT},
      </if>
      <if test="deadlineStartTime != null">
        #{deadlineStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deadlineEndTime != null">
        #{deadlineEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveTime != null">
        #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveDeadlineTime != null">
        #{receiveDeadlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="fv != null">
        #{fv,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iqiyi.vip.present.model.PresentOrder">
    update present_order
    <set>
      <if test="uid != null">
        uid = #{uid,jdbcType=BIGINT},
      </if>
      <if test="msgId != null">
        msg_id = #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="orderCode != null">
        order_code = #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="presentOrderCode != null">
        present_order_code = #{presentOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="presentTradeCode != null">
        present_trade_code = #{presentTradeCode,jdbcType=VARCHAR},
      </if>
      <if test="refundOrderCode != null">
        refund_order_code = #{refundOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="buyType != null">
        buy_type = #{buyType,jdbcType=VARCHAR},
      </if>
      <if test="presentType != null">
        present_type = #{presentType,jdbcType=VARCHAR},
      </if>
      <if test="productAmount != null">
        product_amount = #{productAmount,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="presentConfigId != null">
        present_config_id = #{presentConfigId,jdbcType=BIGINT},
      </if>
      <if test="presentConditionId != null">
        present_condition_id = #{presentConditionId,jdbcType=BIGINT},
      </if>
      <if test="presentPerformId != null">
        present_perform_id = #{presentPerformId,jdbcType=BIGINT},
      </if>
      <if test="deadlineStartTime != null">
        deadline_start_time = #{deadlineStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deadlineEndTime != null">
        deadline_end_time = #{deadlineEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveTime != null">
        receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveDeadlineTime != null">
        receive_deadline_time = #{receiveDeadlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="fv != null">
        fv = #{fv,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.present.model.PresentOrder">
    update present_order
    set uid = #{uid,jdbcType=BIGINT},
      msg_id = #{msgId,jdbcType=VARCHAR},
      order_code = #{orderCode,jdbcType=VARCHAR},
      present_order_code = #{presentOrderCode,jdbcType=VARCHAR},
      present_trade_code = #{presentTradeCode,jdbcType=VARCHAR},
      refund_order_code = #{refundOrderCode,jdbcType=VARCHAR},
      buy_type = #{buyType,jdbcType=VARCHAR},
      present_type = #{presentType,jdbcType=VARCHAR},
      product_amount = #{productAmount,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      present_config_id = #{presentConfigId,jdbcType=BIGINT},
      present_condition_id = #{presentConditionId,jdbcType=BIGINT},
      present_perform_id = #{presentPerformId,jdbcType=BIGINT},
      deadline_start_time = #{deadlineStartTime,jdbcType=TIMESTAMP},
      deadline_end_time = #{deadlineEndTime,jdbcType=TIMESTAMP},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      receive_deadline_time = #{receiveDeadlineTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      order_type = #{orderType,jdbcType=INTEGER},
      fv = #{fv,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryOrderByParams" parameterType="com.iqiyi.vip.present.model.PresentOrder" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from present_order
    where uid = #{uid,jdbcType=BIGINT}
    <if test="orderCode != null">
      and order_code=#{orderCode}
    </if>
    <if test="status != null">
      and `status`=#{status}
    </if>
    <if test="orderType != null">
      and order_type = #{orderType}
    </if>
    <if test="buyType != null">
      and buy_type=#{buyType}
    </if>
    <if test="presentType != null">
      and present_type=#{presentType}
    </if>
    <if test="presentConfigId != null">
      and present_config_id=#{presentConfigId}
    </if>
  </select>

  <update id="updateOrderStatus">
    update present_order
    <set>
      <if test="presentOrder.presentOrderCode != null">
        present_order_code = #{presentOrder.presentOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="presentOrder.presentTradeCode != null">
        present_trade_code = #{presentOrder.presentTradeCode,jdbcType=VARCHAR},
      </if>
      <if test="presentOrder.refundOrderCode != null">
        refund_order_code = #{presentOrder.refundOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="presentOrder.status != null">
        `status` = #{presentOrder.status,jdbcType=INTEGER},
      </if>
      <if test="presentOrder.receiveTime != null">
        receive_time = #{presentOrder.receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="presentOrder.updateTime != null">
        update_time = #{presentOrder.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="presentOrder.deadlineStartTime != null">
        deadline_start_time = #{presentOrder.deadlineStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="presentOrder.deadlineEndTime != null">
        deadline_end_time = #{presentOrder.deadlineEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="presentOrder.orderType != null">
        order_type = #{presentOrder.orderType,jdbcType=INTEGER},
      </if>
      <if test="presentOrder.fv != null">
        fv = #{presentOrder.fv,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{presentOrder.id,jdbcType=BIGINT}
    and `status`=#{oldStatus}
    and uid = #{presentOrder.uid,jdbcType=BIGINT}
  </update>

    <update id="updateStatusFromOld">
        update present_order
        <set>
            <if test="newStatus != null">
                `status` = #{newStatus},
            </if>
                update_time = NOW()
        </set>
        where id = #{id}
        and `status`=#{oldStatus}
        and uid = #{uid}
    </update>



  <update id="updateOrderByAlreadyPresent" parameterType="com.iqiyi.vip.present.model.PresentOrder">
    update present_order
    <set>
      <if test="presentOrderCode != null">
        present_order_code = #{presentOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="presentTradeCode != null">
        present_trade_code = #{presentTradeCode,jdbcType=VARCHAR},
      </if>
      <if test="refundOrderCode != null">
        refund_order_code = #{refundOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="receiveTime != null">
        receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deadlineStartTime != null">
        deadline_start_time = #{deadlineStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deadlineEndTime != null">
        deadline_end_time = #{deadlineEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="fv != null">
        fv = #{fv,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
    and `status`=0
    and uid = #{uid,jdbcType=BIGINT}
  </update>

  <select id="queryByParamsAndDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from present_order_${tableNo}
    where  1=1 
    <if test="presentOrder.buyType != null">
      and buy_type=#{presentOrder.buyType}
    </if>
    <if test="presentOrder.presentType != null">
      and present_type=#{presentOrder.presentType}
    </if>
    <if test="presentOrder.presentConfigId != null">
      and present_config_id=#{presentOrder.presentConfigId}
    </if>
      and update_time >=#{startTime} and <![CDATA[ update_time < #{endTime}]]>
  </select>

  <select id="queryKiwiPresentGoldNotReceiveOrder" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from present_order_${tableNo}
    where present_config_id = 50
    AND `status` = 4
    AND order_type = 0
    AND DATEDIFF(NOW(),create_time)= #{diffDay}
    AND deadline_end_time > now()
    order by create_time desc
  </select>

  <select id="queryKiwiPresentGoldNotReceiveOrderByRange" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from present_order_${tableNo}
    where present_config_id = #{configId}
    AND `status` = 4
    AND order_type = 0
    AND create_time <![CDATA[ >= ]]> #{startTime}
    AND create_time <![CDATA[ <= ]]> #{endTime}
    AND deadline_end_time > now()
    <if test="limitNum != null">
      limit #{limitNum}
    </if>
  </select>

  <select id="queryOrderByPageParams" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from present_order
    where  uid = #{uid,jdbcType=BIGINT}
    <if test="orderCode != null">
      and order_code=#{orderCode}
    </if>
    <if test="presentConfigId != null">
      and present_config_id=#{presentConfigId}
    </if>
    order by ${sortField}
    <if test="order == 'asc'">
      ASC
    </if>
    <if test="order == 'desc'">
      DESC
    </if>
    limit ${start} , ${pageSize}

  </select>

  <select id="findOrderTotalCounts" parameterType="java.util.Map" resultType="java.lang.Integer">
    select count(1)
    from present_order
    where  uid = #{uid,jdbcType=BIGINT}
    <if test="orderCode != null">
      and order_code=#{orderCode}
    </if>
    <if test="presentConfigId != null">
      and present_config_id=#{presentConfigId}
    </if>
  </select>

    <select id="queryManualPresentOrders" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from present_order_${tableNo}
        where `status` = 9
        AND order_type = 0
        AND create_time <![CDATA[ >= ]]> #{startTime}
        AND create_time <![CDATA[ <= ]]> #{endTime}
        AND deadline_end_time > now()
        <if test="configIdList != null and configIdList.size() > 0">
            AND present_config_id in
            <foreach collection="configIdList" item="configId" separator="," open="(" close=")">
                #{configId}
            </foreach>
        </if>
        ORDER BY create_time ASC
        <if test="limitNum != null">
            limit #{limitNum}
        </if>
    </select>

  <select id="queryByPresentTradeCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from present_order
    where uid = #{uid,jdbcType=BIGINT} and present_trade_code = #{presentTradeCode}
  </select>

</mapper>