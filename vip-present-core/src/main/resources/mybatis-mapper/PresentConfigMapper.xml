<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.present.mapper.PresentConfigMapper">
  <resultMap id="BaseResultMap" type="com.iqiyi.vip.present.model.PresentConfig">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="bvip_type" jdbcType="VARCHAR" property="bvipType"/>
    <result column="pvip_type" jdbcType="VARCHAR" property="pvipType"/>
    <result column="buy_code" jdbcType="VARCHAR" property="buyCode"/>
    <result column="present_code" jdbcType="VARCHAR" property="presentCode"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="status" jdbcType="INTEGER" property="status"/>
    <result column="handler_status" jdbcType="INTEGER" property="handlerStatus"/>
    <result column="grouping" jdbcType="VARCHAR" property="grouping"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="pay_type" jdbcType="INTEGER" property="payType"/>
    <result column="condition_ids" jdbcType="VARCHAR" property="conditionIds"/>
    <result column="cal_amount_type" jdbcType="INTEGER" property="calAmountType"/>
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, bvip_type, pvip_type, buy_code, present_code, remark, `status`, handler_status,
    grouping, update_time, create_time, pay_type, condition_ids, cal_amount_type, start_time,
    end_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from present_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from present_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.iqiyi.vip.present.model.PresentConfig">
    insert into present_config (id, bvip_type, pvip_type,
      buy_code, present_code, remark,
      `status`, handler_status, grouping,
      update_time, create_time, pay_type,
      condition_ids, cal_amount_type, start_time,
      end_time)
    values (#{id,jdbcType=BIGINT}, #{bvipType,jdbcType=VARCHAR}, #{pvipType,jdbcType=VARCHAR},
      #{buyCode,jdbcType=VARCHAR}, #{presentCode,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
      #{status,jdbcType=INTEGER}, #{handlerStatus,jdbcType=INTEGER}, #{grouping,jdbcType=VARCHAR},
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{payType,jdbcType=INTEGER},
      #{conditionIds,jdbcType=VARCHAR}, #{calAmountType,jdbcType=INTEGER}, #{startTime,jdbcType=TIMESTAMP},
      #{endTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.iqiyi.vip.present.model.PresentConfig">
    insert into present_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="bvipType != null">
        bvip_type,
      </if>
      <if test="pvipType != null">
        pvip_type,
      </if>
      <if test="buyCode != null">
        buy_code,
      </if>
      <if test="presentCode != null">
        present_code,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="handlerStatus != null">
        handler_status,
      </if>
      <if test="grouping != null">
        grouping,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="conditionIds != null">
        condition_ids,
      </if>
      <if test="calAmountType != null">
        cal_amount_type,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="bvipType != null">
        #{bvipType,jdbcType=VARCHAR},
      </if>
      <if test="pvipType != null">
        #{pvipType,jdbcType=VARCHAR},
      </if>
      <if test="buyCode != null">
        #{buyCode,jdbcType=VARCHAR},
      </if>
      <if test="presentCode != null">
        #{presentCode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="handlerStatus != null">
        #{handlerStatus,jdbcType=INTEGER},
      </if>
      <if test="grouping != null">
        #{grouping,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=INTEGER},
      </if>
      <if test="conditionIds != null">
        #{conditionIds,jdbcType=VARCHAR},
      </if>
      <if test="calAmountType != null">
        #{calAmountType,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iqiyi.vip.present.model.PresentConfig">
    update present_config
    <set>
      <if test="bvipType != null">
        bvip_type = #{bvipType,jdbcType=VARCHAR},
      </if>
      <if test="pvipType != null">
        pvip_type = #{pvipType,jdbcType=VARCHAR},
      </if>
      <if test="buyCode != null">
        buy_code = #{buyCode,jdbcType=VARCHAR},
      </if>
      <if test="presentCode != null">
        present_code = #{presentCode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="handlerStatus != null">
        handler_status = #{handlerStatus,jdbcType=INTEGER},
      </if>
      <if test="grouping != null">
        grouping = #{grouping,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=INTEGER},
      </if>
      <if test="conditionIds != null">
        condition_ids = #{conditionIds,jdbcType=VARCHAR},
      </if>
      <if test="calAmountType != null">
        cal_amount_type = #{calAmountType,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.present.model.PresentConfig">
    update present_config
    set bvip_type = #{bvipType,jdbcType=VARCHAR},
      pvip_type = #{pvipType,jdbcType=VARCHAR},
      buy_code = #{buyCode,jdbcType=VARCHAR},
      present_code = #{presentCode,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      handler_status = #{handlerStatus,jdbcType=INTEGER},
      grouping = #{grouping,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      pay_type = #{payType,jdbcType=INTEGER},
      condition_ids = #{conditionIds,jdbcType=VARCHAR},
      cal_amount_type = #{calAmountType,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryPresentConfig" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from present_config
  </select>

  <select id="queryConfigListByGroup" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from present_config
    where `status`=1 and handler_status=1 and grouping=#{grouping}
  </select>

  <select id="queryConfigListByVipTypeList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from present_config
    where `status`=1 and bvip_type in
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
</mapper>