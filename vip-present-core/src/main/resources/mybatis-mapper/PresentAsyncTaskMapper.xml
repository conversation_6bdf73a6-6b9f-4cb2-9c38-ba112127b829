<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.present.mapper.PresentAsyncTaskMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.present.model.PresentAsyncTask">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="classname" jdbcType="VARCHAR" property="className"/>
        <result column="data" jdbcType="LONGVARCHAR" property="data"/>
        <result column="task_id" jdbcType="VARCHAR" property="taskId"/>
        <result column="inqueue" jdbcType="TINYINT" property="inQueue"/>
        <result column="timerrun_at" jdbcType="TIMESTAMP" property="timerRunAt"/>
        <result column="exec_count" jdbcType="TINYINT" property="execCount"/>
        <result column="priority" jdbcType="TINYINT" property="priority"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, classname, data, task_id, inqueue, timerrun_at, priority, exec_count, create_time, update_time
    </sql>

    <insert id="insert" parameterType="com.iqiyi.vip.present.model.PresentAsyncTask" keyProperty="id" useGeneratedKeys="true">
        insert into present_async_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="className != null">
                classname,
            </if>
            <if test="data != null">
                data,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="inQueue != null">
                inqueue,
            </if>
            <if test="timerRunAt != null">
                timerrun_at,
            </if>
            <if test="execCount != null">
                execCount,
            </if>
            <if test="priority != null">
                priority,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="className != null">
                #{className,jdbcType=VARCHAR},
            </if>
            <if test="data != null">
                #{data,jdbcType=LONGVARCHAR},
            </if>
            <if test="taskId != null">
                #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="inQueue != null">
                #{inQueue,jdbcType=TINYINT},
            </if>
            <if test="timerRunAt != null">
                #{timerRunAt,jdbcType=TIMESTAMP},
            </if>
            <if test="execCount != null">
                #{execCount,jdbcType=TINYINT},
            </if>
            <if test="priority != null">
                #{priority,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.present.model.PresentAsyncTask">
        update present_async_task
        <set>
            <if test="className != null">
                classname = #{className,jdbcType=VARCHAR},
            </if>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="inqueue != null">
                inqueue = #{inQueue,jdbcType=TINYINT},
            </if>
            <if test="timerrunAt != null">
                timerrun_at = #{timerRunAt,jdbcType=TIMESTAMP},
            </if>
            <if test="execCount != null">
                exec_count = #{execCount,jdbcType=TINYINT},
            </if>
            <if test="priority != null">
                priority = #{priority,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="data != null">
                data = #{data,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="makeTaskProcessing" parameterType="java.lang.Long">
        update present_async_task set inqueue = 1 where id = #{id} and inqueue = 0;
    </update>

    <update id="makeTaskUnProcess" parameterType="java.lang.Long">
        update boss_async_task set inqueue = 0 where id = #{id} and inqueue = 1;
    </update>

    <update id="restoreTaskForRetry">
        update present_async_task set timerrun_at = #{nextRunTime}, exec_count = exec_count+1, inqueue = 0
        where id = #{id} and inqueue = 1;
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from present_async_task where id = #{id,jdbcType=BIGINT}
    </delete>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from present_async_task
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByTaskId" resultType="com.iqiyi.vip.present.model.PresentAsyncTask">
        select <include refid="Base_Column_List"/>
        from present_async_task
        where task_id = #{taskId,jdbcType=VARCHAR}
    </select>

</mapper>