<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.present.mapper.PresentRecordMapper">
  <resultMap id="BaseResultMap" type="com.iqiyi.vip.present.model.PresentRecord">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="msg_id" jdbcType="VARCHAR" property="msgId"/>
    <result column="buy_uid" jdbcType="BIGINT" property="buyUid"/>
    <result column="receive_uid" jdbcType="VARCHAR" property="receiveUid"/>
    <result column="buy_type" jdbcType="VARCHAR" property="buyType"/>
    <result column="present_type" jdbcType="VARCHAR" property="presentType"/>
    <result column="present_trade_code" jdbcType="VARCHAR" property="presentTradeCode"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, msg_id, buy_uid, receive_uid, buy_type, present_type, present_trade_code, update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from present_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from present_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.iqiyi.vip.present.model.PresentRecord">
    insert into present_record (id, msg_id, buy_uid,
      receive_uid, buy_type, present_type,
      present_trade_code, update_time, create_time
      )
    values (#{id,jdbcType=BIGINT}, #{msgId,jdbcType=VARCHAR}, #{buyUid,jdbcType=BIGINT},
      #{receiveUid,jdbcType=VARCHAR}, #{buyType,jdbcType=VARCHAR}, #{presentType,jdbcType=VARCHAR},
      #{presentTradeCode,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.iqiyi.vip.present.model.PresentRecord">
    insert into present_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="msgId != null">
        msg_id,
      </if>
      <if test="buyUid != null">
        buy_uid,
      </if>
      <if test="receiveUid != null">
        receive_uid,
      </if>
      <if test="buyType != null">
        buy_type,
      </if>
      <if test="presentType != null">
        present_type,
      </if>
      <if test="presentTradeCode != null">
        present_trade_code,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="msgId != null">
        #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="buyUid != null">
        #{buyUid,jdbcType=BIGINT},
      </if>
      <if test="receiveUid != null">
        #{receiveUid,jdbcType=VARCHAR},
      </if>
      <if test="buyType != null">
        #{buyType,jdbcType=VARCHAR},
      </if>
      <if test="presentType != null">
        #{presentType,jdbcType=VARCHAR},
      </if>
      <if test="presentTradeCode != null">
        #{presentTradeCode,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iqiyi.vip.present.model.PresentRecord">
    update present_record
    <set>
      <if test="msgId != null">
        msg_id = #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="buyUid != null">
        buy_uid = #{buyUid,jdbcType=BIGINT},
      </if>
      <if test="receiveUid != null">
        receive_uid = #{receiveUid,jdbcType=VARCHAR},
      </if>
      <if test="buyType != null">
        buy_type = #{buyType,jdbcType=VARCHAR},
      </if>
      <if test="presentType != null">
        present_type = #{presentType,jdbcType=VARCHAR},
      </if>
      <if test="presentTradeCode != null">
        present_trade_code = #{presentTradeCode,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.present.model.PresentRecord">
    update present_record
    set msg_id = #{msgId,jdbcType=VARCHAR},
      buy_uid = #{buyUid,jdbcType=BIGINT},
      receive_uid = #{receiveUid,jdbcType=VARCHAR},
      buy_type = #{buyType,jdbcType=VARCHAR},
      present_type = #{presentType,jdbcType=VARCHAR},
      present_trade_code = #{presentTradeCode,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryRecordByUidAndVipType" parameterType="com.iqiyi.vip.present.model.PresentRecord"
    resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from present_record
    where 1=1

    <if test="receiveUid != null">
      and receive_uid = #{receiveUid,jdbcType=VARCHAR}
    </if>
    <if test="buyType != null">
      and buy_type = #{buyType,jdbcType=VARCHAR}
    </if>
    <if test="presentType != null">
      and present_type = #{presentType,jdbcType=VARCHAR}
    </if>
    <if test="buyUid != null">
      and buy_uid = #{buyUid,jdbcType=VARCHAR}
    </if>
  </select>
</mapper>