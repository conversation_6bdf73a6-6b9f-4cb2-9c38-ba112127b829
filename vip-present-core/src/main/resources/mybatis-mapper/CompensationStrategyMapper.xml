<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.present.mapper.CompensationStrategyMapper">
  <resultMap id="BaseResultMap" type="com.iqiyi.vip.present.entity.CompensationStrategy">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="rule_id" jdbcType="INTEGER" property="ruleId"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="description" jdbcType="VARCHAR" property="description"/>
    <result column="class_name" jdbcType="VARCHAR" property="className"/>
    <result column="auto_execute" jdbcType="INTEGER" property="autoExecute"/>
    <result column="priority" jdbcType="INTEGER" property="priority"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
  </resultMap>
  
  <sql id="Base_Column_List">
    id
    , rule_id, `name`, description, class_name, auto_execute, priority, create_time,
    update_time, `status`
  </sql>
  
  <select id="findByRuleId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from compensation_strategy
    where rule_id = #{ruleId,jdbcType=INTEGER}
    and status = 1
    order by priority desc
  </select>
</mapper>
