<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.present.dao.OrderDao">

    <select id="findOrderToCheck" resultType="com.iqiyi.vip.present.entity.PersistOrder">
        select *
        from ${tableName}
        where
          pay_time <![CDATA[<=]]> #{endTime}
          and pay_time >= #{startTime}
        <if test="condition != null and condition != ''">
            and ${condition}
        </if>
    </select>

    <select id="findByOrderCode" resultType="com.iqiyi.vip.present.entity.PersistOrder">
        select * from ${tableName} where order_code = #{orderCode}
    </select>

</mapper>
