<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.present.mapper.BossVipUserMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.present.model.BossVipUser">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="level_id" jdbcType="INTEGER" property="levelId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="deadline" jdbcType="TIMESTAMP" property="deadline"/>
        <result column="type_id" jdbcType="INTEGER" property="typeId"/>
        <result column="pay_type" jdbcType="INTEGER" property="payType"/>
        <result column="paid_sign" jdbcType="INTEGER" property="paidSign"/>
        <result column="version" jdbcType="BIGINT" property="version"/>
        <result column="year_deadline" jdbcType="TIMESTAMP" property="yearDeadline"/>
        <result column="card_number" jdbcType="BIGINT" property="cardNumber"/>
        <result column="auto_renew" jdbcType="INTEGER" property="autoRenew"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , user_id, level_id, `status`, deadline, type_id,pay_type,paid_sign,version,year_deadline,
        card_number, auto_renew, update_time, create_time
    </sql>

    <select id="selectByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from boss_vip_user
        where user_id=#{userId}
    </select>

    <select id="selectUserIdByGroupMax" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from boss_vip_user
        where user_id = #{userId}
        or max(deadline) > now()
        order by user_id asc limit 30
    </select>
</mapper>