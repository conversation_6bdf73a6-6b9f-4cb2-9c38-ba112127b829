<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.present.mapper.CheckRecordMapper">
  <resultMap id="BaseResultMap" type="com.iqiyi.vip.present.entity.CheckRecord">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="rule_id" jdbcType="INTEGER" property="ruleId"/>
    <result column="order_code" jdbcType="VARCHAR" property="orderCode"/>
    <result column="extend_param" jdbcType="VARCHAR" property="extendParam"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="status" jdbcType="INTEGER" property="status"/>
    <result column="user_id" jdbcType="BIGINT" property="userId"/>
  </resultMap>
  
  <sql id="Base_Column_List">
    id, rule_id, order_code, extend_param, create_time, update_time, status, user_id
  </sql>
  
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.iqiyi.vip.present.entity.CheckRecord" useGeneratedKeys="true">
    insert into check_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="orderCode != null">
        order_code,
      </if>
      <if test="extendParam != null">
        extend_param,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="userId != null">
        user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ruleId != null">
        #{ruleId,jdbcType=INTEGER},
      </if>
      <if test="orderCode != null">
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="extendParam != null">
        #{extendParam,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  
  <update id="updateCheckRecord" parameterType="com.iqiyi.vip.present.entity.CheckRecord">
    update check_record
    <set>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=INTEGER},
      </if>
      <if test="orderCode != null">
        order_code = #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="extendParam != null">
        extend_param = #{extendParam,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <select id="findRecordByStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from check_record
    where status in
    <foreach collection="status" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and update_time >= #{startTime,jdbcType=VARCHAR}
    order by update_time desc
  </select>

  <select id="findByOrderCodeAndRuleId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from check_record
    where order_code = #{orderCode,jdbcType=VARCHAR}
    and rule_id = #{ruleId,jdbcType=INTEGER}
    limit 1
  </select>
  
  <delete id="deleteRecordsBefore">
    delete from check_record
    where create_time &lt; #{date,jdbcType=TIMESTAMP}
  </delete>
</mapper>
