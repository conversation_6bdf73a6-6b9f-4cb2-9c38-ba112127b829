<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.present.mapper.CheckRuleMapper">
  <resultMap id="BaseResultMap" type="com.iqiyi.vip.present.entity.CheckRule">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="code" jdbcType="VARCHAR" property="code"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="condition" jdbcType="VARCHAR" property="condition"/>
    <result column="offset" jdbcType="INTEGER" property="offset"/>
    <result column="width" jdbcType="INTEGER" property="width"/>
    <result column="data_base" jdbcType="VARCHAR" property="dataBase"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>
  
  <sql id="Base_Column_List">
    id, code, `name`, `condition`, `offset`, width, data_base, `status`, create_time, update_time
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from check_rule
    where id = #{id,jdbcType=INTEGER}
  </select>
</mapper>
