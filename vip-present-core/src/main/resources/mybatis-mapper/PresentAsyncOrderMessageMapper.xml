<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.present.mapper.PresentAsyncOrderMessageMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.present.model.PresentAsyncOrderMessage">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="uid" jdbcType="VARCHAR" property="uid"/>
        <result column="order_code" jdbcType="VARCHAR" property="orderCode"/>
        <result column="product_sub_type" jdbcType="VARCHAR" property="productSubType"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="order_message" jdbcType="INTEGER" property="orderMessage"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , uid, order_code, product_sub_type, start_time, end_time, order_message, create_time, update_time
    </sql>
    <select id="queryMessages" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from present_async_order_message
        where uid = #{uid}
        and product_sub_type=#{productSubType}
    </select>
    <delete id="delete" parameterType="java.lang.String">
        delete
        from present_async_order_message
        where order_code = #{orderCode}
    </delete>

    <insert id="insertSelective" parameterType="com.iqiyi.vip.present.model.PresentAsyncOrderMessage">
        insert into present_async_order_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="uid != null">
                uid,
            </if>
            <if test="orderCode != null">
                order_code,
            </if>
            <if test="productSubType != null">
                product_sub_type,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="orderMessage != null">
                order_message,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="uid != null">
                #{uid,jdbcType=BIGINT},
            </if>
            <if test="orderCode != null">
                #{orderCode,jdbcType=VARCHAR},
            </if>
            <if test="productSubType != null">
                #{productSubType,jdbcType=INTEGER},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="orderMessage != null">
                #{orderMessage,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
</mapper>