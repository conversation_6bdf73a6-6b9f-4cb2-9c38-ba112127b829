<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.present.mapper.PresentProductMapper">
  <resultMap id="BaseResultMap" type="com.iqiyi.vip.present.model.PresentProduct">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="name_tr" jdbcType="VARCHAR" property="nameTr" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="price" jdbcType="INTEGER" property="price" />
    <result column="original_price" jdbcType="INTEGER" property="originalPrice" />
    <result column="period" jdbcType="INTEGER" property="period" />
    <result column="period_unit" jdbcType="INTEGER" property="periodUnit" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="charge_type" jdbcType="INTEGER" property="chargeType" />
    <result column="sub_type" jdbcType="INTEGER" property="subType" />
    <result column="service_type" jdbcType="INTEGER" property="serviceType" />
    <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
    <result column="vip_type_code" jdbcType="VARCHAR" property="vipTypeCode" />
    <result column="source_vip_type_code" jdbcType="VARCHAR" property="sourceVipTypeCode" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="deadline" jdbcType="TIMESTAMP" property="deadline" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="support_exp" jdbcType="INTEGER" property="supportExp" />
    <result column="support_type" jdbcType="VARCHAR" property="supportType" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="scope" jdbcType="INTEGER" property="scope" />
    <result column="source_sub_type" jdbcType="INTEGER" property="sourceSubType" />
    <result column="rebuy" jdbcType="INTEGER" property="rebuy" />
    <result column="time_type" jdbcType="INTEGER" property="timeType" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="unit" jdbcType="TINYINT" property="unit" />
    <result column="cooperator_uid" jdbcType="VARCHAR" property="cooperatorUid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="currency_unit" jdbcType="VARCHAR" property="currencyUnit" />
    <result column="currency_symbol" jdbcType="VARCHAR" property="currencySymbol" />
    <result column="business_charge" jdbcType="VARCHAR" property="businessCharge" />
  </resultMap>

  <sql id="Base_Column_List">
    id, `name`, name_tr, area, code, price, original_price, period, period_unit, `type`, 
    charge_type, sub_type, service_type, business_code, vip_type_code, source_vip_type_code, 
    `status`, deadline, url, support_exp, support_type, business_type, `scope`, source_sub_type, 
    rebuy, time_type, version, description, unit, cooperator_uid, create_time, update_time, 
    `operator`, currency_unit, currency_symbol, business_charge
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from present_product
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="selectByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from present_product
    where code = #{code,jdbcType=VARCHAR}
  </select>

  <select id="getByVipType" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from present_product
    where vip_type_code = #{vipTypeCode,jdbcType=VARCHAR}
  </select>

  <select id="getCodeById" parameterType="java.lang.Long" resultType="java.lang.String">
    select code
    from present_product
    where id = #{id}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from present_product
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.iqiyi.vip.present.model.PresentProduct">
    insert into present_product (id, `name`, name_tr, 
      area, code, price, 
      original_price, period, period_unit, 
      `type`, charge_type, sub_type, 
      service_type, business_code, vip_type_code, 
      source_vip_type_code, `status`, deadline, 
      url, support_exp, support_type, 
      business_type, `scope`, source_sub_type, 
      rebuy, time_type, version, 
      description, unit, cooperator_uid, 
      create_time, update_time, `operator`, 
      currency_unit, currency_symbol, business_charge)
    values (#{id,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{nameTr,jdbcType=VARCHAR}, 
      #{area,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, #{price,jdbcType=INTEGER}, 
      #{originalPrice,jdbcType=INTEGER}, #{period,jdbcType=INTEGER}, #{periodUnit,jdbcType=INTEGER}, 
      #{type,jdbcType=INTEGER}, #{chargeType,jdbcType=INTEGER}, #{subType,jdbcType=INTEGER}, 
      #{serviceType,jdbcType=INTEGER}, #{businessCode,jdbcType=VARCHAR}, #{vipTypeCode,jdbcType=VARCHAR}, 
      #{sourceVipTypeCode,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{deadline,jdbcType=TIMESTAMP}, 
      #{url,jdbcType=VARCHAR}, #{supportExp,jdbcType=INTEGER}, #{supportType,jdbcType=VARCHAR}, 
      #{businessType,jdbcType=VARCHAR}, #{scope,jdbcType=INTEGER}, #{sourceSubType,jdbcType=INTEGER}, 
      #{rebuy,jdbcType=INTEGER}, #{timeType,jdbcType=INTEGER}, #{version,jdbcType=INTEGER}, 
      #{description,jdbcType=VARCHAR}, #{unit,jdbcType=TINYINT}, #{cooperatorUid,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{operator,jdbcType=VARCHAR}, 
      #{currencyUnit,jdbcType=VARCHAR}, #{currencySymbol,jdbcType=VARCHAR}, #{businessCharge,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.iqiyi.vip.present.model.PresentProduct">
    insert into present_product
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="nameTr != null">
        name_tr,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="originalPrice != null">
        original_price,
      </if>
      <if test="period != null">
        period,
      </if>
      <if test="periodUnit != null">
        period_unit,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="chargeType != null">
        charge_type,
      </if>
      <if test="subType != null">
        sub_type,
      </if>
      <if test="serviceType != null">
        service_type,
      </if>
      <if test="businessCode != null">
        business_code,
      </if>
      <if test="vipTypeCode != null">
        vip_type_code,
      </if>
      <if test="sourceVipTypeCode != null">
        source_vip_type_code,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="deadline != null">
        deadline,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="supportExp != null">
        support_exp,
      </if>
      <if test="supportType != null">
        support_type,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="scope != null">
        `scope`,
      </if>
      <if test="sourceSubType != null">
        source_sub_type,
      </if>
      <if test="rebuy != null">
        rebuy,
      </if>
      <if test="timeType != null">
        time_type,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="cooperatorUid != null">
        cooperator_uid,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="operator != null">
        `operator`,
      </if>
      <if test="currencyUnit != null">
        currency_unit,
      </if>
      <if test="currencySymbol != null">
        currency_symbol,
      </if>
      <if test="businessCharge != null">
        business_charge,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameTr != null">
        #{nameTr,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=INTEGER},
      </if>
      <if test="originalPrice != null">
        #{originalPrice,jdbcType=INTEGER},
      </if>
      <if test="period != null">
        #{period,jdbcType=INTEGER},
      </if>
      <if test="periodUnit != null">
        #{periodUnit,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="chargeType != null">
        #{chargeType,jdbcType=INTEGER},
      </if>
      <if test="subType != null">
        #{subType,jdbcType=INTEGER},
      </if>
      <if test="serviceType != null">
        #{serviceType,jdbcType=INTEGER},
      </if>
      <if test="businessCode != null">
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="vipTypeCode != null">
        #{vipTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="sourceVipTypeCode != null">
        #{sourceVipTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="deadline != null">
        #{deadline,jdbcType=TIMESTAMP},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="supportExp != null">
        #{supportExp,jdbcType=INTEGER},
      </if>
      <if test="supportType != null">
        #{supportType,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="scope != null">
        #{scope,jdbcType=INTEGER},
      </if>
      <if test="sourceSubType != null">
        #{sourceSubType,jdbcType=INTEGER},
      </if>
      <if test="rebuy != null">
        #{rebuy,jdbcType=INTEGER},
      </if>
      <if test="timeType != null">
        #{timeType,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=TINYINT},
      </if>
      <if test="cooperatorUid != null">
        #{cooperatorUid,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="currencyUnit != null">
        #{currencyUnit,jdbcType=VARCHAR},
      </if>
      <if test="currencySymbol != null">
        #{currencySymbol,jdbcType=VARCHAR},
      </if>
      <if test="businessCharge != null">
        #{businessCharge,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iqiyi.vip.present.model.PresentProduct">
    update present_product
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameTr != null">
        name_tr = #{nameTr,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=INTEGER},
      </if>
      <if test="originalPrice != null">
        original_price = #{originalPrice,jdbcType=INTEGER},
      </if>
      <if test="period != null">
        period = #{period,jdbcType=INTEGER},
      </if>
      <if test="periodUnit != null">
        period_unit = #{periodUnit,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="chargeType != null">
        charge_type = #{chargeType,jdbcType=INTEGER},
      </if>
      <if test="subType != null">
        sub_type = #{subType,jdbcType=INTEGER},
      </if>
      <if test="serviceType != null">
        service_type = #{serviceType,jdbcType=INTEGER},
      </if>
      <if test="businessCode != null">
        business_code = #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="vipTypeCode != null">
        vip_type_code = #{vipTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="sourceVipTypeCode != null">
        source_vip_type_code = #{sourceVipTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="deadline != null">
        deadline = #{deadline,jdbcType=TIMESTAMP},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="supportExp != null">
        support_exp = #{supportExp,jdbcType=INTEGER},
      </if>
      <if test="supportType != null">
        support_type = #{supportType,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="scope != null">
        `scope` = #{scope,jdbcType=INTEGER},
      </if>
      <if test="sourceSubType != null">
        source_sub_type = #{sourceSubType,jdbcType=INTEGER},
      </if>
      <if test="rebuy != null">
        rebuy = #{rebuy,jdbcType=INTEGER},
      </if>
      <if test="timeType != null">
        time_type = #{timeType,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=TINYINT},
      </if>
      <if test="cooperatorUid != null">
        cooperator_uid = #{cooperatorUid,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        `operator` = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="currencyUnit != null">
        currency_unit = #{currencyUnit,jdbcType=VARCHAR},
      </if>
      <if test="currencySymbol != null">
        currency_symbol = #{currencySymbol,jdbcType=VARCHAR},
      </if>
      <if test="businessCharge != null">
        business_charge = #{businessCharge,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateByCode" parameterType="com.iqiyi.vip.present.model.PresentProduct">
    update present_product
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameTr != null">
        name_tr = #{nameTr,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=INTEGER},
      </if>
      <if test="originalPrice != null">
        original_price = #{originalPrice,jdbcType=INTEGER},
      </if>
      <if test="period != null">
        period = #{period,jdbcType=INTEGER},
      </if>
      <if test="periodUnit != null">
        period_unit = #{periodUnit,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="chargeType != null">
        charge_type = #{chargeType,jdbcType=INTEGER},
      </if>
      <if test="subType != null">
        sub_type = #{subType,jdbcType=INTEGER},
      </if>
      <if test="serviceType != null">
        service_type = #{serviceType,jdbcType=INTEGER},
      </if>
      <if test="businessCode != null">
        business_code = #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="vipTypeCode != null">
        vip_type_code = #{vipTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="sourceVipTypeCode != null">
        source_vip_type_code = #{sourceVipTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="deadline != null">
        deadline = #{deadline,jdbcType=TIMESTAMP},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="supportExp != null">
        support_exp = #{supportExp,jdbcType=INTEGER},
      </if>
      <if test="supportType != null">
        support_type = #{supportType,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="scope != null">
        `scope` = #{scope,jdbcType=INTEGER},
      </if>
      <if test="sourceSubType != null">
        source_sub_type = #{sourceSubType,jdbcType=INTEGER},
      </if>
      <if test="rebuy != null">
        rebuy = #{rebuy,jdbcType=INTEGER},
      </if>
      <if test="timeType != null">
        time_type = #{timeType,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=TINYINT},
      </if>
      <if test="cooperatorUid != null">
        cooperator_uid = #{cooperatorUid,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        `operator` = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="currencyUnit != null">
        currency_unit = #{currencyUnit,jdbcType=VARCHAR},
      </if>
      <if test="currencySymbol != null">
        currency_symbol = #{currencySymbol,jdbcType=VARCHAR},
      </if>
      <if test="businessCharge != null">
        business_charge = #{businessCharge,jdbcType=VARCHAR},
      </if>
    </set>
    where code = #{code,jdbcType=VARCHAR}
  </update>


  <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.present.model.PresentProduct">
    update present_product
    set `name` = #{name,jdbcType=VARCHAR},
      name_tr = #{nameTr,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      price = #{price,jdbcType=INTEGER},
      original_price = #{originalPrice,jdbcType=INTEGER},
      period = #{period,jdbcType=INTEGER},
      period_unit = #{periodUnit,jdbcType=INTEGER},
      `type` = #{type,jdbcType=INTEGER},
      charge_type = #{chargeType,jdbcType=INTEGER},
      sub_type = #{subType,jdbcType=INTEGER},
      service_type = #{serviceType,jdbcType=INTEGER},
      business_code = #{businessCode,jdbcType=VARCHAR},
      vip_type_code = #{vipTypeCode,jdbcType=VARCHAR},
      source_vip_type_code = #{sourceVipTypeCode,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      deadline = #{deadline,jdbcType=TIMESTAMP},
      url = #{url,jdbcType=VARCHAR},
      support_exp = #{supportExp,jdbcType=INTEGER},
      support_type = #{supportType,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=VARCHAR},
      `scope` = #{scope,jdbcType=INTEGER},
      source_sub_type = #{sourceSubType,jdbcType=INTEGER},
      rebuy = #{rebuy,jdbcType=INTEGER},
      time_type = #{timeType,jdbcType=INTEGER},
      version = #{version,jdbcType=INTEGER},
      description = #{description,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=TINYINT},
      cooperator_uid = #{cooperatorUid,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      `operator` = #{operator,jdbcType=VARCHAR},
      currency_unit = #{currencyUnit,jdbcType=VARCHAR},
      currency_symbol = #{currencySymbol,jdbcType=VARCHAR},
      business_charge = #{businessCharge,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>