<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.present.mapper.PresentPerformMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.present.model.PresentPerform">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="is_directly_send" jdbcType="INTEGER" property="isDirectlySend"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="receive_type" jdbcType="INTEGER" property="receiveType"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, is_directly_send, amount, `status`, receive_type, update_time, create_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from present_perform
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from present_perform
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <insert id="insert" parameterType="com.iqiyi.vip.present.model.PresentPerform">
    insert into present_perform (id, is_directly_send, amount, 
      `status`, receive_type, update_time,
      create_time)
    values (#{id,jdbcType=BIGINT}, #{isDirectlySend,jdbcType=INTEGER}, #{amount,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{receiveType,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
    <insert id="insertSelective" parameterType="com.iqiyi.vip.present.model.PresentPerform">
        insert into present_perform
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="isDirectlySend != null">
                is_directly_send,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="receiveType != null">
                receive_type,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="isDirectlySend != null">
                #{isDirectlySend,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="receiveType != null">
                #{receiveType,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iqiyi.vip.present.model.PresentPerform">
        update present_perform
        <set>
            <if test="isDirectlySend != null">
                is_directly_send = #{isDirectlySend,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="receiveType != null">
                receive_type = #{receiveType,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.present.model.PresentPerform">
    update present_perform
    set is_directly_send = #{isDirectlySend,jdbcType=INTEGER},
      amount = #{amount,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      receive_type = #{receiveType,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryPerforms" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from present_perform
    where `status`=1
  </select>
</mapper>