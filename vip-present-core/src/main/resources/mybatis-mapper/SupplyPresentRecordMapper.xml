<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.present.mapper.SupplyPresentRecordMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.present.model.SupplyPresentRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="trade_code" jdbcType="VARCHAR" property="tradeCode"/>
        <result column="uid" jdbcType="BIGINT" property="uid"/>
        <result column="source" jdbcType="INTEGER" property="source"/>
        <result column="buy_type" jdbcType="INTEGER" property="buyType"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="order_code" jdbcType="VARCHAR" property="orderCode"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
        <result column="pay_type" jdbcType="INTEGER" property="payType"/>
        <result column="present_config_id" jdbcType="BIGINT" property="presentConfigId"/>
        <result column="present_condition_id" jdbcType="BIGINT" property="presentConditionId"/>
        <result column="deadline_start_time" jdbcType="TIMESTAMP" property="deadlineStartTime"/>
        <result column="deadline_end_time" jdbcType="TIMESTAMP" property="deadlineEndTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , trade_code
      , uid, `source`,buy_type,vip_type,`status`, order_code, amount, pay_type,present_config_id, present_condition_id, deadline_start_time,deadline_end_time,update_time, create_time
    </sql>

    <insert id="insertSelective" parameterType="com.iqiyi.vip.present.model.SupplyPresentRecord">
        insert into supply_present_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="tradeCode != null">
                trade_code,
            </if>
            <if test="uid != null">
                uid,
            </if>
            <if test="source != null">
                source,
            </if>
            <if test="buyType != null">
                buy_type,
            </if>
            <if test="vipType != null">
                vip_type,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="orderCode != null">
                order_code,
            </if>
            <if test="payType != null">
                pay_type,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="presentConfigId != null">
                present_config_id,
            </if>
            <if test="presentConditionId != null">
                present_condition_id,
            </if>
            <if test="deadlineStartTime != null">
                deadline_start_time,
            </if>
            <if test="deadlineEndTime != null">
                deadline_end_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="tradeCode != null">
                #{tradeCode,jdbcType=VARCHAR},
            </if>
            <if test="uid != null">
                #{uid,jdbcType=BIGINT},
            </if>
            <if test="source != null">
                #{source,jdbcType=INTEGER},
            </if>
            <if test="buyType != null">
                #{buyType,jdbcType=INTEGER},
            </if>
            <if test="vipType != null">
                #{vipType,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="orderCode != null">
                #{orderCode,jdbcType=VARCHAR},
            </if>
            <if test="payType != null">
                #{payType,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=INTEGER},
            </if>
            <if test="presentConfigId != null">
                #{presentConfigId,jdbcType=BIGINT},
            </if>
            <if test="presentConditionId != null">
                #{presentConditionId,jdbcType=BIGINT},
            </if>
            <if test="deadlineStartTime != null">
                #{deadlineStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deadlineEndTime != null">
                #{deadlineEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateSelective" parameterType="com.iqiyi.vip.present.model.SupplyPresentRecord">
        update supply_present_record
        <set>
            <if test="source != null">
                source = #{source,jdbcType=INTEGER},
            </if>
            <if test="tradeCode != null">
                trade_code= #{tradeCode,jdbcType=VARCHAR},
            </if>
            <if test="buyType != null">
                buy_type=#{buyType,jdbcType=INTEGER},
            </if>
            <if test="orderCode != null">
                order_code=#{orderCode,jdbcType=VARCHAR},
            </if>
            <if test="payType != null">
                pay_type=#{payType,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                amount=#{amount,jdbcType=INTEGER},
            </if>
            <if test="presentConfigId != null">
                present_config_id=#{presentConfigId,jdbcType=BIGINT},
            </if>
            <if test="presentConditionId != null">
                present_condition_id=#{presentConditionId,jdbcType=BIGINT},
            </if>
            <if test="deadlineStartTime != null">
                deadline_start_time=#{deadlineStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deadlineEndTime != null">
                deadline_end_time=#{deadlineEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where uid=#{uid ,jdbcType=BIGINT} and vip_type = #{vipType,jdbcType=INTEGER}
    </update>

    <select id="selectByUid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from supply_present_record
        where uid=#{uid}
    </select>

    <select id="selectByUserIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from supply_present_record
        where uid in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByUidAndSource" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from supply_present_record
        where uid=#{uid}
        and source=#{source}
    </select>
</mapper>