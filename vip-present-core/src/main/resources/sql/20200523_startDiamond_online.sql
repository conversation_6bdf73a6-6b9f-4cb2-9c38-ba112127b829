-- present_config
UPDATE present_config SET start_time='2020-05-23 4:00:00',update_time=NOW() WHERE id >=66 and id<=91;
UPDATE present_config SET end_time='2020-05-23 4:00:00',update_time=NOW() WHERE pay_type <>407;

-- vip-present
-- present_product
UPDATE present_product SET name_tr = REPLACE (name_tr,'鉆石','星鉆') WHERE name_tr IS NOT NULL AND id = 870;
UPDATE present_product SET name_tr = REPLACE (name_tr,'鉆石','星鉆') WHERE name_tr IS NOT NULL AND id = 872;


UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 870;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 872;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 873;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 4041;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 4105;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 4106;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 4107;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 10404;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 11847;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 16254;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 16507;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 16874;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 18421;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21645;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21646;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21648;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21650;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21652;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21654;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21656;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21658;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21660;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21662;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21664;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21666;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21668;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26005;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26007;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26206;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26208;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26210;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26293;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26294;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26296;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26298;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26301;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26303;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26305;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26307;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26309;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26312;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26314;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26316;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26318;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26320;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26322;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 27917;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 35081;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 40466;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 48820;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 48825;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 48826;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 48827;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 49479;
UPDATE present_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 52171;

-- partner-notification
-- partner_notify_promotion
UPDATE partner_notify_promotion SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 1;
UPDATE partner_notify_promotion SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 2;
UPDATE partner_notify_promotion SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 3;
UPDATE partner_notify_promotion SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 4;
UPDATE partner_notify_promotion SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 77;
UPDATE partner_notify_promotion SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 78;

-- partner-x
-- partner_qiyi_product
UPDATE partner_qiyi_product SET `name` = replace(name,'钻石','星钻') WHERE name IS NOT NULL AND partner_qiyi_product.id=31;
UPDATE partner_qiyi_product SET `name` = replace(name,'钻石','星钻') WHERE name IS NOT NULL AND partner_qiyi_product.id=38;
UPDATE partner_qiyi_product SET `name` = replace(name,'钻石','星钻') WHERE name IS NOT NULL AND partner_qiyi_product.id=39;
UPDATE partner_qiyi_product SET `name` = replace(name,'钻石','星钻') WHERE name IS NOT NULL AND partner_qiyi_product.id=40;
UPDATE partner_qiyi_product SET `name` = replace(name,'钻石','星钻') WHERE name IS NOT NULL AND partner_qiyi_product.id=47;
UPDATE partner_qiyi_product SET `name` = replace(name,'钻石','星钻') WHERE name IS NOT NULL AND partner_qiyi_product.id=48;
UPDATE partner_qiyi_product SET `name` = replace(name,'钻石','星钻') WHERE name IS NOT NULL AND partner_qiyi_product.id=49;
UPDATE partner_qiyi_product SET `name` = replace(name,'钻石','星钻') WHERE name IS NOT NULL AND partner_qiyi_product.id=57;
UPDATE partner_qiyi_product SET `name` = replace(name,'钻石','星钻') WHERE name IS NOT NULL AND partner_qiyi_product.id=67;

-- trade-card
-- boss_exp_card_batch_extra
UPDATE boss_exp_card_batch_extra SET pid_name = REPLACE (pid_name,'钻石','星钻') WHERE pid_name IS NOT NULL AND id = 16406;
UPDATE boss_exp_card_batch_extra SET pid_name = REPLACE (pid_name,'钻石','星钻') WHERE pid_name IS NOT NULL AND id = 16407;
UPDATE boss_exp_card_batch_extra SET pid_name = REPLACE (pid_name,'钻石','星钻') WHERE pid_name IS NOT NULL AND id = 16408;
UPDATE boss_exp_card_batch_extra SET pid_name = REPLACE (pid_name,'钻石','星钻') WHERE pid_name IS NOT NULL AND id = 16409;
UPDATE boss_exp_card_batch_extra SET pid_name = REPLACE (pid_name,'钻石','星钻') WHERE pid_name IS NOT NULL AND id = 16411;
UPDATE boss_exp_card_batch_extra SET pid_name = REPLACE (pid_name,'钻石','星钻') WHERE pid_name IS NOT NULL AND id = 16433;
UPDATE boss_exp_card_batch_extra SET pid_name = REPLACE (pid_name,'钻石','星钻') WHERE pid_name IS NOT NULL AND id = 16440;
UPDATE boss_exp_card_batch_extra SET pid_name = REPLACE (pid_name,'钻石','星钻') WHERE pid_name IS NOT NULL AND id = 16446;
UPDATE boss_exp_card_batch_extra SET pid_name = REPLACE (pid_name,'钻石','星钻') WHERE pid_name IS NOT NULL AND id = 16447;
UPDATE boss_exp_card_batch_extra SET pid_name = REPLACE (pid_name,'钻石','星钻') WHERE pid_name IS NOT NULL AND id = 16448;
UPDATE boss_exp_card_batch_extra SET pid_name = REPLACE (pid_name,'钻石','星钻') WHERE pid_name IS NOT NULL AND id = 16451;
UPDATE boss_exp_card_batch_extra SET pid_name = REPLACE (pid_name,'钻石','星钻') WHERE pid_name IS NOT NULL AND id = 16474;
UPDATE boss_exp_card_batch_extra SET pid_name = REPLACE (pid_name,'钻石','星钻') WHERE pid_name IS NOT NULL AND id = 16491;
UPDATE boss_exp_card_batch_extra SET pid_name = REPLACE (pid_name,'钻石','星钻') WHERE pid_name IS NOT NULL AND id = 16496;
UPDATE boss_exp_card_batch_extra SET pid_name = REPLACE (pid_name,'钻石','星钻') WHERE pid_name IS NOT NULL AND id = 16512;
UPDATE boss_exp_card_batch_extra SET pid_name = REPLACE (pid_name,'钻石','星钻') WHERE pid_name IS NOT NULL AND id = 16528;
UPDATE boss_exp_card_batch_extra SET pid_name = REPLACE (pid_name,'钻石','星钻') WHERE pid_name IS NOT NULL AND id = 16530;
UPDATE boss_exp_card_batch_extra SET pid_name = REPLACE (pid_name,'钻石','星钻') WHERE pid_name IS NOT NULL AND id = 16537;
UPDATE boss_exp_card_batch_extra SET pid_name = REPLACE (pid_name,'钻石','星钻') WHERE pid_name IS NOT NULL AND id = 16547;
UPDATE boss_exp_card_batch_extra SET pid_name = REPLACE (pid_name,'钻石','星钻') WHERE pid_name IS NOT NULL AND id = 16548;

UPDATE boss_exp_card_batch_extra SET promotion_name = REPLACE (promotion_name,'钻石','星钻') WHERE promotion_name IS NOT NULL AND id = 16406;
UPDATE boss_exp_card_batch_extra SET promotion_name = REPLACE (promotion_name,'钻石','星钻') WHERE promotion_name IS NOT NULL AND id = 16407;
UPDATE boss_exp_card_batch_extra SET promotion_name = REPLACE (promotion_name,'钻石','星钻') WHERE promotion_name IS NOT NULL AND id = 16408;
UPDATE boss_exp_card_batch_extra SET promotion_name = REPLACE (promotion_name,'钻石','星钻') WHERE promotion_name IS NOT NULL AND id = 16409;
UPDATE boss_exp_card_batch_extra SET promotion_name = REPLACE (promotion_name,'钻石','星钻') WHERE promotion_name IS NOT NULL AND id = 16411;
UPDATE boss_exp_card_batch_extra SET promotion_name = REPLACE (promotion_name,'钻石','星钻') WHERE promotion_name IS NOT NULL AND id = 16433;
UPDATE boss_exp_card_batch_extra SET promotion_name = REPLACE (promotion_name,'钻石','星钻') WHERE promotion_name IS NOT NULL AND id = 16440;
UPDATE boss_exp_card_batch_extra SET promotion_name = REPLACE (promotion_name,'钻石','星钻') WHERE promotion_name IS NOT NULL AND id = 16446;
UPDATE boss_exp_card_batch_extra SET promotion_name = REPLACE (promotion_name,'钻石','星钻') WHERE promotion_name IS NOT NULL AND id = 16447;
UPDATE boss_exp_card_batch_extra SET promotion_name = REPLACE (promotion_name,'钻石','星钻') WHERE promotion_name IS NOT NULL AND id = 16448;
UPDATE boss_exp_card_batch_extra SET promotion_name = REPLACE (promotion_name,'钻石','星钻') WHERE promotion_name IS NOT NULL AND id = 16451;
UPDATE boss_exp_card_batch_extra SET promotion_name = REPLACE (promotion_name,'钻石','星钻') WHERE promotion_name IS NOT NULL AND id = 16474;
UPDATE boss_exp_card_batch_extra SET promotion_name = REPLACE (promotion_name,'钻石','星钻') WHERE promotion_name IS NOT NULL AND id = 16491;
UPDATE boss_exp_card_batch_extra SET promotion_name = REPLACE (promotion_name,'钻石','星钻') WHERE promotion_name IS NOT NULL AND id = 16496;
UPDATE boss_exp_card_batch_extra SET promotion_name = REPLACE (promotion_name,'钻石','星钻') WHERE promotion_name IS NOT NULL AND id = 16512;
UPDATE boss_exp_card_batch_extra SET promotion_name = REPLACE (promotion_name,'钻石','星钻') WHERE promotion_name IS NOT NULL AND id = 16528;
UPDATE boss_exp_card_batch_extra SET promotion_name = REPLACE (promotion_name,'钻石','星钻') WHERE promotion_name IS NOT NULL AND id = 16530;
UPDATE boss_exp_card_batch_extra SET promotion_name = REPLACE (promotion_name,'钻石','星钻') WHERE promotion_name IS NOT NULL AND id = 16537;
UPDATE boss_exp_card_batch_extra SET promotion_name = REPLACE (promotion_name,'钻石','星钻') WHERE promotion_name IS NOT NULL AND id = 16547;
UPDATE boss_exp_card_batch_extra SET promotion_name = REPLACE (promotion_name,'钻石','星钻') WHERE promotion_name IS NOT NULL AND id = 16548;


-- boss_vip_type
UPDATE boss_vip_type SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 4;
UPDATE boss_vip_type SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 18;
UPDATE boss_vip_type SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 19;
UPDATE boss_vip_type SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21;
UPDATE boss_vip_type SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 23;
UPDATE boss_vip_type SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 25;
UPDATE boss_vip_type SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 27;
UPDATE boss_vip_type SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 29;
UPDATE boss_vip_type SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 31;
UPDATE boss_vip_type SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 33;
UPDATE boss_vip_type SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 35;
UPDATE boss_vip_type SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 37;
UPDATE boss_vip_type SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 39;
UPDATE boss_vip_type SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 41;
UPDATE boss_vip_type SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 45;
UPDATE boss_vip_type SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 47;
UPDATE boss_vip_type SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 52;


-- qiyue_product_new
UPDATE qiyue_product_new SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 870;
UPDATE qiyue_product_new SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 872;


-- exp_card_product
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 870;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 872;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 873;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 4041;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 4105;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 4106;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 4107;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 4114;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 4115;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 4116;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 10404;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 11847;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 16254;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 16507;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 16874;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 18421;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21645;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21646;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21648;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21650;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21652;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21654;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21656;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21658;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21660;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21662;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21664;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21666;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 21668;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26005;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26007;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26206;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26208;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26210;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26293;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26294;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26296;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26298;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26301;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26303;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26305;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26307;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26309;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26312;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26314;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26316;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26318;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26320;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 26322;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 27917;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 30267;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 30268;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 30269;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 31282;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 31583;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 31584;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 31585;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 35081;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 35273;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 40411;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 40412;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 40416;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 40418;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 40466;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 49201;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 49206;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 49207;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 49208;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 49860;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 50688;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 52552;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 54545;
UPDATE exp_card_product SET `name` = REPLACE (name,'钻石','星钻') WHERE name IS NOT NULL AND id = 54548;

