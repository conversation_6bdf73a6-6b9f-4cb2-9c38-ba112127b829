INSERT INTO present_condition(`condition`,status,priority,update_time,create_time,perform_id) VALUES('${aid}!=null',1,1,NOW(),NOW(),1);

// 要分开执行 先获取 present_condition 的主键ID

INSERT INTO present_config(bvip_type,pvip_type,present_code,remark,status,handler_status,grouping,update_time,create_time,pay_type,condition_ids,start_time,end_time)
VALUES('single','single','single','单点多语言赠送',1,1,'multilingualVod',NOW(),NOW(),'410',!!!conditionId!!!,NOW(),'2099-12-31 00:00:00');

CREATE TABLE `present_single_pid` (
  `id` bigint(32) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pid` varchar(64) NOT NULL COMMENT '产品pid',
  `name` varchar(200) DEFAULT NULL COMMENT '产品名',
  `description` text COMMENT '描述',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `PID_UNIQUE_INDEX` (`pid`) USING BTREE COMMENT 'pid唯一索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='单点pid列表';
