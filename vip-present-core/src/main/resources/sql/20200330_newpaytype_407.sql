-- 将4月12日将要上线的pay_type改为新值
UPDATE `present_config` SET `pay_type`='407', `update_time`='2020-03-30 15:00:00' WHERE `id` IN (66,67,68,69,70,71,72,73,74,75,76,77);

-- 将所有老的配置的结束时间改为4月12日凌晨
UPDATE `present_config` SET `update_time`='2020-03-30 15:00:00', `end_time`='2020-04-12 00:00:00' WHERE `id` < 66;

-- 新增目前有效的配置并将pay_type置为新值407，开始时间置为4月12日
INSERT INTO `present_config` (`bvip_type`, `pvip_type`, `buy_code`, `present_code`, `remark`, `status`, `handler_status`, `grouping`, `update_time`, `create_time`, `pay_type`, `condition_ids`, `cal_amount_type`, `start_time`, `end_time`) VALUES ('4', '1', '', '8ea0996f0444a283', '买钻石送黄金', '1', '1', 'g', '2020-03-30 15:00:00', '2020-03-30 15:00:00', '407', '2', '0', '2020-04-12 00:00:00', NULL);
INSERT INTO `present_config` (`bvip_type`, `pvip_type`, `buy_code`, `present_code`, `remark`, `status`, `handler_status`, `grouping`, `update_time`, `create_time`, `pay_type`, `condition_ids`, `cal_amount_type`, `start_time`, `end_time`) VALUES ('4', '5', '', '9adf62b49e06fe8d', '买钻石送奇异果', '1', '1', 'g', '2020-03-30 15:00:00', '2020-03-30 15:00:00', '407', '40', '0', '2020-04-12 00:00:00', NULL);

INSERT INTO `present_config` (`bvip_type`, `pvip_type`, `buy_code`, `present_code`, `remark`, `status`, `handler_status`, `grouping`, `update_time`, `create_time`, `pay_type`, `condition_ids`, `cal_amount_type`, `start_time`, `end_time`) VALUES ('1', '16', '', '919a2ea53ffa9779', '买黄金送学生', '1', '1', 'a', '2020-03-30 15:00:00', '2020-03-30 15:00:00', '407', '1', '0', '2020-04-12 00:00:00', NULL);
INSERT INTO `present_config` (`bvip_type`, `pvip_type`, `buy_code`, `present_code`, `remark`, `status`, `handler_status`, `grouping`, `update_time`, `create_time`, `pay_type`, `condition_ids`, `cal_amount_type`, `start_time`, `end_time`) VALUES ('5', '1', '', 'a47bac390c51df6a', '小米SDK-买奇异果赠送黄金', '1', '1', 'h', '2020-03-30 15:00:00', '2020-03-30 15:00:00', '407', '3', '0', '2020-04-12 00:00:00', '2050-01-31 23:59:59');
INSERT INTO `present_config` (`bvip_type`, `pvip_type`, `buy_code`, `present_code`, `remark`, `status`, `handler_status`, `grouping`, `update_time`, `create_time`, `pay_type`, `condition_ids`, `cal_amount_type`, `start_time`, `end_time`) VALUES ('5', '1', '', 'a47bac390c51df6a', '升级代扣奇异果产品自动赠送等时长黄金', '1', '1', 'd', '2020-03-30 15:00:00', '2020-03-30 15:00:00', '407', '4', '0', '2020-04-12 00:00:00', '2050-01-31 23:59:59');
INSERT INTO `present_config` (`bvip_type`, `pvip_type`, `buy_code`, `present_code`, `remark`, `status`, `handler_status`, `grouping`, `update_time`, `create_time`, `pay_type`, `condition_ids`, `cal_amount_type`, `start_time`, `end_time`) VALUES ('5', '1', '9ff1a15abb9b50b8', 'a0226bd958843452', '视云OTT送等时长黄金', '1', '1', 'e', '2020-03-30 15:00:00', '2020-03-30 15:00:00', '407', '5', '1', '2020-04-12 00:00:00', NULL);
INSERT INTO `present_config` (`bvip_type`, `pvip_type`, `buy_code`, `present_code`, `remark`, `status`, `handler_status`, `grouping`, `update_time`, `create_time`, `pay_type`, `condition_ids`, `cal_amount_type`, `start_time`, `end_time`) VALUES ('5', '1', '9fe02104b37493c9', 'a0226bd958843452', '秒杀奇异果自动赠送黄金会员', '1', '1', 'f', '2020-03-30 15:00:00', '2020-03-30 15:00:00', '407', '6', '1', '2020-04-12 00:00:00', NULL);
INSERT INTO `present_config` (`bvip_type`, `pvip_type`, `buy_code`, `present_code`, `remark`, `status`, `handler_status`, `grouping`, `update_time`, `create_time`, `pay_type`, `condition_ids`, `cal_amount_type`, `start_time`, `end_time`) VALUES ('7', '8', 'a618504709bda074', 'b9b7fa043da7f825', '买主站网球会员赠TV网球会员（天-天）', '1', '1', 'm', '2020-03-30 15:00:00', '2020-03-30 15:00:00', '407', '21', '0', '2020-04-12 00:00:00', NULL);
INSERT INTO `present_config` (`bvip_type`, `pvip_type`, `buy_code`, `present_code`, `remark`, `status`, `handler_status`, `grouping`, `update_time`, `create_time`, `pay_type`, `condition_ids`, `cal_amount_type`, `start_time`, `end_time`) VALUES ('7', '8', '8f1952f47854f13b', 'b9b7fa043da7f825', '买主站网球会员赠TV网球会员(月-天)', '1', '1', 'm', '2020-03-30 15:00:00', '2020-03-30 15:00:00', '407', '22', '0', '2020-04-12 00:00:00', NULL);
INSERT INTO `present_config` (`bvip_type`, `pvip_type`, `buy_code`, `present_code`, `remark`, `status`, `handler_status`, `grouping`, `update_time`, `create_time`, `pay_type`, `condition_ids`, `cal_amount_type`, `start_time`, `end_time`) VALUES ('8', '7', '924a937978c14030', 'a618504709bda074', '买TV网球会员赠主站网球会员(月-天)', '1', '1', 'n', '2020-03-30 15:00:00', '2020-03-30 15:00:00', '407', '23', '0', '2020-04-12 00:00:00', NULL);
INSERT INTO `present_config` (`bvip_type`, `pvip_type`, `buy_code`, `present_code`, `remark`, `status`, `handler_status`, `grouping`, `update_time`, `create_time`, `pay_type`, `condition_ids`, `cal_amount_type`, `start_time`, `end_time`) VALUES ('8', '7', 'b9b7fa043da7f825', 'a618504709bda074', '买TV网球会员赠主站网球会员（天-天）', '1', '1', 'n', '2020-03-30 15:00:00', '2020-03-30 15:00:00', '407', '24', '0', '2020-04-12 00:00:00', NULL);
INSERT INTO `present_config` (`bvip_type`, `pvip_type`, `buy_code`, `present_code`, `remark`, `status`, `handler_status`, `grouping`, `update_time`, `create_time`, `pay_type`, `condition_ids`, `cal_amount_type`, `start_time`, `end_time`) VALUES ('50', '1', '', 'a47bac390c51df6a', 'VR会员送黄金', '1', '1', 'vr', '2020-03-30 15:00:00', '2020-03-30 15:00:00', '407', '48', '0', '2020-04-12 00:00:00', NULL);
INSERT INTO `present_config` (`bvip_type`, `pvip_type`, `buy_code`, `present_code`, `remark`, `status`, `handler_status`, `grouping`, `update_time`, `create_time`, `pay_type`, `condition_ids`, `cal_amount_type`, `start_time`, `end_time`) VALUES ('5', '1', '', 'a47bac390c51df6a', '大连广电奇异果送黄金', '1', '1', 'q', '2020-03-30 15:00:00', '2020-03-30 15:00:00', '407', '49', '0', '2020-04-12 00:00:00', NULL);
INSERT INTO `present_config` (`bvip_type`, `pvip_type`, `buy_code`, `present_code`, `remark`, `status`, `handler_status`, `grouping`, `update_time`, `create_time`, `pay_type`, `condition_ids`, `cal_amount_type`, `start_time`, `end_time`) VALUES ('5', '1', '', 'a47bac390c51df6a', '奇异果送黄金', '1', '1', 'p', '2020-03-30 15:00:00', '2020-03-30 15:00:00', '407', '50', '0', '2020-04-12 00:00:00', NULL);
