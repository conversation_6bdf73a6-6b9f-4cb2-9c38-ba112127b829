package com.iqiyi.vip.present.config;

import com.iqiyi.vip.TaskProperties;
import com.iqiyi.vip.monitor.CompositeMonitorReporter;
import com.iqiyi.vip.monitor.DefaultTaskErrorHandler;
import com.iqiyi.vip.monitor.ZkMonitorReporter;
import com.iqiyi.vip.repository.ClusterAsyncTaskDao;
import com.iqiyi.vip.repository.ClusterAsyncTaskManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Collections;

/**
 * 异步任务配置类
 * <AUTHOR>
 */
@Configuration
public class AsyncTaskConfig {

    @Value("${async.task.table.name:async_task}")
    private String asyncTaskTable;



    @Bean("clusterAsyncTaskDao")
    public ClusterAsyncTaskDao clusterAsyncTaskDao(@Qualifier("jdbcTemplate") JdbcTemplate jdbcTemplate) {
        ClusterAsyncTaskDao clusterAsyncTaskDao = new ClusterAsyncTaskDao(asyncTaskTable);
        clusterAsyncTaskDao.setJdbcTemplate(jdbcTemplate);
        return clusterAsyncTaskDao;
    }

    @Bean("clusterAsyncTaskManager")
    @Primary
    public ClusterAsyncTaskManager clusterAsyncTaskManager(@Qualifier("clusterAsyncTaskDao") ClusterAsyncTaskDao clusterAsyncTaskDao) {
        return new ClusterAsyncTaskManager(clusterAsyncTaskDao, new DefaultTaskErrorHandler());
    }

    @Bean
    @ConditionalOnMissingBean(TaskProperties.class)
    public TaskProperties taskProperties(){
        return new TaskProperties();
    }

    @Bean
    @Primary
    public ZkMonitorReporter zkMonitorReporter(){
        return new ZkMonitorReporter();
    }

    @Bean("compositeMonitorReport")
    @Primary
    public CompositeMonitorReporter compositeMonitorReporter(){
        return new CompositeMonitorReporter(Collections.EMPTY_LIST);
    }
}
