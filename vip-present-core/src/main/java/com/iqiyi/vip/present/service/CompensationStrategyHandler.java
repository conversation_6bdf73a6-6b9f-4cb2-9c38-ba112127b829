package com.iqiyi.vip.present.service;


import com.iqiyi.vip.present.entity.Order;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.model.dto.CompensateDTO;

public interface CompensationStrategyHandler {

    void compensate(CompensateDTO compensateDTO);

    /**
     * 构建查询参数
     * 不同的策略处理器可以根据自己的业务逻辑构建不同的查询参数
     *
     * @param order 订单信息
     * @return 查询参数
     */
    PresentOrder buildQueryPresentOrderParam(Order order);
}

