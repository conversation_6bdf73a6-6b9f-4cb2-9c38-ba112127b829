package com.iqiyi.vip.present.enums;

/**
 * 补偿类型枚举
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public enum CompensationType {
    
    /**
     * 订单赠送补偿
     */
    ORDER_PRESENT("orderPresentCheck"),
    
    /**
     * 订单取消补偿
     */
    ORDER_CANCEL("orderCancelCheck"),
    
    /**
     * 单点订单赠送补偿
     */
    VOD_ORDER_PRESENT("vodOrderPresentCheck");
    
    private final String code;
    
    CompensationType(String code) {
        this.code = code;
    }
    
    public String getCode() {
        return code;
    }
    
    /**
     * 根据code获取补偿类型
     * 
     * @param code 代码
     * @return 补偿类型
     * @throws IllegalArgumentException 如果找不到对应的补偿类型
     */
    public static CompensationType fromCode(String code) {
        for (CompensationType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown compensation type code: " + code);
    }

}
