package com.iqiyi.vip.present.api;

import com.iqiyi.vip.present.apiresponse.BaseResponse;
import com.iqiyi.vip.present.consts.EnumResultCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Map;


/**
 * Created at: 2020-12-28
 *
 * <AUTHOR>
 */
@Slf4j
public class BaseApi {

    public static String buildQueryUrl(String requestUrl, Map<String, Object> param) {
        try {
            URIBuilder uriBuilder = new URIBuilder(requestUrl);
            for (Map.Entry<String, Object> entry : param.entrySet()) {
                uriBuilder.addParameter(entry.getKey(), String.valueOf(entry.getValue()));
            }
            URI uri = uriBuilder.build();
            return uri.toString();
        } catch (URISyntaxException e) {
            throw new RuntimeException("Failed to build URI", e);
        }
    }

    protected <T> BaseResponse<T> doGet(RestTemplate restTemplate, String requestUrl, Map<String, Object> param, String logHeader, ParameterizedTypeReference<BaseResponse<T>> typeReference) {
        String urlWithParam = buildQueryUrl(requestUrl, param);
        ResponseEntity<BaseResponse<T>> responseEntity = null;
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            responseEntity = restTemplate.exchange(urlWithParam, HttpMethod.GET, null, typeReference);
        } catch (Exception e) {
            log.error(logHeader + "失败，url: {}, cost: {}", urlWithParam, stopWatch.getTime(), e);
            return new BaseResponse<>(EnumResultCode.OUTER_SERVICE_ERROR);
        }
        if (responseEntity == null || responseEntity.getStatusCode() != HttpStatus.OK) {
            int statusCode = responseEntity != null ? responseEntity.getStatusCodeValue() : 0;
            log.error(logHeader + "失败，url: {}, httpStatusCode: {}, cost: {}", urlWithParam, statusCode, stopWatch.getTime());
            return new BaseResponse<>(EnumResultCode.OUTER_SERVICE_ERROR);
        }
        BaseResponse<T> responseBody = responseEntity.getBody();
        log.info(logHeader + "成功，url: {}, cost: {}, result: {}", urlWithParam, stopWatch.getTime(), responseBody);
        return responseBody;
    }

    protected <P, R> BaseResponse<R> doPostByJson(RestTemplate restTemplate, String requestUrl, P body, String logHeader, ParameterizedTypeReference<BaseResponse<R>> typeReference) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<P> requestEntity = new HttpEntity<>(body, headers);
        ResponseEntity<BaseResponse<R>> responseEntity = null;
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            responseEntity = restTemplate.exchange(requestUrl, HttpMethod.POST, requestEntity, typeReference);
        } catch (Exception e) {
            log.error(logHeader + "失败，url: {}, cost: {}, param: {}", requestUrl, stopWatch.getTime(), body, e);
            return new BaseResponse<>(EnumResultCode.OUTER_SERVICE_ERROR);
        }
        if (responseEntity == null || responseEntity.getStatusCode() != HttpStatus.OK) {
            int statusCode = responseEntity != null ? responseEntity.getStatusCodeValue() : 0;
            log.error(logHeader + "失败，url: {}, httpStatusCode: {}, cost: {}, param: {}", requestUrl, statusCode, stopWatch.getTime(), body);
            return new BaseResponse<>(EnumResultCode.OUTER_SERVICE_ERROR);
        }
        BaseResponse<R> responseBody = responseEntity.getBody();
        log.info(logHeader + "成功，url: {}, cost: {}, param: {}, result: {}", requestUrl, stopWatch.getTime(), body, responseBody);
        return responseBody;
    }

    protected <T> BaseResponse<T> doPostByFormData(RestTemplate restTemplate, String requestUrl, Map<String, String> param, String logHeader, ParameterizedTypeReference<BaseResponse<T>> typeReference) {
        return doPostByFormDataWithUrlVariables(restTemplate, requestUrl, param, logHeader, typeReference);
    }

    protected <T> BaseResponse<T> doPostByFormDataWithUrlVariables(RestTemplate restTemplate, String requestUrl, Map<String, String> param, String logHeader, ParameterizedTypeReference<BaseResponse<T>> typeReference, Object... uriVariables) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.setAll(param);
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(formData, headers);
        ResponseEntity<BaseResponse<T>> responseEntity = null;
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            responseEntity = restTemplate.exchange(requestUrl, HttpMethod.POST, requestEntity, typeReference, uriVariables);
        } catch (Exception e) {
            log.error(logHeader + "失败，url: {}, cost: {}, param: {}", requestUrl, stopWatch.getTime(), param, e);
            return new BaseResponse<>(EnumResultCode.OUTER_SERVICE_ERROR);
        }
        if (responseEntity == null || responseEntity.getStatusCode() != HttpStatus.OK) {
            int statusCode = responseEntity != null ? responseEntity.getStatusCodeValue() : 0;
            log.error(logHeader + "失败，url: {}, httpStatusCode: {}, cost: {}, param: {}", requestUrl, statusCode, stopWatch.getTime(), param);
            return new BaseResponse<>(EnumResultCode.OUTER_SERVICE_ERROR);
        }
        BaseResponse<T> responseBody = responseEntity.getBody();
        log.info(logHeader + "成功，url: {}, cost: {}, param: {}, result: {}", requestUrl, stopWatch.getTime(), param, responseBody);
        return responseBody;
    }

}
