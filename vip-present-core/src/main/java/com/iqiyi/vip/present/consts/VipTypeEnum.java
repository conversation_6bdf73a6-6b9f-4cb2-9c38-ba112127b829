package com.iqiyi.vip.present.consts;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version v 1.0 2018/8/17 14:49
 */
@AllArgsConstructor
@Getter
public enum VipTypeEnum {
    /**
     * 会员类型枚举
     */
    gold(1, "黄金"),

    silver(3, "白银"),

    diamonds(4, "钻石"),

    kiwi(5, "奇异果"),

    tennis(7, "主站网球"),

    tv_tennis(8, "tv网球"),

    child(10, "奇巴布"),

    fun(13, "FUN会员"),

    sport(14, "体育会员"),

    tv_sport(15, "TV体育会员"),

    youth(16, "学生会员"),

    vr(50, "VR会员"),

    kiwi_diamonds(54, "奇异果钻石"),

    qiyu(55, "奇遇会员"),
    basic(56, "极速版VIP会员"),

    /**
     * 会员没有统一创建文学会员的身份标识，所以买赠自设了个大值
     */
    book(999, "文学会员"),


    /**
     * 国际化
     */
    global_gold(19, "国际通用-黄金"),
    global_diamonds(20, "国际通用-钻石"),

    american_diamonds(21, "美国-钻石"),
    american_gold(22, "美国-黄金"),

    canada_diamonds(23, "加拿大-钻石"),
    canada_gold(24, "加拿大-黄金"),

    thailand_diamonds(25, "泰国-钻石"),
    thailand_gold(26, "泰国-黄金"),

    philippine_diamonds(27, "菲律宾-钻石"),
    philippine_gold(28, "菲律宾-黄金"),

    malaysia_diamonds(29, "马来西亚-钻石"),
    malaysia_gold(30, "马来西亚-黄金"),

    laos_diamonds(31, "老挝-钻石"),
    laos_gold(32, "老挝-黄金"),

    indonesia_diamonds(33, "印度尼西亚-钻石"),
    indonesia_gold(34, "印度尼西亚-黄金"),

    cambodia_diamonds(35, "柬埔寨-钻石"),
    cambodia_gold(36, "柬埔寨-黄金"),

    brunei_diamonds(37, "文莱-钻石"),
    brunei_gold(38, "文莱-黄金"),

    vietnam_diamonds(39, "越南-钻石"),
    vietnam_gold(40, "越南-黄金"),

    singapore_diamonds(41, "新加坡-钻石"),
    singapore_gold(42, "新加坡-黄金"),

    burma_diamonds(43, "缅甸-钻石"),
    burma_gold(44, "缅甸-黄金"),

    hk_gold(46, "香港-黄金"),
    hk_diamonds(47, "香港-钻石"),

    macao_gold(48, "澳门-黄金"),
    macao_diamonds(49, "澳门-钻石"),

    IQIYI_PLATINUM(58, "爱奇艺白金"),
    kiwi_gold(57, "奇异果专享"),
    kiwi_base(60, " 奇异果基础会员"),
    ;

    private int vipType;
    private String vipTypeName;

    public static String getVipTypeName(int vipType) {
        for (VipTypeEnum vipTypeEnum : VipTypeEnum.values()) {
            if (vipType == vipTypeEnum.getVipType()) {
                return vipTypeEnum.getVipTypeName();
            }
        }
        return null;
    }

}
