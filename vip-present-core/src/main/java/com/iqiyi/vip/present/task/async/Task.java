package com.iqiyi.vip.present.task.async;

import com.iqiyi.vip.present.dao.PresentAsyncTaskDao;

/**
 * Created at: 2022-04-12
 *
 * <AUTHOR>
 */
public interface Task extends Runnable {

    String[] RETRY_TIME = {"5s", "10s", "1m", "5m", "10m", "30m", "1h", "3h", "6h", "12h"};

    void deserialize(String data) throws IllegalArgumentException;

    String serialize();

    /**
     * 数据库持久化id
     */
    Long getPersistenceId();

    void setPersistenceId(Long persistenceId);

    void setExecCount(Integer execCount);

    Integer getExecCount();

    void setAsyncTaskDao(PresentAsyncTaskDao asyncTaskDao);

    PresentAsyncTaskDao getAsyncTaskDao();

}
