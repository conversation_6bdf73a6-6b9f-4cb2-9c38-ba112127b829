package com.iqiyi.vip.present.mapper;

import java.util.List;

import com.iqiyi.vip.present.model.PresentRecord;

public interface PresentRecordMapper extends BaseMapper {

    int deleteByPrimaryKey(Long id);

    int insert(PresentRecord record);

    int insertSelective(PresentRecord record);

    PresentRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PresentRecord record);

    int updateByPrimaryKey(PresentRecord record);

    List<PresentRecord> queryRecordByUidAndVipType(PresentRecord record);
}