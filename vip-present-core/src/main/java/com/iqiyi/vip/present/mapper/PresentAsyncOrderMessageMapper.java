package com.iqiyi.vip.present.mapper;

import com.iqiyi.vip.present.model.PresentAsyncOrderMessage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PresentAsyncOrderMessageMapper extends BaseMapper{

    int insertSelective(PresentAsyncOrderMessage message);

    int delete(@Param("orderCode") String orderCode);

    List<PresentAsyncOrderMessage> queryMessages(@Param("uid") Long uid, @Param("productSubType") Integer productSubType);
}