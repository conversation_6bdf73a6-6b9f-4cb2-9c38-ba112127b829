package com.iqiyi.vip.present.service;

import com.iqiyi.vip.present.model.PresentCondition;

import java.util.List;
import java.util.Map;

public interface PresentConditionService {
    PresentCondition queryConditionById(Long conditionId);

    List<PresentCondition> queryConditionByIds(String conditionIds);

    List<PresentCondition> queryConditionFromCacheByIds(String conditionIds);

    Map<Long, PresentCondition> queryConditions();
}
