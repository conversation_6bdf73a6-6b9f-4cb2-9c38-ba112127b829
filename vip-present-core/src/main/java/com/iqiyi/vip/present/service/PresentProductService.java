package com.iqiyi.vip.present.service;

import com.iqiyi.vip.present.model.PresentProduct;

import java.util.List;

/**
 * 产品新增
 *
 * <AUTHOR>
 * @date 2019/12/16 15:38
 */
public interface PresentProductService {

    /**
     * 新增产品信息
     * @param product
     * @return
     */
    void insertProduct(PresentProduct product);

    /**
     * 更新产品信息
     * @param product
     * @return
     */
    int updateProduct(PresentProduct product);

    /**
     * 新增或修改
     */
    int saveOrUpdate(PresentProduct product);

    /**
     * 查询code
     * @param code
     * @return
     */
    PresentProduct getByCode(String code);

    PresentProduct getById(Long id);

    /**
     * 查询指定类型pid
     */
    List<String> getCodesByVipType(String viptype);

    /**
     * 根据id查询code
     * @param id
     * @return
     */
    String getCodeById(Long id);
}