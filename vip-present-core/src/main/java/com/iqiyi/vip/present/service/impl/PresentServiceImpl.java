package com.iqiyi.vip.present.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.iqiyi.vip.present.api.PassportApi;
import com.iqiyi.vip.present.apirequest.PresentRequest;
import com.iqiyi.vip.present.apiresponse.BaseResponse;
import com.iqiyi.vip.present.apiresponse.PresentResponse;
import com.iqiyi.vip.present.consts.EnumOrderStatusCode;
import com.iqiyi.vip.present.consts.EnumOrderTypeCode;
import com.iqiyi.vip.present.consts.EnumResultCode;
import com.iqiyi.vip.present.consts.VipTypeEnum;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.data.PresentOrderData;
import com.iqiyi.vip.present.model.PresentCondition;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.model.PresentRecord;
import com.iqiyi.vip.present.out.PresentVipRequest;
import com.iqiyi.vip.present.service.*;
import com.iqiyi.vip.present.task.PresentVipAsyncTask;
import com.iqiyi.vip.present.utils.*;
import com.iqiyi.vip.repository.ClusterAsyncTaskManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

@Service
@Slf4j
public class PresentServiceImpl implements PresentService {

//    @Value("${dopay.url}")
//    private String dopayUrl;
//    @Value("${internal.pay.key}")
//    private String payKey;

    @Autowired
    private PresentRecordService presentRecordService;
    @Autowired
    private PresentOrderService presentOrderService;

    @Autowired
    private PassportApi passportApi;

    @Autowired
    private PresentConfigService presentConfigService;

    @Autowired
    private PresentPerformService presentPerformService;

    @Autowired
    private PresentConditionService presentConditionService;

    @Autowired
    private ClusterAsyncTaskManager asyncTaskManager;

    public static Map<String, List<PresentConfig>> configMap = null;

    public static Map<Long, PresentCondition> conditionMap = null;


    @Override
    public void validateParam(PresentRequest request, BaseResponse response) {
        if (StringUtils.isEmpty(request.getVipType())) {
            response.setCode(EnumResultCode.PARAM.getCode());
            response.setMsg("viptype为空");
        }
    }

    @Override
    public Long getUid(HttpServletRequest servletRequest, BaseResponse response, PresentRequest request) {
        if (request.getUid() != null) {
            return request.getUid();
        }

        String ticket = request.getP00001();
        if (StringUtils.isBlank(ticket)) {
            log.warn("[getUid][PresentRequest:{}]uid和p000001都为空",JSONObject.toJSONString(request));
            response.setCode(EnumResultCode.PARAM.getCode());
            response.setMsg("uid和p000001都为空");
            return null;
        }
        //根据p00001获取uid
        return passportApi.getUid(ticket);
    }

    /**
     * 根据uid，bviptype，pviptype，orderType，而pviptype根据config查询
     */
    @Override
    public List<PresentOrderData> queryVip(PresentRequest request, BaseResponse<List<PresentResponse>> response) {
        List<PresentConfig> allConfigList = presentConfigService.queryConfigListByVipType(request.getVipType());
        if (CollectionUtils.isEmpty(allConfigList)) {
            response.setResultCode(EnumResultCode.ERROR_CONFIG);
            return Lists.newArrayList();
        }

        List<PresentOrderData> orderDataList = Lists.newArrayList();
        List<PresentResponse> presentResponseList = Lists.newArrayList();
        for (PresentConfig config : allConfigList) {
            PresentOrder presentOrderRequest = new PresentOrder();
            presentOrderRequest.setPresentConfigId(config.getId());
            presentOrderRequest.setUid(request.getUid());
            presentOrderRequest.setOrderType(EnumOrderTypeCode.YES.getCode());
            presentOrderRequest.setStatus(EnumOrderStatusCode.NOT_RECEIVE.getCode());
            List<PresentOrder> orderList = presentOrderService.queryOrderByParams(presentOrderRequest);
            if (CollectionUtils.isEmpty(orderList)) {
                continue;
            }

            Integer day = this.queryTimeByMixTime(orderList, orderDataList, config);
            if (day == 0) {
                continue;
            }
            PresentResponse presentResponse = new PresentResponse();
            presentResponse.setBvipType(Integer.valueOf(config.getBvipType()));
            presentResponse.setVipType(Integer.valueOf(config.getPvipType()));
            presentResponse.setPresentCode(config.getPresentCode());
            presentResponse.setDay(day);
            presentResponse.setRemark(config.getRemark());
            presentResponseList.add(presentResponse);
        }

        if (CollectionUtils.isEmpty(presentResponseList)) {
            response.setResultCode(EnumResultCode.NO_PRESENT);
            return Lists.newArrayList();
        }
        response.setData(presentResponseList);
        return orderDataList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void presentVip(PresentRequest request, BaseResponse response, List<PresentOrderData> orderDataList) {
        for (PresentOrderData orderData : orderDataList) {
            // 保存present_order表
            PresentOrder order = new PresentOrder();
            BeanUtils.copyProperties(orderData, order);
            order.setReceiveTime(new Date());
            order.setStatus(EnumOrderStatusCode.PRESENTING.getCode());
            int updateRow = presentOrderService.updateOrderStatus(order, EnumOrderStatusCode.NOT_RECEIVE.getCode());
            if (updateRow == 0) {
                log.info("[presentVip][orderId:{} has present]", order.getId());
                continue;
            }

            // 保存present_record表
            PresentRecord record = new PresentRecord();
            record.setMsgId(request.getMessageId());
            record.setBuyUid(orderData.getUid());
            record.setReceiveUid(String.valueOf(request.getUid()));
            record.setBuyType(orderData.getBuyType());
            record.setPresentType(orderData.getPresentType());
            record.setPresentTradeCode(orderData.getPresentTradeCode());
            record.setBuyCode(orderData.getBuyCode());
            record.setPresentCode(orderData.getPresentCode());
            record.setCreateTime(new Date());
            record.setUpdateTime(new Date());
            presentRecordService.insertRecord(record);

            // 保存sendVip赠会员异步任务
            Integer receiveType = presentPerformService.queryReceiveTypeByIdWithDefault(orderData.getPresentPerformId());
            PresentVipRequest presentVipRequest = PackagePresentVip.packagePresentVipRequest(orderData, request, receiveType);
            asyncTaskManager.insertTask(new PresentVipAsyncTask(presentVipRequest, order));
        }
    }

    @Override
    public void judgeReceiveBuyPresent(PresentRequest request, BaseResponse response) {
        PresentRecord record = new PresentRecord();
        record.setBuyUid(request.getUid());
        record.setBuyType(request.getVipType());
        List<PresentRecord> records = presentRecordService.queryRecordByParams(record);
        if (CollectionUtils.isEmpty(records)) {
            response.setResultCode(EnumResultCode.NOT_RECEIVE);
            return;
        }
    }

    @Override
   public Integer queryTimeByMixTime(List<PresentOrder> orderList,
        List<PresentOrderData> orderDataList, PresentConfig config) {
        if (CollectionUtils.isEmpty(orderList)) {
            return 0;
        }

        long totalTimeInMillis = 0;
        for (PresentOrder order : orderList) {
            if (order.getReceiveDeadlineTime() != null && order.getReceiveDeadlineTime().before(new Date())) {
                log.info("DateUtils queryTimeByMixTime超过领取截止时间了，没有可用的有效时间order:{}",
                    JSONObject.toJSONString(order));
                continue;
            }

            PresentOrderData orderData = new PresentOrderData();
            BeanUtils.copyProperties(order, orderData);
            orderData.setPresentCode(config.getPresentCode());
            orderData.setBuyCode(config.getBuyCode());
            orderData.setPayType(config.getPayType());
            Integer receiveType = presentPerformService.queryReceiveTypeByIdWithDefault(order.getPresentPerformId());
            long time = DateUtils.calPresentTime(order, receiveType);
            if (time > 0) {
                totalTimeInMillis += time;
            }
            orderDataList.add(orderData);
        }
        return CalAmountUtils.calCeilAmountByDayMills(totalTimeInMillis);
    }

    @Override
    public List<Long> checkOrderNeedPresent(OrderMessage message) {
        List<Long> list = Lists.newArrayList();
        List<PresentConfig> presentConfigList = configMap.get(message.getProductSubtype());

        for (PresentConfig presentConfig : presentConfigList) {
            if (presentConfig.getStatus() == 0 || presentConfig.getHandlerStatus() == 0) {
                continue;
            }

            if (!this.checkBuyCode(message, presentConfig)) {
                continue;
            }

            if (!presentConfig.checkValid(message.getPayTime())) {
                continue;
            }

            //条件匹配
            PresentCondition targetCondition = getTargetCondition(message, presentConfig);
            if (targetCondition == null) {
                log.info("[条件无效，什么也不做,条件:empty]");
                continue;
            }

            list.add(presentConfig.getId());
        }
        return list;
    }

    private Boolean checkBuyCode(OrderMessage orderMessage, PresentConfig presentConfig) {
        String buyCode = presentConfig.getBuyCode();
        // 兼容buycode为空的
        if (org.apache.commons.lang.StringUtils.isBlank(buyCode) || buyCode.equals(orderMessage.getPid())) {
            return true;
        }
        // 学生会员特殊处理
        if (String.valueOf(VipTypeEnum.gold.getVipType()).equals(orderMessage.getProductSubtype())
            && String.valueOf(VipTypeEnum.youth.getVipType()).equals(presentConfig.getPvipType())) {
            return true;
        }
        return false;
    }

    private PresentCondition getTargetCondition(OrderMessage message, PresentConfig presentConfig) {
        long start = System.currentTimeMillis();
        List<PresentCondition> presentConditionList = getConditionsByConfig(presentConfig.getConditionIds());
        if (CollectionUtils.isEmpty(presentConditionList)) {
            return null;
        }
        //开始把数据库查询出来的条件和消息体的匹配
        Map<String, String> map = (Map<String, String>) ConvertUtils.objectToMap(message);
        PresentCondition targetCondition = null;
        for (PresentCondition condition : presentConditionList) {
            boolean isPresent = ExecuteExpression.executeCodeExpression(map, condition.getCondition());
            if (isPresent || org.apache.commons.lang.StringUtils.isEmpty(condition.getCondition())) {
                targetCondition = condition;
                log.info("[targetCondition:{}]", JSONObject.toJSONString(targetCondition));
                break;
            }
        }
        log.info("getTargetCondition() [cost:{}ms]", System.currentTimeMillis() - start);
        return targetCondition;
    }

    private List<PresentCondition> getConditionsByConfig(String conditionIds) {
        if (StringUtils.isBlank(conditionIds)) {
            return Lists.newArrayList();
        }
        List list = Lists.newArrayList();
        String[] idsStringArray = conditionIds.split(",");
        for (String id : idsStringArray) {
            list.add(conditionMap.get(Long.valueOf(id)));
        }
        return list;
    }


    @PostConstruct
    public void initConfig() {
        Map<String, List<PresentConfig>> map = presentConfigService.queryPresentConfig();
        if (map != null) {
            configMap = map;
        }
        Map<Long, PresentCondition> conditionConfigMap = presentConditionService.queryConditions();
        if (conditionConfigMap != null) {
            conditionMap = conditionConfigMap;
        }
    }

}
