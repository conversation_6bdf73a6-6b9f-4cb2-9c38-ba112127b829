package com.iqiyi.vip.present.utils;

import com.google.common.base.Joiner;
import com.iqiyi.kiwi.utils.EncodeUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * md5类
 * <AUTHOR>
 */
public class Md5Utils {

    private static final Logger logger = LoggerFactory.getLogger(Md5Utils.class);

    public static String getMD5Str(Map paramMap, String key) {
        ArrayList<String> arr = new ArrayList<String>(paramMap.keySet());
        Collections.sort(arr, new Comparator() {
            @Override
            public int compare(Object o1, Object o2) {
                if (o1.toString().compareTo(o2.toString()) > 0) {
                    return 1;
                } else if (o1.toString().compareTo(o2.toString()) == 0) {
                    return 0;
                } else {
                    return -1;
                }
            }
        });
        StringBuffer sb = new StringBuffer();
        for (String para : arr) {
            sb.append(para).append("=").append(paramMap.get(para)).append("&");
        }
        sb.delete(sb.length() - 1, sb.length()).append(key);
        logger.debug("md5 before:" + sb.toString());
        return EncodeUtils.MD5(sb.toString(), "UTF-8");
    }

    /**
     * 功能：除去数组中的空值和签名参数
     *
     * @param sArray 签名参数组
     * @return 去掉空值与签名参数后的新签名参数组
     */
    private static Map paraFilter(Map sArray, List<String> filterList) {
        List keys = new ArrayList(sArray.keySet());
        Map sArrayNew = new HashMap();

        for (int i = 0; i < keys.size(); i++) {
            String key = (String) keys.get(i);
            String value = (String) sArray.get(key);

            if (value == null || value.equals("") || filterList.contains(key)) {
                continue;
            }
            sArrayNew.put(key, value);
        }

        return sArrayNew;
    }


    /**
     * 目前passport使用
     *
     * @param paramMap
     * @param key
     * @return
     */
    public static final String md5HttpParam(Map<String, String> paramMap, String key) {
        SortedMap<String, String> sortedParams = paramMap instanceof SortedMap ? (SortedMap) paramMap : new TreeMap(paramMap);
        String text = Joiner.on('|').withKeyValueSeparator("=").useForNull("").join(sortedParams).concat("|").concat(key);
        return DigestUtils.md5Hex(text);
    }

    /**
     * 目前调用会员卡使用
     *
     * @param paramMap
     * @param key
     * @return
     */
    public static final String md5HttpParamV2(Map<String, String> paramMap, String key) {
        SortedMap<String, String> sortedParams = paramMap instanceof SortedMap ? (SortedMap) paramMap : new TreeMap(paramMap);
        String text = Joiner.on("&").withKeyValueSeparator("=").join(sortedParams).concat(key);
        return DigestUtils.md5Hex(text);
    }

}
