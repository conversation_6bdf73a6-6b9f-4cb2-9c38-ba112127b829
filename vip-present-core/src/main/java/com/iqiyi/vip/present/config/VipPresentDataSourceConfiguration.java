package com.iqiyi.vip.present.config;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * VIP买赠数据源配置 - vip-present数据库
 * 使用Spring Boot + ShardingSphere自动配置的shardingDataSource
 * 处理买赠业务相关的所有数据操作
 *
 * <AUTHOR> Assistant
 */
@Configuration
public class VipPresentDataSourceConfiguration {

    /**
     * VIP买赠SqlSessionFactory - 用于vip-present数据库
     * 使用ShardingSphere配置的分库分表DataSource
     * 处理present_order、present_record等买赠相关表
     */
    @Bean(name = "sqlSessionFactory")
    @Primary
    public SqlSessionFactory sqlSessionFactory(@Qualifier("shardingDataSource") DataSource shardingDataSource) throws Exception {
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        sqlSessionFactoryBean.setDataSource(shardingDataSource);
        
        // 设置Mapper文件位置
        org.springframework.core.io.Resource[] mapperResources = resolver.getResources("classpath:/mybatis-mapper/*Mapper.xml");
        sqlSessionFactoryBean.setMapperLocations(mapperResources);
        
        // 设置类型别名包
        sqlSessionFactoryBean.setTypeAliasesPackage("com.iqiyi.vip.present.model");
        
        // 设置配置
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        sqlSessionFactoryBean.setConfiguration(configuration);
        
        return sqlSessionFactoryBean.getObject();
    }

    /**
     * VIP买赠SqlSessionTemplate - 用于vip-present数据库操作
     */
    @Bean(name = "sqlSessionTemplate")
    @Primary
    public SqlSessionTemplate sqlSessionTemplate(@Qualifier("sqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    /**
     * VIP买赠JdbcTemplate - 用于vip-present数据库的JDBC操作
     * 主要供AsyncTaskConfig等组件使用
     */
    @Bean(name = "jdbcTemplate")
    @Primary
    public JdbcTemplate jdbcTemplate(@Qualifier("shardingDataSource") DataSource shardingDataSource) {
        return new JdbcTemplate(shardingDataSource);
    }

    /**
     * VIP买赠NamedParameterJdbcTemplate - 用于vip-present数据库的命名参数JDBC操作
     */
    @Bean(name = "namedParameterJdbcTemplate")
    @Primary
    public NamedParameterJdbcTemplate namedParameterJdbcTemplate(@Qualifier("shardingDataSource") DataSource shardingDataSource) {
        return new NamedParameterJdbcTemplate(shardingDataSource);
    }

    /**
     * VIP买赠事务管理器 - 用于vip-present数据库的事务管理
     */
    @Bean(name = "transactionManager")
    @Primary
    public PlatformTransactionManager transactionManager(@Qualifier("shardingDataSource") DataSource shardingDataSource) {
        return new DataSourceTransactionManager(shardingDataSource);
    }
}
