package com.iqiyi.vip.present.mapper;

import com.iqiyi.vip.present.model.PresentProduct;

import java.util.List;

public interface PresentProductMapper {

    int deleteByPrimaryKey(Integer id);

    int insert(PresentProduct record);

    int insertSelective(PresentProduct record);

    PresentProduct selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(PresentProduct record);

    int updateByPrimaryKey(PresentProduct record);

    int updateByCode(PresentProduct record);

    PresentProduct selectByCode(String code);

    List<PresentProduct> getByVipType(String vipType);

    String getCodeById(Long id);
}