package com.iqiyi.vip.present.enums;

import com.google.common.collect.Lists;

import java.util.List;

public enum CheckRecordStatusEnum {

    WAITING(1, "告警中"),
    COMPENSATED(2, "已补偿"),
    COMPENSATE_FAIL(3, "补偿失败"),
    COMPENSATING(4, "补偿中"),
    NOT_MATCH(5, "不匹配处理条件"),
    ALREADY_PRESENTED(6, "已被其他服务赠送"),
    NO_NEED_PRESENT(7, "无需赠送"),
    NO_NEED_COMPENSATE(8, "无需补偿"),
    ;

    private Integer status;
    private String desc;

    CheckRecordStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 获取所有正在补偿过程中的状态列表（待补偿、补偿中）
     * @return 状态值列表
     */
    public static List<Integer> getCompensationProcessStatuses() {
        return Lists.newArrayList(
            WAITING.getStatus(),
            COMPENSATING.getStatus()
        );
    }

    /**
     * 判断是否无需补偿或已补偿完毕
     * 
     * @param status 检查记录状态
     * @return true-无需补偿或已补偿完毕，false-需要继续补偿处理
     */
    public static boolean isNoNeedOrCompletedCompensation(Integer status) {
        return NOT_MATCH.getStatus().equals(status)
                || NO_NEED_PRESENT.getStatus().equals(status)
                || NO_NEED_COMPENSATE.getStatus().equals(status)
                || ALREADY_PRESENTED.getStatus().equals(status)
                || COMPENSATED.getStatus().equals(status);
    }
}
