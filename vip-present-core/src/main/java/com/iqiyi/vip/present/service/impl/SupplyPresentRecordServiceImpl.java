package com.iqiyi.vip.present.service.impl;

import com.iqiyi.vip.present.dao.SupplyPresentRecordDao;
import com.iqiyi.vip.present.model.SupplyPresentRecord;
import com.iqiyi.vip.present.service.SupplyPresentRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class SupplyPresentRecordServiceImpl implements SupplyPresentRecordService {

    @Resource
    private SupplyPresentRecordDao supplyPresentRecordDao;


    @Override
    public SupplyPresentRecord selectByUidAndSource(SupplyPresentRecord supplyPresentRecord) {
        return supplyPresentRecordDao.selectByUidAndSource(supplyPresentRecord);
    }

    @Override
    public List<SupplyPresentRecord> selectByUid(SupplyPresentRecord supplyPresentRecord) {
        return supplyPresentRecordDao.selectByUid(supplyPresentRecord);
    }
}
