package com.iqiyi.vip.present.dao;

import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import java.sql.Timestamp;

import com.iqiyi.kiwi.utils.DateHelper;
import com.iqiyi.vip.present.mapper.PresentAsyncTaskMapper;
import com.iqiyi.vip.present.model.PresentAsyncTask;

/**
 * Created at: 2022-04-11
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
public class PresentAsyncTaskDao {

    @Resource
    PresentAsyncTaskMapper presentAsyncTaskMapper;

    public boolean addAsyncTask(PresentAsyncTask asyncTask) {
        asyncTask.setUpdateTime(DateHelper.getDateTime());
        try {
            return presentAsyncTaskMapper.insert(asyncTask) > 0;
        } catch (DuplicateKeyException e) {
            PresentAsyncTask storedAsyncTask = presentAsyncTaskMapper.selectByTaskId(asyncTask.getTaskId());
            if (asyncTask.getClassName().equals(storedAsyncTask.getClassName())) {
                log.warn("保存异步任务-重复保存,相同classname异步任务! asyncTask={}", asyncTask);
            } else {
                log.error("保存异步任务-taskId重复! asyncTask={}", asyncTask);
            }
            return false;
        }
    }

    public void removeAsyncTask(Long primaryKey) {
        presentAsyncTaskMapper.deleteByPrimaryKey(primaryKey);
    }

    public boolean makeTaskProcessing(Long primaryKey) {
        return presentAsyncTaskMapper.makeTaskProcessing(primaryKey) > 0;
    }

    public boolean makeTaskUnProcess(Long primaryKey) {
        return presentAsyncTaskMapper.makeTaskUnProcess(primaryKey) > 0;
    }

    public boolean restoreTaskForRetry(Long primaryKey, Timestamp nextRunTime) {
        return presentAsyncTaskMapper.restoreTaskForRetry(primaryKey, nextRunTime) > 0;
    }

    public PresentAsyncTask getAsyncTask(Long primaryKey) {
        if (primaryKey == null) {
            return null;
        }
        return presentAsyncTaskMapper.selectByPrimaryKey(primaryKey);
    }

}
