package com.iqiyi.vip.present.config;

import com.ctrip.framework.apollo.core.ConfigRegion;
import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.solar.config.client.CloudConfigService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: <PERSON>
 * @Date: 2020/11/11
 */
@Configuration
public class CloudConfigInitializer {
    @Value("${appEnv}")
    private String appEnv;

    @Value("${appName}")
    private String appName;

    @Value("${appRegion}")
    private String appRegion;

    @Bean(name = "cloudConfig")
    public CloudConfig cloudConfig() {
        return CloudConfigService.builder().withConfigRegion(ConfigRegion.fromRegion(appRegion)).withAppID(appName).withEnv(appEnv).build();
    }
}
