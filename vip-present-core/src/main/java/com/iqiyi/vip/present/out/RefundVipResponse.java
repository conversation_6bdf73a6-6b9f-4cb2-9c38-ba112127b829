package com.iqiyi.vip.present.out;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RefundVipResponse {

    private String code;
    private String msg;
    private String message;
    private ResponseData data;

    public boolean isSuccess() {
        return Objects.equals(this.code, "A00000") || Objects.equals(this.code, "A00001");
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ResponseData {

        private String code;
        private String msg;
        private String status;
        @JsonProperty("order_code")
        private String orderCode;
        private String fee;
        private String sign;
        private String uid;
        @JsonProperty("create_time")
        private String createTime;
        @JsonProperty("refundTime")
        private String refundTime;
        @JsonProperty("refund_code")
        private String refundCode;
        private String reason;
    }
}
