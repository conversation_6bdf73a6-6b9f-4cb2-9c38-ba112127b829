package com.iqiyi.vip.present.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * check_rule
 */
@Data
public class CheckRule implements Serializable {
    private Integer id;

    /**
     * 规则code
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 校验筛选项
     */
    private String condition;

    /**
     * 校验区间偏移量
     */
    private Integer offset;

    /**
     * 校验区长度，单位：秒
     */
    private Integer width;

    private String dataBase;

    /**
     * 状态，0：无效，1：失效
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}
