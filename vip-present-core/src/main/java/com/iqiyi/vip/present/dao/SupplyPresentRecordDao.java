package com.iqiyi.vip.present.dao;

import com.alibaba.fastjson.JSON;
import com.iqiyi.vip.present.mapper.SupplyPresentRecordMapper;
import com.iqiyi.vip.present.model.SupplyPresentRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class SupplyPresentRecordDao {
    @Resource
    private SupplyPresentRecordMapper supplyPresentRecordMapper;

    public SupplyPresentRecord selectByUidAndSource(SupplyPresentRecord supplyPresentRecord) {
        return supplyPresentRecordMapper.selectByUidAndSource(supplyPresentRecord.getUid(), supplyPresentRecord.getSource());
    }

    public List<SupplyPresentRecord> selectByUid(SupplyPresentRecord supplyPresentRecord) {
        return supplyPresentRecordMapper.selectByUid(supplyPresentRecord.getUid());
    }

    @Transactional
    public void batchInsert(List<SupplyPresentRecord> supplyPresentRecords) {
        for (SupplyPresentRecord record : supplyPresentRecords) {
            if (record.getId() != null) {
                //更新
                log.info("SupplyPresentRecordDao update record:{}", JSON.toJSONString(record));
                supplyPresentRecordMapper.updateSelective(record);
            } else {
                log.info("SupplyPresentRecordDao insert record:{}", JSON.toJSONString(record));
                supplyPresentRecordMapper.insertSelective(record);
            }
        }
    }
}
