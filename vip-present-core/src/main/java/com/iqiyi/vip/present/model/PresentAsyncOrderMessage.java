package com.iqiyi.vip.present.model;

import com.alibaba.fastjson.JSON;
import com.iqiyi.vip.present.data.OrderMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/7/25
 * @apiNote
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PresentAsyncOrderMessage {

    private Long id;
    private Long uid;
    private String orderCode;
    private Integer productSubType;
    private Date startTime;
    private Date endTime;
    private String orderMessage;
    private Date createTime;
    private Date updateTime;

    public static PresentAsyncOrderMessage build(OrderMessage orderMessage) {
        return PresentAsyncOrderMessage.builder()
                .uid(orderMessage.getUid())
                .orderCode(orderMessage.getOrderCode())
                .productSubType(Integer.parseInt(orderMessage.getProductSubtype()))
                .startTime(orderMessage.getStartTime())
                .endTime(orderMessage.getEndTime())
                .orderMessage(JSON.toJSONString(orderMessage))
                .build();
    }
}
