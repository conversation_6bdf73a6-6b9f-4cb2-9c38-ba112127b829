package com.iqiyi.vip.present.consts;

import lombok.Getter;

/**
 * 计算赠送amount类型 User: zhangdaoguang Date: 2019/3/15 Time: 9:33
 *
 * @see com.iqiyi.vip.present.utils.CalAmountUtils
 */
@Getter
public enum CalAmountTypeEnum {

    /**
     * 根据消息计算赠送amount
     */
    MESSAGE_TIME_DAYS(0, "消息权益开始和结束时间间隔(天)"),
    MESSAGE_AMOUNT(1, "直接取消息的amount");

    private Integer type;

    private String desc;

    CalAmountTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
