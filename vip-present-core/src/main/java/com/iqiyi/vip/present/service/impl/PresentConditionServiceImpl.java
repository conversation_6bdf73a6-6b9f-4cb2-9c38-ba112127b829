package com.iqiyi.vip.present.service.impl;

import com.google.common.collect.Lists;

import com.iqiyi.vip.present.config.GuavaCacheConfig;
import com.iqiyi.vip.present.dao.PresentConditionDao;
import com.iqiyi.vip.present.model.PresentCondition;
import com.iqiyi.vip.present.service.PresentConditionService;
import com.iqiyi.vip.present.utils.PresentConfigCacheComponent;

import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CacheConfig(cacheManager = GuavaCacheConfig.GUAVA_CACHE_MANAGER)
@Service
public class PresentConditionServiceImpl implements PresentConditionService {
    @Resource
    PresentConditionDao presentConditionDao;

    @Cacheable(value = "queryConditionById", keyGenerator = "customKeyGenerator")
    @Override
    public PresentCondition queryConditionById(Long conditionId) {
        return presentConditionDao.selectByPrimaryKey(conditionId);
    }

    @Override
    public List<PresentCondition> queryConditionByIds(String conditionIds) {
        if (StringUtils.isBlank(conditionIds)) {
            return Lists.newArrayList();
        }
        String[] idsStringArray = conditionIds.split(",");
        List<String> ids = Arrays.asList(idsStringArray);
        List<Long> idList = new ArrayList<>();
        for (String id : ids) {
            idList.add(Long.valueOf(id));
        }
        return presentConditionDao.queryConditionByIds(idList);
    }

    @Override
    public List<PresentCondition> queryConditionFromCacheByIds(String conditionIds) {
        if (StringUtils.isBlank(conditionIds)) {
            return Lists.newArrayList();
        }
        String[] idsStringArray = conditionIds.split(",");
        List<PresentCondition> conditions = new ArrayList<>();
        for (String id : idsStringArray) {
            conditions.add(PresentConfigCacheComponent.conditionMap.get(Long.valueOf(id)));
        }
        return conditions;
    }

    @Override
    public Map<Long, PresentCondition> queryConditions() {
        List<PresentCondition> conditions = presentConditionDao.queryConditions();
        return conditions.stream().collect(Collectors.toMap(PresentCondition::getId, p -> p));
    }
}
