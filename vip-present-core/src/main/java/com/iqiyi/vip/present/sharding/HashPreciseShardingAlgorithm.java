package com.iqiyi.vip.present.sharding;

import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;

import java.util.Collection;


@Slf4j
public class HashPreciseShardingAlgorithm implements PreciseShardingAlgorithm<Long> {

    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<Long> shardingValue) {
        Long tableSuffix = shardingValue.getValue() % availableTargetNames.size();
        return String.format("%s_%02d", shardingValue.getLogicTableName(), tableSuffix);
    }

    public static void main(String[] args) {
        Long uid = 5640970355976320L;
        System.out.println(uid%20);
    }

}