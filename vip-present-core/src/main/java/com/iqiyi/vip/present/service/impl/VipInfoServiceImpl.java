package com.iqiyi.vip.present.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.iqiyi.vip.present.out.VipUser;
import com.iqiyi.vip.present.service.RestTemplateService;
import com.iqiyi.vip.present.service.VipInfoService;
import com.qiyi.usercloud.UidEnigma;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Map;

@Service
@Slf4j
public class VipInfoServiceImpl implements VipInfoService {
    @Autowired
    private RestTemplateService restTemplateService;
    @Value("${vinfo.get.url}")
    private String vinfoGetUrl;

    @Override
    public VipUser getVipInfo(Long uid, String vipTypes, String msgId) throws Exception{
        Map<String, Object> params = Maps.newHashMap();
        params.put("platform", "b6c13e26323c537d");//默认pc=b6c13e26323c537d
        params.put("appVersion", "1.0");
        params.put("bizSource", "vip-present");
        params.put("messageId", msgId);
        params.put("version", "5.0");
        String suid = UidEnigma.encrypt(uid);
        params.put("suid", suid);
        if (StringUtils.isNotBlank(vipTypes)) {
            params.put("vipTypes", vipTypes);
        } else {
            params.put("vipTypes", "vt001");
        }

        Map<String, Object> result = restTemplateService.postForObjectThrow(vinfoGetUrl, params, Map.class,true);
        log.info("VipInfoServiceImpl getVipInfo return result [msgId:{}] [uid:{}] [vipTypes:{}] [result:{}]",
                msgId, uid, vipTypes, JSON.toJSONString(result));
        if(result==null || CollectionUtils.isEmpty(result) || !"A00000".equals(result.get("code"))){
            //调用出现异常map也不一定为空
            log.info("VipInfoServiceImpl getVipInfo error [msgId:{}] [uid:{}] [vipTypes:{}] [result:{}]",
                msgId, uid, vipTypes, JSON.toJSONString(result));
            return null;
        }
        Map data = (Map) result.get("data");
        boolean vip_not_exists = data.get("vip_info") == null || data.get("vip_info").equals("null");
        boolean tv_vip_not_exists = data.get("tv_vip_info") == null || data.get("tv_vip_info").equals("null");

        if (data == null || (vip_not_exists && tv_vip_not_exists)) {
            //未会员
            return null;
        }

        VipUser vipUserVo = JSON.parseObject(JSON.toJSONString(vip_not_exists ? data.get("tv_vip_info") : data.get("vip_info")), VipUser.class);
        if(vipUserVo!=null){
            vipUserVo.setUid(uid);
        }
        return vipUserVo;
    }
}
