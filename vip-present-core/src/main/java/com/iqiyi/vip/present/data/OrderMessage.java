package com.iqiyi.vip.present.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.iqiyi.vip.present.utils.NullStringToIntegerDeserializer;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Date;
import java.util.Set;

/**
 * http://wiki.qiyi.domain/pages/viewpage.action?pageId=29426430
 */
@Data
public class OrderMessage {

    private String msgId;

    private Long uid;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    private String centerCode;
    private String gateway;
    private String fv;
    private String orderCode;
    private String aid;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
    private String productSubtype;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payTime;
    private String tradeCode;
    private Integer productType;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    private Integer amount;
    private String centerPayType;
    private Integer msgtype;
    private String centerPayService;
    private String renewFlag;
    private String sendVodCoupon;
    @JSONField(name = "fr_version")
    private String frVersion;
    private String sourceType;
    private Integer platform;
    private Integer status;
    private String autoRenew;
    private String pid;
    private Long orderFee;
    private String expCardBatch;
    private Integer payType;
    private String serviceCode;
    private String channel;
    private Long settlementFee;
    private Long orderRealFee;
    private Long couponSettlementFee;
    private String presentGoldVip;
    private String platformCode;
    private String orderType;
    private String partner;
    private String type;
    private String actCode;
    @JsonDeserialize(using = NullStringToIntegerDeserializer.class)
    private Integer chargeType;
    /**
     * 免费会员批次号
     */
    private String giftBatchNo;
    /**
     * 历史补赠标识
     */
    private Boolean historyPresent;
    /**
     * 无需赠送的会员类型
     */
    private Set<Integer> notNeedPresentVipTypeSet;

    //扩展字段
    private String refer;

    private String isPressureTest;

    /**
     * 1：签约连续包月 2：系统发起的自动续费扣费 3：签约的时长与购买时长相同
     * 其他：普通订单
     */
    public boolean isAutoRenewProduct() {
        if (StringUtils.isBlank(this.autoRenew)) {
            return false;
        }
        return "1".equals(this.autoRenew) || "2".equals(this.autoRenew) || "3".equals(this.autoRenew);
    }
}
