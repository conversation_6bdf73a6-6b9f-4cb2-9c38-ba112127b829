package com.iqiyi.vip.present.service;

import com.iqiyi.vip.present.entity.CheckRecord;
import com.iqiyi.vip.present.entity.Order;
import com.iqiyi.vip.present.model.PresentOrder;

/**
 * 补偿状态检查服务
 * 
 * <AUTHOR>
 * @date 2024/12/20
 */
public interface CompensationStatusService {

    /**
     * 统一检查订单补偿状态
     * 只要不是"未赠送"状态，就认为已经补偿过了
     *
     * @param order 订单信息
     * @param checkRecord 检查记录
     * @param queryPresentOrderParam 查询参数，由调用方构建
     * @return true-已补偿或无需补偿，false-需要补偿
     */
    boolean checkCompensationStatus(Order order, CheckRecord checkRecord, PresentOrder queryPresentOrderParam);
}
