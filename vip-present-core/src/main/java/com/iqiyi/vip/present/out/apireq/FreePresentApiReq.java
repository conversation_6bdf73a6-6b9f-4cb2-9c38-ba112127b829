package com.iqiyi.vip.present.out.apireq;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iqiyi.vip.present.model.PresentOrder;
import org.springframework.core.env.Environment;

import java.util.Map;

import com.iqiyi.vip.present.out.PresentVipRequest;
import com.iqiyi.vip.present.utils.ConvertUtils;
import com.iqiyi.vip.present.utils.EncodeUtils;

/**
 * 通过交易免费订单接口赠送会员的请求信息
 * https://wiki.qiyi.domain/pages/viewpage.action?pageId=18423460
 * https://iq.feishu.cn/wiki/WY6fwbehZizJyQktUHBc1pmWnyc
 *
 * <AUTHOR> (<EMAIL>)
 * @date 2020/3/11 12:00
 */
public class FreePresentApiReq implements PresentApiReq {

    @Override
    public String presentUrl(Environment environment) {
        return environment.getProperty("dopay.url");
    }

    @Override
    public Map presentParams(PresentVipRequest request, Environment environment, PresentOrder presentOrder) {
        ObjectMapper objectMapper = new ObjectMapper();
        Map reqMap = objectMapper.convertValue(request, Map.class);
        ConvertUtils.prepareRequestMap(reqMap);
        String payKey = environment.getProperty("internal.pay.key");
        reqMap.put("sign", EncodeUtils.signMessage(reqMap, payKey));
        return reqMap;
    }

    @Override
    public String cancelUrl(Environment environment) {
        return environment.getProperty("refund.url");
    }

    @Override
    public Map cancelParams(Map request, Environment environment) {
        Map map = PresentApiReqEngine.commonCancelParams(request);
        //map.put("serviceCode", environment.getProperty("qiyue.refund.serviceCode"));
        map.put("sign", EncodeUtils.sign(map, environment.getProperty("qiyue.refund.sign")));
        return map;
    }

    @Override
    public Boolean isCancelLb(Environment environment) {
        return environment.getProperty("refund.url.lb",Boolean.class);
    }

    @Override
    public Boolean ispPresentLb(Environment environment) {
        return environment.getProperty("dopay.url.lb",Boolean.class);
    }
}
