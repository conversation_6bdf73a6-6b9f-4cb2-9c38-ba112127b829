package com.iqiyi.vip.present.data;

import com.iqiyi.vip.present.model.PresentCondition;
import com.iqiyi.vip.present.model.PresentPerform;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/4/10
 * @apiNote
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PresentRulePerform {

    private PresentCondition condition;

    private PresentPerform perform;
}
