package com.iqiyi.vip.present.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.iqiyi.vip.present.consts.EnumResultCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.core.env.Environment;

import java.util.HashMap;
import java.util.Map;

import com.iqiyi.vip.present.consts.EnumOrderStatusCode;
import com.iqiyi.vip.present.consts.EnumOrderTypeCode;
import com.iqiyi.vip.present.consts.TaskPoolTypeEnum;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.out.RefundVipResponse;
import com.iqiyi.vip.present.out.apireq.PresentApiReq;
import com.iqiyi.vip.present.out.apireq.PresentApiReqEngine;
import com.iqiyi.vip.present.service.PresentOrderService;
import com.iqiyi.vip.present.service.RestTemplateService;
import com.iqiyi.vip.present.utils.ApplicationContextUtil;
import com.iqiyi.vip.present.utils.ConvertUtils;
import com.iqiyi.vip.present.utils.DateUtils;
import com.iqiyi.vip.threadpool.AbstractTask;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class RefundVipAsyncTask extends AbstractTask {

    @Getter
    @Setter
    private Map<String, Object> request;

    public RefundVipAsyncTask(Map<String, Object> request) {
        this.request = request;
    }

    @Override
    protected boolean execute() {
        log.info("RefundVipAsyncTask execute [request:{}]",JSONObject.toJSONString(request));
        Environment environment = (Environment) ApplicationContextUtil.getBean(Environment.class);
        String msgId = (String) request.get("msgId");
        String refundOrderCode = request.containsKey("idempotentCode")?(String) request.get("idempotentCode")
                :(String) request.get("refundOrderCode");
        Map<String, Object> map = (Map<String, Object>) request.get("presentOrder");
        PresentOrder presentOrder = ConvertUtils.mapToObject(map, PresentOrder.class);
        int oldStatus = presentOrder.getStatus();

        int presentType = NumberUtils.toInt(presentOrder.getPresentType());
        PresentApiReq presentApiReq = PresentApiReqEngine.build(presentType);
        String refundUrl = presentApiReq.cancelUrl(environment);
        request = presentApiReq.cancelParams(request, environment);
        Boolean lb = presentApiReq.isCancelLb(environment);

        log.info("RefundVipAsyncTask execute [msgId:{}][request:{}]", msgId, JSONObject.toJSONString(request));
        RestTemplateService restTemplateService = (RestTemplateService) ApplicationContextUtil.getBean(RestTemplateService.class);
        RefundVipResponse refundVipResponse;
        Map<String, Object> execResult = new HashMap<>();
        try {
            execResult = restTemplateService.postForObject(refundUrl, request, Map.class,lb);
        } catch (Exception e) {
            log.error("RefundVipAsyncTask [异步任务奇悦服务异常，重试][reqMap:{}][uid:{}]", JSONObject.toJSONString(request),presentOrder.getUid(), e);
            return false;
        }
        if (!EnumResultCode.SUCCESS.getCode().equals(execResult.get("code")) && !"REFUND_SUCCESS_EXISTED".equals(execResult.get("code"))
                &&!"A00001".equals(execResult.get("code"))) {
            log.error("RefundVipAsyncTask [异步任务奇悦服务异常，重试][reqMap:{}][uid:{}][response:{}]", JSONObject.toJSONString(request),presentOrder.getUid(),
                    execResult.isEmpty() ? "" : JSONObject.toJSONString(execResult));
            return false;
        }
        log.info("RefundVipAsyncTask execute [msgId:{}][request:{}][uid:{}][refundVipResponse:{}]", msgId, JSONObject.toJSONString(request), presentOrder.getUid(),JSONObject
            .toJSONString(execResult));

        //String amount = (String) request.get("timeLength");
        String amount = request.containsKey("amount")?(String) request.get("amount"):(String) request.get("timeLength");
        PresentOrderService presentOrderService = (PresentOrderService) ApplicationContextUtil.getBean(PresentOrderService.class);
        presentOrder.setStatus(EnumOrderStatusCode.REFUND_SUCCESS.getCode());
        presentOrder.setOrderType(EnumOrderTypeCode.NOT.getCode());
        presentOrder.setRefundOrderCode(refundOrderCode);//退款原单号
        presentOrder.setDeadlineEndTime(DateUtils.getDateBefore(presentOrder.getDeadlineEndTime(), Integer.valueOf(amount)));
        log.info("RefundVipAsyncTask execute [msgId:{}][request:{}][presentOrder:{}]", msgId, JSONObject.toJSONString(request), JSONObject
            .toJSONString(presentOrder));
        presentOrderService.updateOrderStatus(presentOrder, oldStatus);
        return true;
    }

    @Override
    public void deserialize(String data) throws IllegalArgumentException {
        JSONObject obj = JSON.parseObject(data);
        this.request = obj.getObject("request", Map.class);
    }

    @Override
    public String serialize() {
        JSONObject obj = new JSONObject();
        obj.put("request", request);
        return obj.toJSONString();
    }

    @Override
    public int getDefaultPoolType() {
        return TaskPoolTypeEnum.REFUND_VIP.getType();
    }
}
