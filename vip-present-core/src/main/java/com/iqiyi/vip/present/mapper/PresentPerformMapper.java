package com.iqiyi.vip.present.mapper;

import com.iqiyi.vip.present.model.PresentPerform;

import java.util.List;

public interface PresentPerformMapper extends BaseMapper{
    int deleteByPrimaryKey(Long id);

    int insert(PresentPerform record);

    int insertSelective(PresentPerform record);

    PresentPerform selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PresentPerform record);

    int updateByPrimaryKey(PresentPerform record);

    List<PresentPerform> queryPerforms();
}