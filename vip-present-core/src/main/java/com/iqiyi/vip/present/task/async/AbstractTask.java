package com.iqiyi.vip.present.task.async;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;

import java.sql.Timestamp;

import com.iqiyi.vip.present.dao.PresentAsyncTaskDao;
import com.iqiyi.vip.present.utils.DateUtils;

/**
 * Created at: 2022-04-12
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractTask implements Task {

    /**
     * 任务持久化id
     */
    private transient Long persistenceId;
    /**
     * 任务已执行次数
     */
    private transient Integer execCount;
    private transient PresentAsyncTaskDao asyncTaskDao;

    @Override
    public void run() {
        boolean execResult = false;
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            execResult = execute();
            processDB(execResult);
        } catch (Exception e) {
            log.error("exec task occurred exception, task:{}, persistenceId:{}", getClass().getSimpleName(), getPersistenceId(), e);
            processDB(execResult);
        } finally {
            callback(execResult);
        }
        log.info("task:{} exec finished persistenceId:{} cost:{}", getClass().getSimpleName(), getPersistenceId(), stopWatch.getTime());
    }

    protected abstract boolean execute();

    public void callback(boolean result) {}

    private void processDB(boolean execResult) {
        if (execResult) {
            getAsyncTaskDao().removeAsyncTask(getPersistenceId());
        } else {
            if (getExecCount() > RETRY_TIME.length) {
                log.error("任务:{} 执行次数已到最大:{}, persistenceId:{}", getClass().getSimpleName(), RETRY_TIME.length, getPersistenceId());
                return;
            }
            Timestamp nextRunTime = DateUtils.calculateTime(DateUtils.getCurrentTimestamp(), RETRY_TIME[getExecCount()]);
            getAsyncTaskDao().restoreTaskForRetry(getPersistenceId(), nextRunTime);
        }
    }

    @Override
    public abstract void deserialize(String data) throws IllegalArgumentException;

    @Override
    public abstract String serialize();

    @Override
    public Long getPersistenceId() {
        return persistenceId;
    }

    @Override
    public void setPersistenceId(Long persistenceId) {
        this.persistenceId = persistenceId;
    }

    @Override
    public void setExecCount(Integer execCount) {
        this.execCount = execCount;
    }

    @Override
    public Integer getExecCount() {
        return execCount;
    }

    public abstract String genTaskId();

    @Override
    public void setAsyncTaskDao(PresentAsyncTaskDao asyncTaskDao) {
        this.asyncTaskDao = asyncTaskDao;
    }

    @Override
    public PresentAsyncTaskDao getAsyncTaskDao() {
        return asyncTaskDao;
    }

}
