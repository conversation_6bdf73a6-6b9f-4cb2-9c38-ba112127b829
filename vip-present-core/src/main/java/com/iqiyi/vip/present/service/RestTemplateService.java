package com.iqiyi.vip.present.service;

import com.iqiyi.vip.present.commonInner.HttpResultVo;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * 对restTemplate进行封装
 */
public interface RestTemplateService {

    /**
     * 返回无泛型对象
     * @param url
     * @param params
     * @param clazz
     * @param <T>
     * @return
     */
    <T> T postForObject(String url, Map<String, Object> params, Class<T> clazz,Boolean lb);

    <T> T postForObject(String url, Map<String, Object> params, Class<T> clazz,Boolean lb, String isPressureTest);

    /**
     * 返回无泛型对象
     * @param url
     * @param params
     * @param clazz
     * @param <T>
     * @return
     */
    <T> T post(String url, Map<String, String> params, Class<T> clazz,Boolean lb);

    /**
     * 返回指定泛型对象
     * @param url
     * @param params
     * @param type
     * @param <T>
     * @return
     */
    <T> T postForEntity(String url, Map<String, String> params, ParameterizedTypeReference<T> type,Boolean lb);

    /**
     * 返回指定泛型对象
     * @param url
     * @param params
     * @param type
     * @param <T>
     * @return
     */
    <T> T postForResult(String url, Map<String, String> params, ParameterizedTypeReference<HttpResultVo<T>> type,Boolean lb);

    /**
     * 返回泛型对象
     *
     * @param url
     * @param params
     * @param clazz
     * @param <T>
     * @return
     */
    <T> T getForResult(String url, Map<String, String> params, Class<T> clazz,Boolean lb);

    <T> T postForObjectThrow(String url, Map<String, Object> params, Class<T> clazz,Boolean lb) throws Exception;

    /**
     * 选择 restTemplate
     * @return
     */
    RestTemplate choseRestTemplate(Boolean lb);
}
