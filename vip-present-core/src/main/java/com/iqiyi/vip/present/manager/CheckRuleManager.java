package com.iqiyi.vip.present.manager;


import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import com.iqiyi.vip.present.entity.CheckRule;
import com.iqiyi.vip.present.mapper.CheckRuleMapper;

@Component
@Slf4j
public class CheckRuleManager {

    @Resource
    private CheckRuleMapper checkRuleMapper;

    public CheckRule getById(Integer id) {
        return checkRuleMapper.selectByPrimaryKey(id);
    }


}
