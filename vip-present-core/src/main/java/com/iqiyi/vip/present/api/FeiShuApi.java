package com.iqiyi.vip.present.api;

import com.iqiyi.vip.present.apiresponse.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Map;

@Component
@Slf4j
public class FeiShuApi extends BaseApi {

    @Resource
    private RestTemplate notifyRestTemplate;

    public static final String URL = "http://feishu.qiyi.domain/interactiveMsg";

    public static final String URL_TEXT = "http://feishu.qiyi.domain/textMsg";


    public boolean notify(Map<String, Object> param) {
        if (MapUtils.isEmpty(param)) {
            return true;
        }
        BaseResponse<String> response = doPostByJson(notifyRestTemplate, URL, param, "通知交易管家", new ParameterizedTypeReference<BaseResponse<String>>() {
        });
        return "A00000".equals(response.getCode());
    }

}
