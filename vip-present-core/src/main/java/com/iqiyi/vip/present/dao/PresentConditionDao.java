package com.iqiyi.vip.present.dao;

import com.iqiyi.vip.present.mapper.PresentConditionMapper;
import com.iqiyi.vip.present.model.PresentCondition;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public  class PresentConditionDao extends BaseDao<PresentCondition> {

    @Resource
    PresentConditionMapper presentConditionMapper;


    public List<PresentCondition> queryConditionByIds(List<Long> ids) {
        return presentConditionMapper.queryConditionByIds(ids);
    }


    public List<PresentCondition> queryConditions() {
        return presentConditionMapper.queryConditions();
    }
}
