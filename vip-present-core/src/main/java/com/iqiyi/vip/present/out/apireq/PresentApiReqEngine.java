package com.iqiyi.vip.present.out.apireq;

import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

import com.qiyi.vip.commons.web.context.UserInfo;
import com.iqiyi.vip.present.api.PassportApi;
import com.iqiyi.vip.present.consts.VipTypeEnum;
import com.iqiyi.vip.present.model.PresentProduct;
import com.iqiyi.vip.present.out.PresentVipRequest;
import com.iqiyi.vip.present.service.PresentProductService;
import com.iqiyi.vip.present.utils.ApplicationContextUtil;
import com.iqiyi.vip.present.utils.ConvertUtils;

/**
 * 转换PresentApiReq接口对应的实现类
 *
 * <AUTHOR> (<EMAIL>)
 * @date 2020/3/11 14:54
 */
public class PresentApiReqEngine {

    /**
     * 根据赠送的会员类型转换到对应的实现类
     */
    public static PresentApiReq build(int presentType) {
        PresentApiReq presentApiReq;
        if (presentType == VipTypeEnum.sport.getVipType()) {
            presentApiReq = new SportPresentApiReq();
        } else if (presentType == VipTypeEnum.book.getVipType()) {
            presentApiReq = new BookPresentApiReq();
        } else if (presentType == VipTypeEnum.qiyu.getVipType()) {
            presentApiReq = new QiyuPresentApiReq();
        } else {
            presentApiReq = new FreePresentApiReq();
        }
        return presentApiReq;
    }

    /**
     * 赠送接口公共处理方法
     */
    public static Map commonPresentParams(PresentVipRequest request) {
        Map<String, Object> commonParams = Maps.newHashMap();
        commonParams.put("tradeCode", request.getTradeCode());
        commonParams.put("buyPid", request.getBuyPid());
        commonParams.put("buyPidType", request.getBuyPidType());
        commonParams.put("buyPidAmt", request.getBuyPidAmt());
        commonParams.put("presentPid", request.getPid());
        commonParams.put("presentPidAmt", request.getAmount());
        commonParams.put("chargeType", request.getChargeType());
        commonParams.put("uid", request.getUid());
        commonParams.put("originCode", request.getOriginalOrderCode());
        commonParams.put("source", "vip-present");
        // 购买产品名称
        PresentProductService presentProductService = (PresentProductService) ApplicationContextUtil.getBean(PresentProductService.class);
        PresentProduct presentProduct = presentProductService.getByCode(request.getBuyPid());
        if (presentProduct != null && StringUtils.isNotBlank(presentProduct.getName())) {
            commonParams.put("buyPidName", presentProduct.getName());
        }
        // uid关联手机号
        PassportApi passportApi = (PassportApi) ApplicationContextUtil.getBean(PassportApi.class);
        UserInfo userInfo = passportApi.getByUid(request.getUid());
        if (userInfo != null && StringUtils.isNotBlank(userInfo.getPhone())) {
            commonParams.put("mobile", userInfo.getPhone());
        }
        // 过滤空k-v
        ConvertUtils.prepareRequestMap(commonParams);
        return commonParams;
    }

    /**
     * 取消接口公共处理方法
     */
    public static Map commonCancelParams(Map request) {
        request.remove("msgId");
        request.remove("presentOrder");
        request.remove("refundOrderCode");
        request.remove("sign");
        return request;
    }
}
