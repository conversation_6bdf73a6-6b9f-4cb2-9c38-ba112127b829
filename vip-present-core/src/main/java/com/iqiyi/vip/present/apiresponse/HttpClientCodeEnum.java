package com.iqiyi.vip.present.apiresponse;

import lombok.Getter;

/**
 * 本系统自定义错误码及描述
 * 错误码分两类：本系统自定义的错误码，外部系统返回的错误码HttpClientResponseCodeEnum
 *
 * <AUTHOR> on 2018/9/5
 */
public enum HttpClientCodeEnum {

    /**
     * 错误码定义
     */
    SUC("A00000", "成功"),

    //Http调用错误码以H开头定义
    HTTP_ERROR("H00001", "http接口异常"),
    HTTP_ERROR_CONNECT_TIME_OUT("H00002", "ConnectTimeoutException"),
    HTTP_ERROR_SOCKET_TIME_OUT("H00003", "SocketTimeoutException"),
    HTTP_ERROR_UNKNOWN_HOST("H00004", "UnknownHostException"),
    HTTP_ERROR_CONNECTION_REFUSED("H00005", "Connection refused"),
    HTTP_ERROR_NO_HTTP_RESPONSE("H00006", "NoHttpResponseException"),
    ;

    @Getter
    private String code;
    @Getter
    private String msg;

    HttpClientCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Getter
    private String alarmMsg;

    HttpClientCodeEnum(String code, String msg, String alarmMsg) {
        this.code = code;
        this.msg = msg;
        this.alarmMsg = alarmMsg;
    }
}
