package com.iqiyi.vip.present.config;

import com.iqiyi.vip.present.interceptor.SignatureInterceptor;
import com.qiyi.vip.trade.dataservice.client.DataServiceClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

/**
 * Created at: 2021-12-13
 *
 * <AUTHOR>
 */
@Configuration
public class RestTemplateConfig {

    private static final String CHANNEL_NAME = "vip-trade";

    private static final String SIGN_KEY = "asdacrthserg67";

    @Value("${vipTradeSdkSignKey}")
    private String vipTradeSdkSignKey;
    @Value("${tradeapiServerUrl}")
    private String tradeapiServerUrl;

    @Bean(name = "restTemplate")
    public RestTemplate restTemplate() {
        return getRestTemplate(500, 1000);
    }

    @Bean(name = "vipInfoRestTemplate")
    public RestTemplate vipInfoRestTemplate() {
        return getRestTemplate(500, 1000);
    }

    @Bean(name = "dataServiceRestTemplate")
    public RestTemplate dataServiceRestTemplate(){
        return getRestTemplate(500, 3000);
    }

    @Bean(name="lbRestTemplate")
    @LoadBalanced
    RestTemplate lbRestTemplate() {
        return getRestTemplate(500, 1000);
    }

    @Lazy(false)
    @Bean(name = {"notifyRestTemplate"})
    public RestTemplate notifyRestTemplate() {
        RestTemplate template = getRestTemplate(1000, 10000);
        template.getInterceptors().add(new SignatureInterceptor(CHANNEL_NAME, SIGN_KEY));
        return template;
    }

    @Bean("dataServiceClient")
    public DataServiceClient dataServiceClient(){
        DataServiceClient dataServiceClient = new DataServiceClient();
        dataServiceClient.setChannel("vip-present");
        dataServiceClient.setSignKey(vipTradeSdkSignKey);
        dataServiceClient.setServerUrl(tradeapiServerUrl);
        dataServiceClient.setNeedCheckRequest(true);
        dataServiceClient.setRestTemplate(dataServiceRestTemplate());
        return dataServiceClient;
    }

    private RestTemplate getRestTemplate(int connectTimeout, int readTimeout) {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(connectTimeout);
        requestFactory.setReadTimeout(readTimeout);
        RestTemplate restTemplate = new RestTemplate(requestFactory);
        restTemplate.setMessageConverters(transConverters(restTemplate.getMessageConverters()));
        return restTemplate;
    }

    private List<HttpMessageConverter<?>> transConverters(List<HttpMessageConverter<?>> oldMessageConverters) {
        MappingJackson2HttpMessageConverter jacksonConverter = new MappingJackson2HttpMessageConverter();
        List<MediaType> fastMediaTypes = new ArrayList<>();
        fastMediaTypes.add(MediaType.TEXT_HTML);
        fastMediaTypes.add(MediaType.APPLICATION_JSON);
        fastMediaTypes.add(MediaType.parseMediaType(MediaType.TEXT_PLAIN_VALUE + ";UTF-8"));
        jacksonConverter.setSupportedMediaTypes(fastMediaTypes);

        List<HttpMessageConverter<?>> messageConverters = new ArrayList<>();
        for (HttpMessageConverter converter : oldMessageConverters) {
            if (converter instanceof StringHttpMessageConverter) {
                messageConverters.add(new StringHttpMessageConverter(Charset.forName("UTF-8")));
            } else if (converter instanceof MappingJackson2HttpMessageConverter) {
                messageConverters.add(jacksonConverter);
            } else {
                messageConverters.add(converter);
            }
        }
        return messageConverters;
    }

}
