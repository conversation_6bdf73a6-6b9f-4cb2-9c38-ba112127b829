package com.iqiyi.vip.present.service.impl;

import com.iqiyi.vip.present.service.TradeSdkService;
import com.qiyi.vip.commons.constant.QueryConstants;
import com.qiyi.vip.trade.dataservice.client.DataServiceClient;
import com.qiyi.vip.trade.dataservice.client.dto.OrderDto;
import com.qiyi.vip.trade.dataservice.client.request.QueryOrdersRequest;
import com.qiyi.vip.trade.dataservice.client.response.QueryOrdersResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

@Service
@Slf4j
public class TradeSdkServiceImpl implements TradeSdkService {
    @Autowired
    private DataServiceClient dataServiceClient;

    @Override
    public OrderDto queryOrder(String orderCode) {
        log.info("queryOrder#start orderCode:{}",orderCode);
        QueryOrdersRequest ordersQueryRequest = new QueryOrdersRequest();
        ordersQueryRequest.addParam("orderCode", orderCode);
        ordersQueryRequest.addParam(QueryConstants.QUERY_PARAM_LIMIT, String.valueOf(1));
        QueryOrdersResponse response = dataServiceClient.execute(ordersQueryRequest);
        log.info("queryOrder#dataServiceClient.execute response:{}",response);
        if (response.isSuccessful()  && !CollectionUtils.isEmpty(response.getData())){
            return response.getData().get(0);
        }
        log.error("TradeSdkServiceImpl#queryOrder error,response:{}",response);
        return null;
    }
}
