package com.iqiyi.vip.present.manager;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.present.entity.CompensationStrategy;
import com.iqiyi.vip.present.mapper.CompensationStrategyMapper;
import com.iqiyi.vip.present.service.CompensationStrategyHandler;

@Component
@Slf4j
public class CompensationStrategyManager implements InitializingBean, ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Resource
    private CompensationStrategyMapper compensationStrategyMapper;

    private static final Map<String, CompensationStrategyHandler> compensationStrategyHandlerMap = Maps.newHashMap();

    @Autowired
    public CompensationStrategyManager(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("CompensationStrategyManager-init start!");
        applicationContext.getBeansOfType(CompensationStrategyHandler.class).values()
            .forEach(handler -> {
                log.info("AgreementHandlerFactory-init:register handler {} ",
                    handler.getClass().getSimpleName());
                compensationStrategyHandlerMap.put(handler.getClass().getSimpleName(), handler);
            });
        log.info("CompensationStrategyManager-init end!");
    }

    public CompensationStrategyHandler getHandler(String className) {
        return compensationStrategyHandlerMap.get(className);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public List<CompensationStrategy> findByRuleId(Integer ruleId) {
        return compensationStrategyMapper.findByRuleId(ruleId);
    }


}
