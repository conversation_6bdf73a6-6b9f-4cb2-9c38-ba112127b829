package com.iqiyi.vip.present.service;

import com.iqiyi.vip.present.model.PresentConfig;

import java.util.List;
import java.util.Map;

public interface PresentConfigService {
    PresentConfig queryConfigById(Long presentConfigId);

    Map<String,List<PresentConfig>> queryPresentConfig();

    List<PresentConfig> queryConfigListByGroup(String grouping);

    List<PresentConfig> queryConfigListByVipType(String bvipType);
    int save(PresentConfig config);

    Map<Long, PresentConfig> getIdPresentConfigMap();
}
