package com.iqiyi.vip.present.model;

import java.util.Date;

public class PresentProduct {
    private Integer id;

    private String name;

    private String nameTr;

    private String area;

    private String code;

    private Integer price;

    private Integer originalPrice;

    private Integer period;

    private Integer periodUnit;

    private Integer type;

    private Integer chargeType;

    private Integer subType;

    private Integer serviceType;

    private String businessCode;

    private String vipTypeCode;

    private String sourceVipTypeCode;

    private Integer status;

    private Date deadline;

    private String url;

    private Integer supportExp;

    private String supportType;

    private String businessType;

    private Integer scope;

    private Integer sourceSubType;

    private Integer rebuy;

    private Integer timeType;

    private Integer version;

    private String description;

    private Integer unit;

    private String cooperatorUid;

    private Date createTime;

    private Date updateTime;

    private String operator;

    private String currencyUnit;

    private String currencySymbol;

    private String businessCharge;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getNameTr() {
        return nameTr;
    }

    public void setNameTr(String nameTr) {
        this.nameTr = nameTr == null ? null : nameTr.trim();
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area == null ? null : area.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    public Integer getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(Integer originalPrice) {
        this.originalPrice = originalPrice;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public Integer getPeriodUnit() {
        return periodUnit;
    }

    public void setPeriodUnit(Integer periodUnit) {
        this.periodUnit = periodUnit;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getChargeType() {
        return chargeType;
    }

    public void setChargeType(Integer chargeType) {
        this.chargeType = chargeType;
    }

    public Integer getSubType() {
        return subType;
    }

    public void setSubType(Integer subType) {
        this.subType = subType;
    }

    public Integer getServiceType() {
        return serviceType;
    }

    public void setServiceType(Integer serviceType) {
        this.serviceType = serviceType;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode == null ? null : businessCode.trim();
    }

    public String getVipTypeCode() {
        return vipTypeCode;
    }

    public void setVipTypeCode(String vipTypeCode) {
        this.vipTypeCode = vipTypeCode == null ? null : vipTypeCode.trim();
    }

    public String getSourceVipTypeCode() {
        return sourceVipTypeCode;
    }

    public void setSourceVipTypeCode(String sourceVipTypeCode) {
        this.sourceVipTypeCode = sourceVipTypeCode == null ? null : sourceVipTypeCode.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getDeadline() {
        return deadline;
    }

    public void setDeadline(Date deadline) {
        this.deadline = deadline;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url == null ? null : url.trim();
    }

    public Integer getSupportExp() {
        return supportExp;
    }

    public void setSupportExp(Integer supportExp) {
        this.supportExp = supportExp;
    }

    public String getSupportType() {
        return supportType;
    }

    public void setSupportType(String supportType) {
        this.supportType = supportType == null ? null : supportType.trim();
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType == null ? null : businessType.trim();
    }

    public Integer getScope() {
        return scope;
    }

    public void setScope(Integer scope) {
        this.scope = scope;
    }

    public Integer getSourceSubType() {
        return sourceSubType;
    }

    public void setSourceSubType(Integer sourceSubType) {
        this.sourceSubType = sourceSubType;
    }

    public Integer getRebuy() {
        return rebuy;
    }

    public void setRebuy(Integer rebuy) {
        this.rebuy = rebuy;
    }

    public Integer getTimeType() {
        return timeType;
    }

    public void setTimeType(Integer timeType) {
        this.timeType = timeType;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public Integer getUnit() {
        return unit;
    }

    public void setUnit(Integer unit) {
        this.unit = unit;
    }

    public String getCooperatorUid() {
        return cooperatorUid;
    }

    public void setCooperatorUid(String cooperatorUid) {
        this.cooperatorUid = cooperatorUid == null ? null : cooperatorUid.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator == null ? null : operator.trim();
    }

    public String getCurrencyUnit() {
        return currencyUnit;
    }

    public void setCurrencyUnit(String currencyUnit) {
        this.currencyUnit = currencyUnit == null ? null : currencyUnit.trim();
    }

    public String getCurrencySymbol() {
        return currencySymbol;
    }

    public void setCurrencySymbol(String currencySymbol) {
        this.currencySymbol = currencySymbol == null ? null : currencySymbol.trim();
    }

    public String getBusinessCharge() {
        return businessCharge;
    }

    public void setBusinessCharge(String businessCharge) {
        this.businessCharge = businessCharge == null ? null : businessCharge.trim();
    }
}