package com.iqiyi.vip.present.service.impl;

import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.iqiyi.vip.present.apirequest.ManualPresentReq;
import com.iqiyi.vip.present.consts.EnumOrderStatusCode;
import com.iqiyi.vip.present.dao.PresentOrderDao;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.out.PresentVipRequest;
import com.iqiyi.vip.present.service.ManualPresentService;
import com.iqiyi.vip.present.service.PresentConfigService;
import com.iqiyi.vip.present.service.PresentPerformService;
import com.iqiyi.vip.present.task.PresentVipAsyncTask;
import com.iqiyi.vip.present.utils.CloudConfigUtil;
import com.iqiyi.vip.present.utils.PackagePresentVip;
import com.iqiyi.vip.repository.ClusterAsyncTaskManager;

/**
 * @Author: Lin Peihui
 * @Date: 2021/9/30
 */
@Service
@Slf4j
public class ManualPresentServiceImpl implements ManualPresentService {

    @Resource
    private PresentOrderDao presentOrderDao;
    @Resource
    private PresentPerformService presentPerformService;
    @Resource
    private ClusterAsyncTaskManager clusterAsyncTaskManager;
    @Resource
    private PresentConfigService presentConfigService;

    @Value("${table.present.order.total.count}")
    private int tableCount;

    @Override
    public void manualPresent(ManualPresentReq manualPresentReq) {
        Integer finishedTableNum = 0;
        Long count = 0L;
        Set<String> finishedTableSet = Sets.newHashSet();
        boolean stop = false;
        Map<Long, PresentConfig> presentConfigMap = presentConfigService.getIdPresentConfigMap();
        do {
            log.info("Manual present loop enter, params:{}", manualPresentReq);

            for (int index = 0; index < tableCount; index++) {
                stop = CloudConfigUtil.stopManualPresentOrders();
                if (stop) {
                    log.info("[Manual present] stop!, params:{}", manualPresentReq);
                    break;
                }
                Integer limitNum = CloudConfigUtil.queryManualPresentOrdersQueryLimit();
                String sleepLimit = CloudConfigUtil.manualPresentOrdersSleepLimit();
                List<String> sleepConditionList = Splitter.on("_").splitToList(sleepLimit);
                Integer countOfNeedSleep = Integer.valueOf(sleepConditionList.get(0));
                Integer sleepTime = Integer.valueOf(sleepConditionList.get(1));
                String tableNo = String.format("%02d", index);
                if (finishedTableSet.contains(tableNo)) {
                    continue;
                }
                List<PresentOrder> presentOrderList = presentOrderDao.queryManualPresentOrders(manualPresentReq.getStartTime(), manualPresentReq.getEndTime(), tableNo, manualPresentReq
                    .getConfigIdLists(), limitNum);
                if (CollectionUtils.isEmpty(presentOrderList)) {
                    finishedTableNum++;
                    finishedTableSet.add(tableNo);
                    log.info("[Manual present] table {} finished. params:{}", tableNo, manualPresentReq);
                    continue;
                }

                for (PresentOrder presentOrder : presentOrderList) {
                    Date taskRunTime = new Date();
                    PresentConfig presentConfig = presentConfigMap.get(presentOrder.getPresentConfigId());
                    Integer receiveType = presentPerformService.queryReceiveTypeByIdWithDefault(presentOrder.getPresentPerformId());
                    PresentVipRequest presentVipRequest = PackagePresentVip.packagePresentVipJobRequest(presentOrder, presentConfig, receiveType);
                    Integer oldStatus = presentOrder.getStatus();
                    presentOrder.setStatus(EnumOrderStatusCode.NO_PRESENT.getCode());
                    presentOrderDao.updateStatusFromOld(presentOrder.getUid(), presentOrder.getId(), presentOrder.getStatus(), oldStatus);
                    clusterAsyncTaskManager.insertTask(new PresentVipAsyncTask(presentVipRequest, presentOrder), taskRunTime);

                    count++;
                    if (count % countOfNeedSleep == 0) {
                        try {
                            log.info("[Manual present] sleep start. count:{}", count);
                            Thread.sleep(sleepTime);
                        } catch (InterruptedException e) {
                            log.error("Sleep error", e);
                        }
                    }
                }
                log.info("[Manual present] current count:{}", count);
            }
        } while (finishedTableNum < tableCount && !stop);
        log.info("[Manual present] end. count:{}, params:{}", count, manualPresentReq);
    }

}
