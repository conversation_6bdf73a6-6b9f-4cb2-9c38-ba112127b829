package com.iqiyi.vip.present.out.apireq;

import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.utils.CloudConfigUtil;
import org.springframework.core.env.Environment;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Map;

import com.iqiyi.vip.present.out.PresentVipRequest;
import com.iqiyi.vip.present.utils.EncodeUtils;

/**
 * 赠送体育会员（包含TV体育会员）接口请求信息
 *
 * <AUTHOR> (<EMAIL>)
 * @date 2020/3/11 15:00
 */
public class SportPresentApiReq implements PresentApiReq {

    private DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public String presentUrl(Environment environment) {
        return environment.getProperty("sport.present.url");
    }

    @Override
    public Map presentParams(PresentVipRequest request, Environment environment, PresentOrder presentOrder) {
        Map map = PresentApiReqEngine.commonPresentParams(request);
        map.put(DEADLINE_START_TIME, format.format(presentOrder.getDeadlineStartTime()));
        map.put(DEADLINE_END_TIME, format.format(presentOrder.getDeadlineEndTime()));
        map.put("presentPid", "ssports");
        //奇异果SDK充值体育会员时只充小屏权益
        if (CloudConfigUtil.justPresentSportsSmall(request.getPid())) {
            map.put("isSmall", "1");
        }
        map.put("sign", EncodeUtils.signMessage(map, environment.getProperty("sport.api.key")));
        return map;
    }

    @Override
    public String cancelUrl(Environment environment) {
        return environment.getProperty("sport.cancel.url");
    }

    @Override
    public Map cancelParams(Map request, Environment environment) {
        Map map = PresentApiReqEngine.commonCancelParams(request);
        map.put("sign", EncodeUtils.signMessage(map, environment.getProperty("sport.api.key")));
        return map;
    }

    @Override
    public Boolean isCancelLb(Environment environment) {
        return environment.getProperty("sport.cancel.url.lb",Boolean.class);
    }

    @Override
    public Boolean ispPresentLb(Environment environment) {
        return environment.getProperty("sport.present.url.lb",Boolean.class);
    }
}
