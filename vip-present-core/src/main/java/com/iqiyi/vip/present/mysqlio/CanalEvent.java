package com.iqiyi.vip.present.mysqlio;

import com.iqiyi.vip.present.utils.JacksonUtils;

/**
 * Created at: 2022-04-13
 *
 * <AUTHOR>
 */
public class CanalEvent<T> {

    private String schemaName;
    private String tableName;
    private String eventType;
    private T rowBefore;
    private T rowAfter;

    public CanalEvent() {
    }

    public CanalEvent(String schemaName, String tableName, String eventType, T rowBefore, T rowAfter) {
        this.schemaName = schemaName;
        this.tableName = tableName;
        this.eventType = eventType;
        this.rowBefore = rowBefore;
        this.rowAfter = rowAfter;
    }

    public String getSchemaName() {
        return schemaName;
    }

    public void setSchemaName(String schemaName) {
        this.schemaName = schemaName;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public T getRowBefore() {
        return rowBefore;
    }

    public void setRowBefore(T rowBefore) {
        this.rowBefore = rowBefore;
    }

    public T getRowAfter() {
        return rowAfter;
    }

    public void setRowAfter(T rowAfter) {
        this.rowAfter = rowAfter;
    }

    @Override
    public String toString() {
        return JacksonUtils.toJsonString(this);
    }
}
