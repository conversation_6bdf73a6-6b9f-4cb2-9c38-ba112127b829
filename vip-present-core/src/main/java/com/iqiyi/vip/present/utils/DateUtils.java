package com.iqiyi.vip.present.utils;

import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import com.iqiyi.vip.present.consts.EnumReceiveTypeCode;
import com.iqiyi.vip.present.data.PresentOrderData;
import com.iqiyi.vip.present.model.PresentOrder;

@Slf4j
public class DateUtils {

    private static final String PATTERN = "yyyy-MM-dd HH:mm:ss";

    private static final String DATE_PATTERN = "yyyy-MM-dd";

    public static String getCurrentTimeStr() {
        return getTimeStr(new Date());
    }

    public static Date getCurrentTime() {
        return new Date();
    }

    public static String getTimeStr(Date date) {
        return new SimpleDateFormat(PATTERN).format(date);
    }

    public static String getDateStr(Date date) {
        return new SimpleDateFormat(DATE_PATTERN).format(date);
    }

    public static String getYesterdayDateStr(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);
        String yesterday = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
        return yesterday;
    }

    /**
     * 获取当日sql.Date
     *
     * @return
     */
    public static java.sql.Date getSqlDate() {
        Date utilDate = new Date();
        return new java.sql.Date(utilDate.getTime());
    }

    /**
     * 获取当月第一天sql.Date
     *
     * @return
     */
    public static java.sql.Date getMonthFirstDay() {
        Calendar cal = Calendar.getInstance();
        cal.set(GregorianCalendar.DAY_OF_MONTH, 1);
        return new java.sql.Date(cal.getTime().getTime());
    }

    public static Date parse(String timeStr) throws ParseException {
        return new SimpleDateFormat(PATTERN).parse(timeStr);
    }

    public static Timestamp calculateTime(Timestamp begin, String times) {
        String offsets = times.substring(0, times.length() - 1);
        String units = times.substring(times.length() - 1);
        int unit = 0;
        int offset = Integer.parseInt(offsets);
        if (units.equals("h")) {
            unit = Calendar.HOUR;
        } else if (units.equals("m")) {
            unit = Calendar.MINUTE;
        } else if (units.equals("s")) {
            unit = Calendar.SECOND;
        } else if (units.equals("M")) {
            unit = Calendar.MONTH;
        } else if (units.equals("d")) {
            unit = Calendar.DATE;
        }

        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(begin.getTime());
        c.add(unit, offset);

        return new Timestamp(c.getTimeInMillis());
    }

    public static long getMonthStartTimeInMillis() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTimeInMillis();
    }

    public static Date getToday() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getDayEnd(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999);
        return c.getTime();
    }

    public static int getDayInterval(Date begin, Date end) {
        return (int) ((end.getTime() - begin.getTime()) / 1000 / 60 / 60 / 24);
    }

    public static long getTimeInterval(Timestamp begin, Timestamp end) {
        return (end.getTime() - begin.getTime());
    }


    public static Timestamp getCurrentTimestamp() {
        return new Timestamp(System.currentTimeMillis());
    }

    public static Date getDateBefore(Date d, Integer day) {
        Calendar now = Calendar.getInstance();
        now.setTime(d);
        now.set(Calendar.DATE, now.get(Calendar.DATE) - day);
        return now.getTime();
    }

    public static int getDay(PresentOrderData orderData, Integer receiveType) {
        long totalTimeInMillis = 0;
        long time = calPresentTime(orderData, receiveType);
        if (time > 0) {
            totalTimeInMillis += time;
        }
        return CalAmountUtils.calCeilAmountByDayMills(totalTimeInMillis);
    }

    public static int getRestDay(PresentOrder presentOrder, Integer receiveType) {
        long totalTimeInMillis = 0;
        long time = calPresentTime(presentOrder, receiveType);
        if (time > 0) {
            totalTimeInMillis += time;
        }
        return CalAmountUtils.calCeilAmountByDayMills(totalTimeInMillis);
    }

    public static long calPresentTime(PresentOrder presentOrder, Integer receiveType) {
        long now = System.currentTimeMillis();
        long endTime = presentOrder.getDeadlineEndTime().getTime();
        long startTime = presentOrder.getDeadlineStartTime().getTime();
        long payTime = presentOrder.getPayTime().getTime();
        long time;
        if (EnumReceiveTypeCode.DENGSHICHANG.getCode().equals(receiveType)) {
            time = (endTime - startTime);
        } else {
            //time = (endTime - startTime) - (now - payTime);
            time = (endTime - startTime) - Math.max(0, now - startTime);
        }
        return time;
    }

    /**
     * 获取日期间隔天数后的日期
     *
     * @param date      日期,为空表示当前
     * @param dayAmount 间隔天数,日期之后正数,之前用负数
     * @return 间隔后的date
     */
    public static String calculateTime(Date date, Integer dayAmount) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        // add方法中的第二个参数n中，正数表示该日期后n天，负数表示该日期的前n天
        calendar.add(Calendar.DATE, dayAmount);
        return com.iqiyi.kiwi.utils.DateHelper.getFormatDate(calendar.getTime(), "yyyy-MM-dd");
    }

}
