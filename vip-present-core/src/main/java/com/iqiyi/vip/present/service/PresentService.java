package com.iqiyi.vip.present.service;

import com.iqiyi.vip.present.apirequest.PresentRequest;
import com.iqiyi.vip.present.apiresponse.BaseResponse;
import com.iqiyi.vip.present.apiresponse.PresentResponse;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.data.PresentOrderData;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.model.PresentOrder;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface PresentService {
    void validateParam(PresentRequest request, BaseResponse response);

    Long getUid(HttpServletRequest servletRequest, BaseResponse response, PresentRequest request);

    List<PresentOrderData> queryVip(PresentRequest request, BaseResponse<List<PresentResponse>> response);

    void presentVip(PresentRequest request, BaseResponse response, List<PresentOrderData> orderDataList);

    void judgeReceiveBuyPresent(PresentRequest request, BaseResponse response);

    Integer queryTimeByMixTime(List<PresentOrder> orderList,
                               List<PresentOrderData> orderDataList, PresentConfig config);

    List<Long> checkOrderNeedPresent(OrderMessage orderMessage);
}
