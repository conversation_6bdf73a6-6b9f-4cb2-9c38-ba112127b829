package com.iqiyi.vip.present.enums;

/**
 * 是否自动执行
 *
 * @author: lin<PERSON><PERSON><PERSON>
 * @createTime: 2024/11/18
 */
public enum AutoExecuteEnum {
    MANUAL(0, "手动"),
    AUTO(1, "自动");

    private Integer type;
    private String desc;

    AutoExecuteEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
