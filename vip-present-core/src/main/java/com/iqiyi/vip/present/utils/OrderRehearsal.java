package com.iqiyi.vip.present.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;

/**
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/10/18 11:03
 */
public final class OrderRehearsal {

    /**
     * true:压测模式 false:非压测模式
     */
    public static final String PRESSURE_TEST_MODE = "Pressure-Test-Mode";


    /**
     * 是否是压测消息
     * @param messageExt 消息实体
     * @param isOpenPressureTestFilter 是否开启压测过滤
     * @return true:是压测消息、不需要处理，false:非压测消息、需要处理
     */
    public static boolean isPressureOrderMsg(MessageExt messageExt, boolean isOpenPressureTestFilter) {
        if (!isOpenPressureTestFilter) {
            return false;
        }
        String pressureTest = messageExt.getUserProperty(PRESSURE_TEST_MODE);
        return StringUtils.isNotBlank(pressureTest) && "true".equalsIgnoreCase(pressureTest);
    }

}
