package com.iqiyi.vip.present.utils;

import java.util.UUID;

/**
 * 产生唯一ID工具类
 * Created by liugu<PERSON>n on 2016/8/4.
 */
public final class UUidUtils {

    /**
     * 产生UUID
     *
     * @return
     */
    public static String getUUid() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    /**
     * 产生UUID
     *
     * @return
     */
    public static String getUUid(String prefix) {
        return prefix + "_" + UUID.randomUUID().toString().replaceAll("-", "");
    }

}
