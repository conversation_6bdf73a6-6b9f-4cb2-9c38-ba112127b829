package com.iqiyi.vip.present.consts;


import lombok.Getter;

@Getter
public enum EnumOrderStatusCode {
    /**
     * 状态枚举
     */
    ALREDY_PRESENT(0, "已赠送-handler,已领取-api"),
    NO_PRESENT(1, "未赠送"),
    PRESENTING(2, "已赠送，领取中"),
    REFUND_SUCCESS(3, "退款成功"),
    NOT_RECEIVE(4, "已赠送-handler,未领取-api");

    private Integer code;

    private String msg;

    EnumOrderStatusCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
