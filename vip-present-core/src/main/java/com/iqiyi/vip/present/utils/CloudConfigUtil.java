package com.iqiyi.vip.present.utils;

import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.vip.present.consts.PresentConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created at: 2020-11-27
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Lazy(false)
public class CloudConfigUtil implements ApplicationContextAware, InitializingBean {

    private static final String NEED_PROCESS_KIWI_PRESENT_GOLD_CONFIG_ID = "need.process.kiwi_present_gold_config_id";
    private static final String NEED_PROCESS_KIWI_PRESENT_GOLD_ORDER_RIGHT = "need.process.kiwi.present.gold.order.right";
    private static final String ONE_TABLE_PROCESS_KIWI_PRESENT_GOLD_ORDER_RIGHT_NUM = "one.table.process.kiwi.present.gold.order.right.num";
    private static final String SEARCH_UNSENT_GOLD_RIGHT_PRESENT_ORDER_BEGIN_TIME = "search.unsent.gold.right.present.order.beginTime";
    private static final String SEARCH_UNSENT_GOLD_RIGHT_PRESENT_ORDER_END_TIME = "search.unsent.gold.right.present.order.endTime";
    private static final String ORDER_MESSAGE_DELAY_TIME_IN_SECONDS = "order.message.delay.time.in.seconds";
    private static final String NATIONAL_DAY_DIAMOND_ACT_GIFT_BATCHNO = "national.day.diamond.act.gift.batchNo";
    private static final String NO_NEED_CREATE_PRESENT_SPORT_VIP_TASK = "no.need.create.present.sportVip.task";
    private static final String NO_NEED_CREATE_PRESENT_QIYU_VIP_TASK = "no.need.create.present.qiyuVip.task";
    /**
     * 补偿任务中过滤 refer 关键字（逗号分隔）
     */
    private static final String DEFAULT_COMPENSATION_REFER_FILTER_KEYWORDS = "vip_present,pressuretest,presentIqyPlatinum,specPresentGold";
    private static ApplicationContext context;
    /**
     * 配置中心非Hystrix配置
     */
    private static CloudConfig cloudConfig;

    /**
     * TV对外合作赠送爱奇艺星钻pid列表
     */
    private static final String PARTNER_GITV_SDK_DIAMOND_PRODUCTS = "partner.gitv.sdk.diamond.products";
    /**
     * 手动触发赠送-单表每次捞取的数据量
     */
    private static final String MANUAL_PRESENT_ORDERS_QUERY_LIMIT = "manual.present.orders.query.limit";
    /**
     * 手动触发赠送-是否停止捞数据
     */
    private static final String STOP_MANUAL_PRESENT_ORDERS = "stop.manual.present.orders";

    /**
     * 手动触发赠送-sleep控制
     */
    private static final String MANUAL_PRESENT_ORDERS_SLEEP_LIMIT = "manual.present.orders.sleep.limit";

    @Override
    public void afterPropertiesSet() throws Exception {
        cloudConfig = context.getBean("cloudConfig", CloudConfig.class);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }

    /**
     * 此PID只赠送体育小屏权益
     */
    public static boolean justPresentSportsSmall(String productCode) {
        String partnerGitvSdkDiamondProducts = cloudConfig.getProperty(PARTNER_GITV_SDK_DIAMOND_PRODUCTS, "9e408f2ec2c806d3");
        return partnerGitvSdkDiamondProducts.contains(productCode);
    }

    public static Long sendKiwiPresentGoldRightConfigId() {
        return cloudConfig.getLongProperty(NEED_PROCESS_KIWI_PRESENT_GOLD_CONFIG_ID, 91L);
    }

    public static boolean needProcessKiwiPresentGoldOrderRight() {
        return cloudConfig.getBooleanProperty(NEED_PROCESS_KIWI_PRESENT_GOLD_ORDER_RIGHT, false);
    }

    public static Integer getOneTableProcessKiwiPresentGoldOrderRightNum() {
        return cloudConfig.getIntProperty(ONE_TABLE_PROCESS_KIWI_PRESENT_GOLD_ORDER_RIGHT_NUM, 2300);
    }

    public static String searchUnsentGoldRightPresentOrderBeginTime() {
        return cloudConfig.getProperty(SEARCH_UNSENT_GOLD_RIGHT_PRESENT_ORDER_BEGIN_TIME, "2021-05-01 00:00:00");
    }

    public static String searchUnsentGoldRightPresentOrderEndTime() {
        return cloudConfig.getProperty(SEARCH_UNSENT_GOLD_RIGHT_PRESENT_ORDER_END_TIME, "2021-05-17 00:00:00");
    }

    public static Integer orderMessageDelayTimeInSeconds() {
        return cloudConfig.getIntProperty(ORDER_MESSAGE_DELAY_TIME_IN_SECONDS, 60 * 60 * 24);
    }

    public static String getNationalDayDiamondActGiftBatchNo() {
        return cloudConfig.getProperty(NATIONAL_DAY_DIAMOND_ACT_GIFT_BATCHNO, "");
    }

    public static boolean noNeedCreatePresentSportVipTask() {
        return cloudConfig.getBooleanProperty(NO_NEED_CREATE_PRESENT_SPORT_VIP_TASK, false);
    }

    public static boolean noNeedCretaePresentQiyuVipTask() {
        return cloudConfig.getBooleanProperty(NO_NEED_CREATE_PRESENT_QIYU_VIP_TASK, false);
    }

    public static Integer queryManualPresentOrdersQueryLimit() {
        return cloudConfig.getIntProperty(MANUAL_PRESENT_ORDERS_QUERY_LIMIT, 0);
    }

    public static Boolean stopManualPresentOrders() {
        return cloudConfig.getBooleanProperty(STOP_MANUAL_PRESENT_ORDERS, false);
    }

    public static String manualPresentOrdersSleepLimit() {
        return cloudConfig.getProperty(MANUAL_PRESENT_ORDERS_SLEEP_LIMIT, "100_1000");
    }

    public static List<Long> historyPresentConfigIds() {
        String values = cloudConfig.getProperty("history.present.config.ids", "114,115,116,117,118,119");
        return Arrays.stream(values.split(",")).map(Long::parseLong).collect(Collectors.toList());
    }

    public static Set<Integer> getEffectiveNotNeedPresentPayTypeSet() {
        String notNeedPresentPayTypes = cloudConfig.getProperty("not.need.present.payTypes", "");
        Date defaultEffectiveTime = null;
        try {
            defaultEffectiveTime = DateUtils.parse("2022-05-10 00:00:00");
        } catch (ParseException e) {
            log.error("Parse date error");
        }
        Date effectiveTime = cloudConfig.getDateProperty("not.need.present.payTypes.effective.time", defaultEffectiveTime);
        Date currentDate = DateUtils.getCurrentTime();
        if (StringUtils.isNotBlank(notNeedPresentPayTypes) && effectiveTime != null && currentDate.compareTo(effectiveTime) >= 0) {
            return Arrays.stream(notNeedPresentPayTypes.split(",")).map(Integer::parseInt).collect(Collectors.toSet());
        } else {
            return PresentConstants.FREE_PAY_TYPE_SET;
        }
    }

    /**
     * 获取补偿任务需要过滤的 refer 关键字集合
     *
     */
    public static Set<String> compensationReferFilterKeywords() {

        String configValue = cloudConfig.getProperty("compensation.refer.filter.keywords", DEFAULT_COMPENSATION_REFER_FILTER_KEYWORDS);
        return Arrays.stream(configValue.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
    }

    /**
     * 获取补偿任务需要过滤的 partner skuId 集合
     *
     */
    public static Set<String> compensationPartnerSkuFilterIds() {
        String configValue = cloudConfig.getProperty("compensation.partner.sku.filter.ids", "sku_415924347249665058");
        if (StringUtils.isBlank(configValue)) {
            return Collections.emptySet();
        }
        return Arrays.stream(configValue.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
    }

    /**
     * 获取补偿任务需要过滤的会员类型集合
     *
     */
    public static Set<Integer> compensationFilterVipTypes() {
        String configValue = cloudConfig.getProperty("compensation.filter.vip.types", PresentConstants.BASIC_TV_VIP_TYPE + "," + PresentConstants.BASIC_VIP_TYPE);
        if (StringUtils.isBlank(configValue)) {
            return Collections.emptySet();
        }
        return Arrays.stream(configValue.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .map(Integer::parseInt)
                .collect(Collectors.toSet());
    }

    /**
     * 获取补偿任务需要过滤的 partner productId 集合
     *
     */
    public static Set<Long> compensationPartnerProductFilterIds() {
        String configValue = cloudConfig.getProperty("compensation.partner.product.filter.ids", "1900135");
        if (StringUtils.isBlank(configValue)) {
            return Collections.emptySet();
        }
        return Arrays.stream(configValue.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .map(Long::parseLong)
                .collect(Collectors.toSet());
    }

}
