package com.iqiyi.vip.present.mapper;

import com.iqiyi.vip.present.entity.CheckRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface CheckRecordMapper {
    int insertSelective(CheckRecord record);

    int updateCheckRecord(CheckRecord record);

    List<CheckRecord> findRecordByStatus(@Param("status") List<Integer> status, @Param("startTime") String startTime);

    CheckRecord findByOrderCodeAndRuleId(@Param("orderCode") String orderCode, @Param("ruleId") Integer ruleId);
    
    /**
     * 删除指定日期之前的检查记录
     * @param date 截止日期
     * @return 删除的记录数
     */
    int deleteRecordsBefore(@Param("date") Date date);
}
