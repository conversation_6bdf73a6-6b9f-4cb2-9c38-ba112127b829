package com.iqiyi.vip.present.manager;

import cn.hutool.core.date.DateUtil;
import com.iqiyi.vip.present.entity.CheckRecord;
import com.iqiyi.vip.present.enums.CheckRecordStatusEnum;
import com.iqiyi.vip.present.mapper.CheckRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class CheckRecordManager {

    @Resource
    private CheckRecordMapper checkRecordMapper;

    /**
     * 保存check 记录
     */
    public void saveCheckRecord(List<CheckRecord> checkRecords) {
        for (CheckRecord record : checkRecords) {
            try {
                checkRecordMapper.insertSelective(record);
            } catch (DuplicateKeyException e) {
                log.warn("重复的补偿记录, ruleId: {},orderCode:{}", record.getRuleId(), record.getOrderCode());
            } catch (Exception e) {
                log.error("保存补偿记录异常,ruleId: {},orderCode:{}", record.getRuleId(), record.getOrderCode(), e);
            }
        }
    }

    /**
     * 查询状态为待补偿、补偿中、补偿失败的任务
     */
    public List<CheckRecord> findRecordToReset() {
        List<Integer> statusToReset = CheckRecordStatusEnum.getCompensationProcessStatuses();
        Date date = DateUtils.addMinutes(new Date(), -10); // 默认-10分钟
        String startTime = DateUtil.formatDateTime(date);
        return checkRecordMapper.findRecordByStatus(statusToReset, startTime);
    }



    public int updateCheckRecord(CheckRecord checkRecord) {
        return checkRecordMapper.updateCheckRecord(checkRecord);
    }

    /**
     * 根据订单号和规则ID查询检查记录
     */
    public CheckRecord findByOrderCodeAndRuleId(String orderCode, Integer ruleId) {
        return checkRecordMapper.findByOrderCodeAndRuleId(orderCode, ruleId);
    }

    /**
     * 删除指定日期之前的检查记录
     * @param date 截止日期
     * @return 删除的记录数
     */
    public int deleteRecordsBefore(Date date) {
        try {
            return checkRecordMapper.deleteRecordsBefore(date);
        } catch (Exception e) {
            log.error("删除历史检查记录异常", e);
            return 0;
        }
    }
}
