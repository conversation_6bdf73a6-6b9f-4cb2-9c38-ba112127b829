package com.iqiyi.vip.present.consts;

import lombok.Getter;

@Getter
public enum EnumVipDirectlySendCode {
    /**
     * 赠送类型
     */
    DIRECTLY_SEND(0, "直接赠送"),
    RECEIVE_ONE(1, "首次领取"),
    RECEIVE_EVERY(2, "每次领取");

    private Integer code;

    private String msg;

    EnumVipDirectlySendCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getMsg(Integer code) {
        for (EnumVipDirectlySendCode send : values()) {
            if (send.getCode().equals(code)) {
                return send.getMsg();
            }
        }
        return null;
    }

}
