package com.iqiyi.vip.present.consts;

/**
 * 订单上的Type字段枚举
 *
 * @Author: <PERSON>
 * @Date: 2021/3/4
 */
public enum TypeOfOrderEnum {
    NORMAL("1", "普通订单"),
    TEST("-1", "测试订单"),
    VIP_RIGHT_DONATION("-2", "权益转赠"),
    VIP_RIGHT_TRANSFER("-3", "权益转移"),
    SETTLE("11", "芝麻GO结算单"),
    ZHIMA_GO_DUT("12", "芝麻GO代扣单"),
    WECHAT_PAY_SCORE_CREATE("13", "微信支付分创单"),
    WECHAT_PAY_SCORE_COMPLETE("14", "微信支付分结单"),
    ;

    private String code;
    private String desc;

    TypeOfOrderEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean isVipRightTransfer(String type) {
        return VIP_RIGHT_TRANSFER.getCode().equals(type);
    }

    public static boolean isVipRightDonation(String type) {
        return VIP_RIGHT_DONATION.getCode().equals(type);
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
