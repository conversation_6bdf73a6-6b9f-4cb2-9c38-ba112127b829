package com.iqiyi.vip.present.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Map;

import com.iqiyi.vip.present.consts.EnumOrderStatusCode;
import com.iqiyi.vip.present.consts.TaskPoolTypeEnum;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.out.PresentVipRequest;
import com.iqiyi.vip.present.out.PresentVipResponse;
import com.iqiyi.vip.present.out.apireq.PresentApiReq;
import com.iqiyi.vip.present.out.apireq.PresentApiReqEngine;
import com.iqiyi.vip.present.service.PresentOrderService;
import com.iqiyi.vip.present.service.RestTemplateService;
import com.iqiyi.vip.present.utils.ApplicationContextUtil;
import com.iqiyi.vip.threadpool.AbstractTask;

@Slf4j
@Data
@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class PresentVipAsyncTask extends AbstractTask {

    private PresentVipRequest presentVipRequest;

    private PresentOrder presentOrder;

    public PresentVipAsyncTask() {

    }

    public PresentVipAsyncTask(PresentVipRequest presentVipRequest, PresentOrder presentOrder) {
        this.presentOrder = presentOrder;
        this.presentVipRequest = presentVipRequest;
    }

    @Override
    protected boolean execute() {
        if (presentVipRequest.getAmount() == 0) {
            log.info("PresentVipAsyncTask ignored, amount为0，presentVipRequest: {}, presentOrder: {}", JSONObject.toJSONString(presentVipRequest), JSONObject.toJSONString(presentOrder));
            return true;
        }

        int oldStatus = presentOrder.getStatus();
        Environment environment = (Environment) ApplicationContextUtil.getBean(Environment.class);

        int presentType = NumberUtils.toInt(presentOrder.getPresentType());
        PresentApiReq presentApiReq = PresentApiReqEngine.build(presentType);
        String url = presentApiReq.presentUrl(environment);
        Boolean lb = presentApiReq.ispPresentLb(environment);
        Map reqMap = presentApiReq.presentParams(presentVipRequest, environment, presentOrder);

        PresentVipResponse presentVipResponse;
        RestTemplateService restTemplateService = (RestTemplateService) ApplicationContextUtil.getBean(RestTemplateService.class);
        log.info("PresentVipAsyncTask execute [msgId:{}][http-presentVipRequest:{}]", presentOrder.getMsgId(), JSONObject.toJSONString(reqMap));
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            presentVipResponse = restTemplateService.postForObject(url, reqMap, PresentVipResponse.class,lb,presentOrder.getIsPressureTest());
        } catch (Exception e) {
            log.error("PresentVipAsyncTask [msgId:{}][异步任务奇悦服务异常，重试][reqMap:{}]", presentOrder.getMsgId(), JSONObject.toJSONString(reqMap), e);
            return false;
        }

        if (presentVipResponse != null && presentVipResponse.isUserNotFound()) {
            log.info("PresentVipAsyncTask [msgId:{}][异步任务奇悦服务用户不存在][reqMap:{}] [response:{}]", presentOrder.getMsgId(),
                    JSONObject.toJSONString(reqMap), JSON.toJSONString(presentVipResponse));
            return true;
        }

        if (presentVipResponse == null || !presentVipResponse.isSuccess()) {
            log.error("PresentVipAsyncTask [msgId:{}][异步任务奇悦服务异常，重试][reqMap:{}] [response:{}]", presentOrder.getMsgId(), JSONObject
                .toJSONString(reqMap), presentVipResponse == null ? "" : JSON.toJSONString(presentVipResponse));
            return false;
        }
        log.info("PresentVipAsyncTask execute [msgId:{}][http-presentVipRequest:{}] [response:{}][costTime:{}ms]", presentOrder.getMsgId(), JSONObject
            .toJSONString(reqMap), JSONObject.toJSONString(presentVipResponse), stopWatch.getTime());

        PresentOrderService presentOrderService = (PresentOrderService) ApplicationContextUtil.getBean(PresentOrderService.class);
        presentOrder.setStatus(EnumOrderStatusCode.ALREDY_PRESENT.getCode());
        if (presentVipResponse.getData() != null) {
            String presentOrderCode = presentVipResponse.getData().getOrderCode();
            presentOrder.setPresentOrderCode(presentOrderCode);
        }
        log.info("PresentVipAsyncTask execute [msgId:{}][修改数据库presentOrder:{}]", presentOrder.getMsgId(), JSONObject.toJSONString(presentOrder));

        presentOrderService.updateOrderStatus(presentOrder, oldStatus);
        log.info("PresentVipAsyncTask execute [msgId:{}][修改数据库成功presentOrder:{}]", presentOrder.getMsgId(), JSONObject.toJSONString(presentOrder));
        log.info("[bcpcheck]presentOrderCode={},tradeCode={}", presentOrder.getPresentOrderCode(), presentOrder.getPresentTradeCode());
        return true;
    }


    @Override
    public void deserialize(String data) throws IllegalArgumentException {
        JSONObject obj = JSON.parseObject(data);
        this.presentOrder = obj.getObject("presentOrder", PresentOrder.class);
        this.presentVipRequest = obj.getObject("presentVipRequest", PresentVipRequest.class);
    }

    @Override
    public String serialize() {
        JSONObject obj = new JSONObject();
        obj.put("presentOrder", presentOrder);
        obj.put("presentVipRequest", presentVipRequest);
        return obj.toJSONString();
    }

    @Override
    public int getDefaultPoolType() {
        return TaskPoolTypeEnum.PRESENT_VIP.getType();
    }

    @Data
    public static class TaskData {

        private PresentVipRequest request;
        private PresentOrder presentOrder;
    }

}
