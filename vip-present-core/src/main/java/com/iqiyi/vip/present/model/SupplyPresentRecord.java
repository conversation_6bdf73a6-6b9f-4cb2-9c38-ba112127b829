package com.iqiyi.vip.present.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 补赠记录
 *
 * <AUTHOR>
 * @date 2024/2/26
 * @apiNote
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplyPresentRecord {

    private Long id;
    private String tradeCode;
    private Long uid;
    private Integer source;
    private Integer buyType;
    private Integer vipType;
    private Integer status;
    private String orderCode;

    private Integer amount;
    private Integer payType;
    private Long presentConfigId;
    private Long presentConditionId;
    private Date deadlineStartTime;
    private Date deadlineEndTime;
    private Date createTime;
    private Date updateTime;
}
