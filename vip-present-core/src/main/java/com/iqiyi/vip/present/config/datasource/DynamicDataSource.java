package com.iqiyi.vip.present.config.datasource;

import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

/**
 * <AUTHOR> kongwenqiang
 * Date:2017/4/2
 * Time:下午12:55
 * Mail:<EMAIL>
 * Description: 动态数据源
 */
public class DynamicDataSource extends AbstractRoutingDataSource {

    /**
     * 获取当前数据源的key
     * @return
     */
    @Override
    protected Object determineCurrentLookupKey() {
       /*
        * DynamicDataSourceContextHolder代码中使用setDataSourceKey
        * 设置当前的数据源，在路由类中使用getDataSourceKey进行获取，
        * 交给AbstractRoutingDataSource进行注入使用。
        */
        return DynamicDataSourceContextHolder.getDataSourceKey();
    }
}
