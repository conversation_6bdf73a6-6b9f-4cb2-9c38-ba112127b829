package com.iqiyi.vip.present.dao;

import com.iqiyi.vip.present.mapper.PresentConfigMapper;
import com.iqiyi.vip.present.model.PresentConfig;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;


@Repository
public  class PresentConfigDao extends BaseDao<PresentConfig> {

    @Resource
    PresentConfigMapper presentConfigMapper;


    public List<PresentConfig> queryPresentConfig() {
        return presentConfigMapper.queryPresentConfig();
    }

    public List<PresentConfig> queryConfigListByGroup(String grouping) {
        return presentConfigMapper.queryConfigListByGroup(grouping);
    }

    public List<PresentConfig> queryConfigListByVipTypeList(List<String> list) {
        return presentConfigMapper.queryConfigListByVipTypeList(list);
    }
    public List<PresentConfig> queryAllPresentConfig() {
        return presentConfigMapper.queryAllPresentConfig();
    }

    @Override
    public PresentConfig selectByPrimaryKey(Long id) {
        return presentConfigMapper.selectByPrimaryKey(id);
    }

    @Override
    public int insertSelective(PresentConfig config) {
        return presentConfigMapper.insertSelective(config);
    }


}
