package com.iqiyi.vip.present.model;

import lombok.Data;

import java.util.Date;

@Data
public class PresentConfig {

    private Long id;

    private String bvipType;

    private String pvipType;

    private String buyCode;

    private String presentCode;

    private String remark;

    private Integer status;

    private Integer handlerStatus;

    private String grouping;

    private Date updateTime;

    private Date createTime;

    private Integer payType;

    private String conditionIds;

    private Integer calAmountType;

    private Date startTime;//生效时间

    private Date endTime;//失效时间

    /**
     * 校验是否有效
     */
    public boolean checkValid(Date orderDate) {
        if ((startTime == null || orderDate.compareTo(startTime) >= 0)
            && (endTime == null || orderDate.compareTo(endTime) < 0)
            && status == 1 && handlerStatus == 1) {//状态1为有效
            return true;
        }
        return false;
    }

}