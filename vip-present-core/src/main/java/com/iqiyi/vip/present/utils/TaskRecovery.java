package com.iqiyi.vip.present.utils;

import lombok.extern.slf4j.Slf4j;

import com.iqiyi.vip.present.dao.PresentAsyncTaskDao;
import com.iqiyi.vip.present.model.PresentAsyncTask;
import com.iqiyi.vip.present.task.async.Task;

/**
 * Created at: 2022-04-14
 *
 * <AUTHOR>
 */
@Slf4j
public class TaskRecovery {

    public static Task recoverTask(PresentAsyncTask asyncTask, PresentAsyncTaskDao asyncTaskDao) {
        Task taskInstance = null;
        try {
            Class<Task> taskClass = (Class<Task>) Class.forName(asyncTask.getClassName());
            taskInstance = taskClass.newInstance();
            taskInstance.deserialize(asyncTask.getData());
            taskInstance.setPersistenceId(asyncTask.getId());
            taskInstance.setExecCount(asyncTask.getExecCount());
            taskInstance.setAsyncTaskDao(asyncTaskDao);
            return taskInstance;
        } catch (Exception e) {
            log.error("recoverTask failed, asyncTask:{}", asyncTask, e);
        }
        return taskInstance;
    }

}
