package com.iqiyi.vip.present.utils;

import org.springframework.cglib.beans.BeanMap;

import java.util.Iterator;
import java.util.Map;

public class ConvertUtils {
    /**
     *
     * @Title: mapToObject
     * @Description: TODO(map转换为bean)
     * @return T    返回类型
     * @param map
     * @param beanClass
     * @return
     * @throws Exception
     */
    public static <T> T mapToObject(Map<String, Object> map, Class<T> beanClass) {
        if (map == null) {
            return null;
        }

        T obj = null;
        try {
            obj = beanClass.newInstance();
            org.apache.commons.beanutils.BeanUtils.populate(obj, map);
        } catch (InstantiationException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }


        return obj;
    }

    /**
     *
     * @Title: objectToMap
     * @Description: TODO(bean转换为Map)
     * @return Map<?,?>    返回类型
     * @param obj
     * @return
     */
    public static Map<?, ?> objectToMap(Object obj) {
        if(obj == null) {
            return null;
        }
        return BeanMap.create(obj);
    }


    public static void prepareRequestMap(Map<String, Object> reqMap) {
        // rm null value pair & wrap value with String
        Iterator<Map.Entry<String, Object>> iterator = reqMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Object> entry = iterator.next();
            if (entry.getValue() == null) {
                iterator.remove();
            } else if (!(entry.getValue() instanceof String)) {
                reqMap.put(entry.getKey(), String.valueOf(entry.getValue()));
            }
        }
    }

}
