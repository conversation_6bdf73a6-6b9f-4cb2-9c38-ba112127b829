package com.iqiyi.vip.present.service.impl;

import com.google.common.collect.Lists;

import com.iqiyi.vip.present.config.GuavaCacheConfig;
import com.iqiyi.vip.present.consts.PresentConstants;
import com.iqiyi.vip.present.dao.PresentConfigDao;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.service.PresentConfigService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.drools.util.StringUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@CacheConfig(cacheManager = GuavaCacheConfig.GUAVA_CACHE_MANAGER)
@Service
public class PresentConfigServiceImpl implements PresentConfigService {
    @Resource
    PresentConfigDao presentConfigDao;

    @Cacheable(value = "queryConfigById", keyGenerator = "customKeyGenerator")
    @Override
    public PresentConfig queryConfigById(Long presentConfigId) {
        if (presentConfigId == null || PresentConstants.PRESENT_CONFIG_ID_DEFAULT.equals(presentConfigId)) {
            return null;
        }
        return presentConfigDao.selectByPrimaryKey(presentConfigId);
    }

    @Override
    public Map<String, List<PresentConfig>> queryPresentConfig() {
        List<PresentConfig> list = presentConfigDao.queryPresentConfig();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Map<String, List<PresentConfig>> map = new HashMap<>();
        for (PresentConfig presentConfig : list) {
            if (CollectionUtils.isEmpty(map.get(presentConfig.getBvipType()))) {
                List<PresentConfig> configList = new ArrayList<PresentConfig>();
                map.put(presentConfig.getBvipType(), configList);
            }
            map.get(presentConfig.getBvipType()).add(presentConfig);
        }
        return map;
    }

    @Override
    public List<PresentConfig> queryConfigListByGroup(String grouping) {
        return presentConfigDao.queryConfigListByGroup(grouping);
    }

    @Override
    public List<PresentConfig> queryConfigListByVipType(String bvipType) {
        List<PresentConfig> result = Lists.newArrayList();
        String[] bvipTypes = StringUtils.split(bvipType, ",");
        if (ArrayUtils.isEmpty(bvipTypes)) {
            return result;
        }
        return presentConfigDao.queryConfigListByVipTypeList(Lists.newArrayList(bvipTypes));
    }

    @Override
    @Transactional
    public int save(PresentConfig config) {
        return presentConfigDao.insertSelective(config);
    }


    @Override
    public Map<Long, PresentConfig> getIdPresentConfigMap() {
        List<PresentConfig> list = presentConfigDao.queryPresentConfig();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Map<Long,  PresentConfig> map = new HashMap<>();
        for (PresentConfig presentConfig : list) {
            map.put(presentConfig.getId(), presentConfig);
        }
        return map;
    }
}
