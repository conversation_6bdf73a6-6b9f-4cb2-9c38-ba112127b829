package com.iqiyi.vip.present.handler;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;

import com.iqiyi.vip.present.mysqlio.CanalEvent;
import com.iqiyi.vip.present.utils.CanalEventUtil;

/**
 * Created at: 2022-04-13
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractEventHandler<T> implements CanalEventHandler {

    @Override
    public boolean accept(CanalEvent<?> event) {
        if (event == null) {
            return false;
        }
        if (CanalEventUtil.isDelete(event.getEventType()) && event.getTableName().equals(this.getTableName())) {
            log.warn("Receiver Delete Event, Can Not Process This Event.");
            return false;
        }
        return true;
    }

    @Override
    public void handCanalEvent(String event) {
        CanalEvent<T> canalEvent = getConcreteType(event);
        if (!childAccept(canalEvent)) {
            return;
        }
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            doHandleEvent(canalEvent);
        } finally {
            log.info("EventHandler:{} cost:{}", getClass().getSimpleName(), stopWatch.getTime());
        }
    }

    protected abstract CanalEvent<T> getConcreteType(String event);

    protected abstract boolean childAccept(CanalEvent<T> event);

    /**
     * 子类实现具体的事件处理逻辑
     * @param event {@link CanalEvent}
     */
    protected abstract void doHandleEvent(CanalEvent<T> event);
}
