package com.iqiyi.vip.present.config;

import com.google.common.cache.CacheBuilder;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.lang.Nullable;

import java.util.Collection;
import java.util.Collections;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 自定义GuavaCacheManager，用于Spring Boot 2.x
 * 替代Spring Boot 1.x中被移除的GuavaCacheManager
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
public class GuavaCacheManager implements CacheManager {

    private final ConcurrentMap<String, Cache> cacheMap = new ConcurrentHashMap<>(16);
    private volatile boolean dynamic = true;
    private CacheBuilder<Object, Object> cacheBuilder = CacheBuilder.newBuilder();

    /**
     * 构造函数
     */
    public GuavaCacheManager() {
    }

    /**
     * 构造函数，指定缓存名称
     */
    public GuavaCacheManager(String... cacheNames) {
        setCacheNames(java.util.Arrays.asList(cacheNames));
    }

    /**
     * 设置缓存名称集合
     */
    public void setCacheNames(@Nullable Collection<String> cacheNames) {
        if (cacheNames != null) {
            for (String name : cacheNames) {
                this.cacheMap.put(name, createGuavaCache(name));
            }
            this.dynamic = false;
        }
    }

    /**
     * 设置CacheBuilder
     */
    public void setCacheBuilder(CacheBuilder<Object, Object> cacheBuilder) {
        this.cacheBuilder = cacheBuilder;
    }

    /**
     * 获取CacheBuilder
     */
    public CacheBuilder<Object, Object> getCacheBuilder() {
        return this.cacheBuilder;
    }

    @Override
    public Cache getCache(String name) {
        Cache cache = this.cacheMap.get(name);
        if (cache == null && this.dynamic) {
            synchronized (this.cacheMap) {
                cache = this.cacheMap.get(name);
                if (cache == null) {
                    cache = createGuavaCache(name);
                    this.cacheMap.put(name, cache);
                }
            }
        }
        return cache;
    }

    @Override
    public Collection<String> getCacheNames() {
        return Collections.unmodifiableSet(this.cacheMap.keySet());
    }

    /**
     * 创建Guava Cache
     */
    protected Cache createGuavaCache(String name) {
        return new GuavaCache(name, createNativeGuavaCache());
    }

    /**
     * 创建原生Guava Cache
     */
    protected com.google.common.cache.Cache<Object, Object> createNativeGuavaCache() {
        return this.cacheBuilder.build();
    }

    /**
     * Spring Cache接口的Guava实现
     */
    static class GuavaCache implements Cache {

        private final String name;
        private final com.google.common.cache.Cache<Object, Object> cache;

        public GuavaCache(String name, com.google.common.cache.Cache<Object, Object> cache) {
            this.name = name;
            this.cache = cache;
        }

        @Override
        public String getName() {
            return this.name;
        }

        @Override
        public Object getNativeCache() {
            return this.cache;
        }

        @Override
        @Nullable
        public ValueWrapper get(Object key) {
            Object value = this.cache.getIfPresent(key);
            return (value != null ? new SimpleValueWrapper(value) : null);
        }

        @Override
        @SuppressWarnings("unchecked")
        @Nullable
        public <T> T get(Object key, @Nullable Class<T> type) {
            Object value = this.cache.getIfPresent(key);
            if (value != null && type != null && !type.isInstance(value)) {
                throw new IllegalStateException("Cached value is not of required type [" + type.getName() + "]: " + value);
            }
            return (T) value;
        }

        @Override
        @SuppressWarnings("unchecked")
        @Nullable
        public <T> T get(Object key, java.util.concurrent.Callable<T> valueLoader) {
            try {
                return (T) this.cache.get(key, () -> {
                    try {
                        return valueLoader.call();
                    } catch (Exception ex) {
                        throw new RuntimeException(ex);
                    }
                });
            } catch (Exception ex) {
                throw new ValueRetrievalException(key, valueLoader, ex.getCause());
            }
        }

        @Override
        public void put(Object key, @Nullable Object value) {
            if (value != null) {
                this.cache.put(key, value);
            }
        }

        @Override
        @Nullable
        public ValueWrapper putIfAbsent(Object key, @Nullable Object value) {
            if (value == null) {
                return get(key);
            }
            Object existing = this.cache.asMap().putIfAbsent(key, value);
            return (existing != null ? new SimpleValueWrapper(existing) : null);
        }

        @Override
        public void evict(Object key) {
            this.cache.invalidate(key);
        }

        @Override
        public void clear() {
            this.cache.invalidateAll();
        }

        /**
         * 简单的值包装器
         */
        private static class SimpleValueWrapper implements ValueWrapper {
            private final Object value;

            public SimpleValueWrapper(@Nullable Object value) {
                this.value = value;
            }

            @Override
            @Nullable
            public Object get() {
                return this.value;
            }
        }
    }
}
