package com.iqiyi.vip.present.out.apireq;

import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.out.PresentVipRequest;
import com.iqiyi.vip.present.utils.EncodeUtils;
import org.springframework.core.env.Environment;

import java.util.Map;

/**
 * Created at: 2020-10-15
 *
 * <AUTHOR>
 */
public class QiyuPresentApiReq implements PresentApiReq {

    @Override
    public String presentUrl(Environment environment) {
        return environment.getProperty("qiyu.present.url");
    }

    @Override
    public Map presentParams(PresentVipRequest request, Environment environment, PresentOrder presentOrder) {
        Map map = PresentApiReqEngine.commonPresentParams(request);
        map.put("sign", EncodeUtils.signMessage(map, environment.getProperty("qiyu.api.key")));
        return map;
    }

    @Override
    public String cancelUrl(Environment environment) {
        return environment.getProperty("qiyu.cancel.url");
    }

    @Override
    public Map cancelParams(Map request, Environment environment) {
        Map map = PresentApiReqEngine.commonCancelParams(request);
        map.put("sign", EncodeUtils.signMessage(map, environment.getProperty("qiyu.api.key")));
        return map;
    }

    @Override
    public Boolean isCancelLb(Environment environment) {
        return environment.getProperty("qiyu.cancel.url.lb",Boolean.class);
    }

    @Override
    public Boolean ispPresentLb(Environment environment) {
        return environment.getProperty("qiyu.present.url.lb",Boolean.class);
    }
}
