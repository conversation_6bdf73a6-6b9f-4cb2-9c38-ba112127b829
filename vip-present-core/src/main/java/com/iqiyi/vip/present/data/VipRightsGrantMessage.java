package com.iqiyi.vip.present.data;


import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.out.PresentVipRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VipRightsGrantMessage {
    private PresentVipRequest request;
    private PresentOrder presentOrder;
}
