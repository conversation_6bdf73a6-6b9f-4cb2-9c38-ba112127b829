package com.iqiyi.vip.present.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * compensation_strategy
 */
@Data
public class CompensationStrategy implements Serializable {
    private Integer id;

    private Integer ruleId;

    private String name;

    private String description;

    private String className;

    /**
     * 自动执行，0：不自动，1：自动
     */
    private Integer autoExecute;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 状态，1：有效，0：无效
     */
    private Byte status;

    private static final long serialVersionUID = 1L;

    public boolean autoExecute() {
        return autoExecute == 1;
    }
}
