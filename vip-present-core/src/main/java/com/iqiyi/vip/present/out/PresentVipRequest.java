package com.iqiyi.vip.present.out;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

@Data
public class PresentVipRequest {
    private String tradeCode;
    private Integer payType;
    private String actCode;
    private String pid;
    private String aid;
    private Integer amount;
    private Long uid;
    private String platform;
    private String originalOrderCode;
    private String fv;
    private Boolean isTest;

    private String buyPid;
    private Integer buyPidAmt;
    private Integer buyPidType;
    private Integer chargeType;
    private String originalGiftBatchNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
