package com.iqiyi.vip.present.mapper;

import com.iqiyi.vip.present.model.SupplyPresentRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SupplyPresentRecordMapper {

    int insertSelective(SupplyPresentRecord record);

    int updateSelective(SupplyPresentRecord record);

    List<SupplyPresentRecord> selectByUid(@Param("uid") Long uid);

    List<SupplyPresentRecord> selectByUserIds(List<Long> list);

    SupplyPresentRecord selectByUidAndSource(@Param("uid") Long uid, @Param("source") Integer source);

}