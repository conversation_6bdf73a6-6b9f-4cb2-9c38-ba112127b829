package com.iqiyi.vip.present.service.impl;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.iqiyi.vip.present.config.datasource.DynamicDataSourceContextHolder;
import com.iqiyi.vip.present.constants.ShardingConstants;
import com.iqiyi.vip.present.entity.*;
import com.iqiyi.vip.present.enums.AutoExecuteEnum;
import com.iqiyi.vip.present.enums.CheckRecordStatusEnum;
import com.iqiyi.vip.present.manager.CheckRecordManager;
import com.iqiyi.vip.present.manager.CheckRuleManager;
import com.iqiyi.vip.present.manager.CompensationStrategyManager;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.model.dto.CompensateDTO;
import com.iqiyi.vip.present.model.dto.ProcessContext;
import com.iqiyi.vip.present.model.dto.ResetProcessContext;
import com.iqiyi.vip.present.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CompensateServiceImpl extends CompensateService {

    private static final int AUTO_EXECUTE = 1;

    @Resource
    private CompensationStrategyManager compensationStrategyManager;

    @Resource
    private CheckRuleManager checkRuleManager;

    @Resource
    private OrderRepositoryService orderRepositoryService;

    @Resource
    private CheckRecordManager checkRecordManager;

    @Resource
    private AlterService alterService;
    @Resource
    private CompensationStatusService compensationStatusService;

    @Override
    public void compensate(ProcessContext processContext) {
        CheckRule checkRule = processContext.getCheckRule();
        List<Order> orderList = processContext.getOrderList();
        List<CheckRecord> checkRecords = processContext.getCheckRecords();
        if (CollectionUtils.isEmpty(orderList) && CollectionUtils.isEmpty(checkRecords)) {
            log.info("补偿订单列表为空, processContext:{}" , processContext);
            return;
        }
        Integer compensateTimeType = processContext.getCompensateTimeType();
        if (AutoExecuteEnum.MANUAL.getType().equals(compensateTimeType)) {
            manualCompensate(processContext);
            return;
        }
        autoCompensate(orderList, checkRule);
    }

    @Override
    public void checkAndRest(ResetProcessContext resetProcessContext) {
        Map<Integer, List<CheckRecord>> recordByRule = resetProcessContext.getCheckRecords()
            .stream()
            .collect(Collectors.groupingBy(CheckRecord::getRuleId));
        for (Entry<Integer, List<CheckRecord>> entry : recordByRule.entrySet()) {
            Integer ruleId = entry.getKey();
            CheckRule checkRule = checkRuleManager.getById(ruleId);
            if (Objects.isNull(checkRule)) {
                log.info("待重置记录，对应规则为空,recordId:{}", ruleId);
                return;
            }
            List<String> compensatedOrders = Lists.newArrayList();
            List<CheckRecord> toCheckRecord = entry.getValue();
            CompensationStrategy compensationStrategy = compensationStrategyManager.findByRuleId(checkRule.getId()).get(0);
            CompensationStrategyHandler handler = compensationStrategyManager.getHandler(compensationStrategy.getClassName());
            Date minCreateTime = null;
            Date maxUpdateTime = null;
            for (CheckRecord checkRecord : toCheckRecord) {
                String orderCode = checkRecord.getOrderCode();
                try {
                    // 计算分库分表信息并切换数据源
                    ShardingInfo shardingInfo = orderRepositoryService.calculateShardingInfo(orderCode);
                    if (shardingInfo == null) {
                        log.warn("无法计算订单号 {} 的分库分表信息", orderCode);
                        continue;
                    }

                    int databaseIndex = shardingInfo.getDatabaseIndex();
                    String tableName = shardingInfo.getTableName();

                    String dbName = ShardingConstants.SHARDING_DATABASE_NAME_PREFIX + databaseIndex;
                    DynamicDataSourceContextHolder.setDataSourceKey(dbName);

                    Order order;
                    try {
                        order = orderRepositoryService.findByOrderCode(orderCode, tableName);
                    } finally {
                        DynamicDataSourceContextHolder.removeDataSourceKey();
                    }

                    // 根据补偿策略构建查询参数
                    PresentOrder queryParam = handler.buildQueryPresentOrderParam(order);

                    if (compensationStatusService.checkCompensationStatus(order, checkRecord, queryParam)) {
                        compensatedOrders.add(orderCode);
                        checkRecord.setStatus(CheckRecordStatusEnum.COMPENSATED.getStatus());
                        checkRecordManager.updateCheckRecord(checkRecord);
                        if (minCreateTime == null || checkRecord.getCreateTime().before(minCreateTime)) {
                            minCreateTime = checkRecord.getCreateTime();
                        }
                        if (maxUpdateTime == null || checkRecord.getUpdateTime().after(maxUpdateTime)) {
                            maxUpdateTime = checkRecord.getUpdateTime();
                        }
                    }
                } catch (Exception e) {
                    log.error("重置补偿记录异常,orderCode:{}", orderCode, e);
                }
            }
            if (CollectionUtils.isNotEmpty(compensatedOrders)) {
                LocalDateTime startTime = LocalDateTime.now();
                LocalDateTime endTime = LocalDateTime.now();
                Duration duration = Duration.between(startTime, endTime);
                int cost = (int) duration.toMillis();
                ProcessContext context = ProcessContext.builder()
                    .checkRule(checkRule)
                    .time(DateUtil.formatDateTime(maxUpdateTime))
                    .okCount(compensatedOrders.size())
                    .totalCount(toCheckRecord.size())
                    .cost(cost)
                    .timeRange(DateUtil.formatDateTime(minCreateTime).substring(11)  + "-" + DateUtil.formatDateTime(maxUpdateTime).substring(11))
                    .build();
                alterService.buildAndSendRuleOK(compensatedOrders, context);
            }
        }
    }

    private void manualCompensate(ProcessContext processContext) {
        List<CheckRecord> checkRecords = processContext.getCheckRecords();
        CheckRule checkRule = processContext.getCheckRule();
        CompensationStrategy compensationStrategy = processContext.getCompensationStrategy();
        if (compensationStrategy == null) {
            compensationStrategy = getCompensationStrategyByRuleId(checkRule.getId());
        }
        if (compensationStrategy == null) {
            log.info("手动补偿，对应补偿策略为空, ruleId:{}", checkRule.getId());
            return;
        }
        CompensationStrategyHandler handler = compensationStrategyManager.getHandler(compensationStrategy.getClassName());
        for (CheckRecord checkRecord : checkRecords) {
            try {
                // 计算分库分表信息并切换数据源
                String orderCode = checkRecord.getOrderCode();
                ShardingInfo shardingInfo = orderRepositoryService.calculateShardingInfo(orderCode);
                if (shardingInfo == null) {
                    log.warn("无法计算订单号 {} 的分库分表信息", orderCode);
                    continue;
                }

                int databaseIndex = shardingInfo.getDatabaseIndex();
                String tableName = shardingInfo.getTableName();
                String dbName = ShardingConstants.SHARDING_DATABASE_NAME_PREFIX + databaseIndex;
                DynamicDataSourceContextHolder.setDataSourceKey(dbName);

                Order order;
                try {
                    // 根据CheckRecord的orderCode获取Order
                    order = orderRepositoryService.findByOrderCode(orderCode, tableName);
                } finally {
                    DynamicDataSourceContextHolder.removeDataSourceKey();
                }

                handler.compensate(new CompensateDTO(order, checkRecord, null));
            } catch (Exception e) {
                log.error("手动补偿执行失败, orderCode: {}", checkRecord.getOrderCode(), e);
                // 更新CheckRecord状态为补偿失败
                checkRecord.setStatus(CheckRecordStatusEnum.COMPENSATE_FAIL.getStatus());
                checkRecordManager.updateCheckRecord(checkRecord);
            }
        }
    }

    private CompensationStrategy getCompensationStrategyByRuleId(Integer ruleId) {
        List<CompensationStrategy> strategyList = compensationStrategyManager.findByRuleId(ruleId);
        return strategyList.stream().findFirst().orElse(null);
    }

    private void autoCompensate(List<Order> orderList, CheckRule checkRule) {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        List<CompensationStrategy> compensationStrategies = compensationStrategyManager.findByRuleId(checkRule.getId());

        //对外合作订单要求顺序性，所以不能并发执行补偿
        for (Order order : orderList) {
            CheckRecord checkRecord = checkRecordManager.findByOrderCodeAndRuleId(order.getOrderCode(), checkRule.getId());
            doAutoCompensate(order, compensationStrategies, checkRecord);
        }
    }

    private void doAutoCompensate(Order order, List<CompensationStrategy> compensationStrategies, CheckRecord checkRecord) {
        for (CompensationStrategy strategy : compensationStrategies) {
            if (strategy.getAutoExecute().equals(AUTO_EXECUTE)) {
                CompensationStrategyHandler handler = compensationStrategyManager.getHandler(strategy.getClassName());
                handler.compensate(new CompensateDTO(order, checkRecord, null));
                break;
            }
        }
    }
}
