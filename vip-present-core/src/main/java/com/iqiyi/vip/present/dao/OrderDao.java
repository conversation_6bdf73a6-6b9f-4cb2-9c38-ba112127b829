package com.iqiyi.vip.present.dao;

import com.iqiyi.vip.present.entity.PersistOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;

/**
 * 订单数据访问接口 - 使用MyBatis实现
 * 整合了原来的CrudRepository和OrderRepositoryExtension的所有方法
 *
 * <AUTHOR> Assistant
 */
@Mapper
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class, transactionManager = "transactionManager")
public interface OrderDao {

    PersistOrder[] findOrderToCheck(@Param("tableName") String tableName,
                                   @Param("startTime") Timestamp startTime,
                                   @Param("endTime") Timestamp endTime,
                                   @Param("condition") String condition);

    // 原CrudRepository的核心方法，改为MyBatis实现
    PersistOrder findByOrderCode(@Param("tableName") String tableName, @Param("orderCode") String orderCode);
}
