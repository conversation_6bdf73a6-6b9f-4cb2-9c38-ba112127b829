package com.iqiyi.vip.present.apiresponse;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * 分页
 *
 * <AUTHOR>
 * @date 2019/7/15 17:25
 */

public class Page<T> {
    //-- 公共变量 --//
    /**排序的方式-顺序*/
    public static final String ASC = "asc";
    /**排序的方式-倒序*/
    public static final String DESC = "desc";

    //-- 分页参数 --//
    protected int pageNo = 1;
    protected int pageSize = -1;
    protected String orderBy = null;
    protected String order = null;
    //-- 返回结果 --//
    protected List<T> result = Lists.newArrayList();
    protected long totalCount = -1;

    /**
     * 构造函数
     */
    public Page() {
    }

    /**
     * 构造函数
     * @param pageSize 每页的条数
     */
    public Page(int pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 构造函数
     * @param pageNo
     * @param pageSize
     * @param orderBy
     * @param order
     */
    public Page(int pageNo, int pageSize, String orderBy, String order) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.orderBy = orderBy;
        this.order = order;
    }

    /**
     * 获得当前页的页号,序号从1开始,默认为1.
     * @return pageNo
     */
    public int getPageNo() {
        return pageNo;
    }

    /**
     * 设置当前页的页号,序号从1开始,低于1时自动调整为1.
     * @param pageNo 当前页的页号
     */
    public void setPageNo(final int pageNo) {
        this.pageNo = pageNo;

        if (pageNo < 1) {
            this.pageNo = 1;
        }
    }

    /**
     * 返回Page对象自身的setPageNo函数,可用于连续设置。
     * @param thePageNo 当前页
     */
    public Page<T> pageNo(final int thePageNo) {
        setPageNo(thePageNo);
        return this;
    }

    /**
     * 获得每页的记录数量, 默认为-1.
     * @return 每页的记录数
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * 设置每页的记录数量.
     * @param pageSize 设置记录数
     */
    public void setPageSize(final int pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 返回Page对象自身的setPageSize函数,可用于连续设置。
     * @param thePageSize 记录数
     * @return Page<T>
     */
    public Page<T> pageSize(final int thePageSize) {
        setPageSize(thePageSize);
        return this;
    }

    /**
     * 根据pageNo和pageSize计算当前页第一条记录在总结果集中的位置,序号从1开始.
     * @return 当前页第一条记录在总结果集中的位置
     */
    public int getFirst() {
        return ((pageNo - 1) * pageSize) + 1;
    }

    /**
     * 获得排序字段,无默认值. 多个排序字段时用','分隔.
     * @return 排序方式
     */
    public String getOrderBy() {
        return orderBy;
    }

    /**
     * 设置排序字段,多个排序字段时用','分隔.
     * @param orderBy 排序的字段名
     */
    public void setOrderBy(final String orderBy) {
        this.orderBy = orderBy;
    }

    /**
     * 返回Page对象自身的setOrderBy函数,可用于连续设置。
     * @param theOrderBy 排序属性
     * @return Page<T>
     */
    public Page<T> orderBy(final String theOrderBy) {
        setOrderBy(theOrderBy);
        return this;
    }

    /**
     * 获得排序方向, 无默认值.
     * @return 排序方向
     */
    public String getOrder() {
        return order;
    }

    /**
     * 获得页内的记录列表.
     * @return List<T>
     */
    public List<T> getResult() {
        return result;
    }

    /**
     * 设置页内的记录列表.
     * @param result 记录列表
     */
    public void setResult(final List<T> result) {
        this.result = result;
    }

    /**
     * 获得总记录数, 默认值为-1.
     * @return 总记录数
     */
    public long getTotalCount() {
        return totalCount;
    }

    /**
     * 设置总记录数.
     * @param totalCount 设置总记录数
     */
    public void setTotalCount(final long totalCount) {
        this.totalCount = totalCount;
    }

    /**
     * 根据pageSize与totalCount计算总页数, 默认值为-1.
     * @return 总页数
     */
    public long getTotalPages() {
        if (totalCount < 0) {
            return -1;
        }

        long count = totalCount / pageSize;
        if (totalCount % pageSize > 0) {
            count++;
        }
        return count;
    }

    /**
     * 是否还有下一页.
     * @return true：有下一页，false:无下一页
     */
    public boolean isHasNext() {
        return (pageNo + 1 <= getTotalPages());
    }

    /**
     * 取得下页的页号, 序号从1开始.
     * 当前页为尾页时仍返回尾页序号.
     * @return 下页的页号
     */
    public int getNextPage() {
        if (isHasNext()) {
            return pageNo + 1;
        } else {
            return pageNo;
        }
    }

    /**
     * 是否还有上一页.
     * @return true:有上一页，false:没有上一页
     */
    public boolean isHasPre() {
        return (pageNo - 1 >= 1);
    }

    /**
     * 取得上页的页号, 序号从1开始.
     * 当前页为首页时返回首页序号.
     * @return 上页的页号
     */
    public int getPrePage() {
        if (isHasPre()) {
            return pageNo - 1;
        } else {
            return pageNo;
        }
    }
}
