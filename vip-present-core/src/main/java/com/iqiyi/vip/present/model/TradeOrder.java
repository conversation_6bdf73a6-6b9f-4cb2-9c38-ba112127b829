package com.iqiyi.vip.present.model;

import lombok.Data;

import java.sql.Timestamp;

/**
 * 交易订单对象
 *
 * <AUTHOR>
 * @date 2019/7/9 18:05
 */
@Data
public class TradeOrder {
    private String orderCode;
    private String tradeCode;
    private Long userId;
    private Integer fee;
    private Integer status;
    private Timestamp createTime = new Timestamp(System.currentTimeMillis());
    private Long serviceId;
    private String serviceOrderNo;
    private String returnUrl;
    private String notifyUrl;
    private String notifyResult;
    private String userIp;
    private Timestamp payTime;
    private Timestamp tradeCreate;
    private Timestamp tradePayment;
    private Integer payType;
    private Long platform;
    private Long channel;
    private Long pushChannel;
    private Long gateway;
    private String cartCode;
    private String tradeNo;
    private String centerCode;
    private Integer type;
    private Integer couponFee = 0;
    private String fv;
    private Long productId;
    private String name;
    private Integer originalPrice;
    private Integer realFee;
    private Integer productFee;
    private Long contentId;
    private Integer productType;
    private String partner;
    private String contentUrl;
    private String pictureUrl;
    private Integer renewalsFlag = 0;
    private Integer amount = 1;
    private Timestamp deadline;
    private Timestamp startTime;
    private String businessValues;
    private Integer autoRenew;
    private String frVersion;
    private String sendMsgId;
    private Long id;
    private String accountId;
    private Long beanCount;
    private Timestamp updateTime = new Timestamp(System.currentTimeMillis());
}