package com.iqiyi.vip.present.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.MessageQueueSelector;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;

import java.nio.charset.StandardCharsets;

/**
 * Created at: 2022-04-13
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class RMQMsgSender {

    protected boolean doSend(DefaultMQProducer producer, Message message) {
        StopWatch stopWatch = StopWatch.createStarted();
        String topic = message.getTopic();
        try {
            SendResult sendResult = null;
            try {
                sendResult = producer.send(message);
            } catch (MQClientException | RemotingException | MQBrokerException | InterruptedException e) {
                //发送异常进行重试一次
                sendResult = producer.send(message);
            }
            if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
                String msgContent = StringUtils.toEncodedString(message.getBody(), StandardCharsets.UTF_8);
                log.error("send msg to topic: {} failed, cost: {}ms, sendResult: {}, msgKeys:{}, msgContent: {}",
                    topic, stopWatch.getTime(), sendResult, message.getKeys(), msgContent);
                return false;
            }
            log.info("send msg to topic: {} success, msgId: {}, msgKeys: {}, cost: {}ms", topic, sendResult.getMsgId(), message.getKeys(), stopWatch.getTime());
            return true;
        } catch (Exception e) {
            String msgContent = StringUtils.toEncodedString(message.getBody(), StandardCharsets.UTF_8);
            log.error("send msg to topic: {} occur exception, msgContent: {}", topic, JacksonUtils.toJsonString(msgContent), e);
            throw (e instanceof RuntimeException) ? (RuntimeException) e : new RuntimeException(e.getMessage(), e);
        }
    }

    protected boolean doSend(DefaultMQProducer producer, Message message, MessageQueueSelector msgQueueSelector, Object arg) {
        StopWatch stopWatch = StopWatch.createStarted();
        String topic = message.getTopic();
        try {
            SendResult sendResult = null;
            try {
                sendResult = producer.send(message, msgQueueSelector, arg);
            } catch (MQClientException | RemotingException | MQBrokerException | InterruptedException e) {
                //发送异常进行重试一次
                sendResult = producer.send(message, msgQueueSelector, arg);
            }
            if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
                String msgContent = StringUtils.toEncodedString(message.getBody(), StandardCharsets.UTF_8);
                log.error("send msg to topic: {} failed, cost: {}ms, sendResult: {}, msgKeys:{}, msgContent: {}",
                    topic, stopWatch.getTime(), sendResult, message.getKeys(), msgContent);
                return false;
            }
            //TODO 测试完毕后删除日志中的 msg body
            log.info("send msg to topic: {} success, msgId: {}, msgKeys: {}, body:{}, cost: {}ms", topic, sendResult.getMsgId(),
                message.getKeys(), StringUtils.toEncodedString(message.getBody(), StandardCharsets.UTF_8), stopWatch.getTime());
            return true;
        } catch (Exception e) {
            String msgContent = StringUtils.toEncodedString(message.getBody(), StandardCharsets.UTF_8);
            log.error("send msg to topic: {} occur exception, msgContent: {}", topic, JacksonUtils.toJsonString(msgContent), e);
            throw (e instanceof RuntimeException) ? (RuntimeException) e : new RuntimeException(e.getMessage(), e);
        }
    }

}
