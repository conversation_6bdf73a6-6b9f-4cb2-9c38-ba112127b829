package com.iqiyi.vip.present.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Map;


@Component
@Lazy(value = false)
public class ApplicationContextUtil implements ApplicationContextAware {
	private static ApplicationContext context = null;

	private ApplicationContextUtil() {
	}

	@Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }

	public static ApplicationContextUtil getInstance() {
		return (ApplicationContextUtil)context.getBean("applicationContextUtil");
	}


    public static Object getBean(String name){
        return context.getBean(name);
    }

    public static Object getBean(Class classz){
        return context.getBean(classz);
    }

    public static <T> Map<String, T> getBeansOfType(Class<T> clazz) {
        Map<String, T> beansOfType = context.getBeansOfType(clazz);
        return beansOfType;
    }

    public static String getEnvProperty(String key) {
        Environment env = context.getEnvironment();
        return env.getProperty(key);
    }

}
