package com.iqiyi.vip.present.handler;

import com.iqiyi.vip.present.mysqlio.CanalEvent;

/**
 * Created at: 2022-04-13
 *
 * <AUTHOR>
 */
public interface CanalEventHandler {

    /**
     * 获取该Handler处理的表名
     */
    String getTableName();

    /**
     * 判断是否可以处理改事件
     * @param event {@link CanalEvent}
     */
    boolean accept(CanalEvent<?> event);

    /**
     * 处理mysql IO　事件
     * @param event
     */
    void handCanalEvent(String event);

}
