package com.iqiyi.vip.present.service.impl;

import com.iqiyi.vip.present.config.GuavaCacheConfig;
import com.iqiyi.vip.present.consts.EnumReceiveTypeCode;
import com.iqiyi.vip.present.dao.PresentPerformDao;
import com.iqiyi.vip.present.model.PresentPerform;
import com.iqiyi.vip.present.service.PresentPerformService;

import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CacheConfig(cacheManager = GuavaCacheConfig.GUAVA_CACHE_MANAGER)
@Service
public class PresentPerformServiceImpl implements PresentPerformService {
    @Resource
    PresentPerformDao presentPerformDao;

    @Cacheable(value = "queryPresentPerformById", keyGenerator = "customKeyGenerator")
    @Override
    public PresentPerform queryPresentPerformById(Long performId) {
        return presentPerformDao.queryPresentPerformById(performId);
    }

    @Override
    public Integer queryReceiveTypeByIdWithDefault(Long performId) {
        Integer receiveType = EnumReceiveTypeCode.SHISHI.getCode();
        PresentPerform presentPerform = this.queryPresentPerformById(performId);
        if (presentPerform != null && presentPerform.getReceiveType() != null) {
            receiveType = presentPerform.getReceiveType();
        }
        return receiveType;
    }

    @Override
    public Map<Long, PresentPerform> queryPerforms() {
        List<PresentPerform> performs = presentPerformDao.queryPerforms();
        return performs.stream().collect(Collectors.toMap(PresentPerform::getId, p -> p));
    }
}
