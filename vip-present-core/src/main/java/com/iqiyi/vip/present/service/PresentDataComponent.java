package com.iqiyi.vip.present.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.present.consts.EnumOrderStatusCode;
import com.iqiyi.vip.present.consts.EnumOrderTypeCode;
import com.iqiyi.vip.present.consts.EnumVipDirectlySendCode;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.model.PresentCondition;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.model.PresentPerform;
import com.iqiyi.vip.present.out.PresentVipRequest;
import com.iqiyi.vip.present.task.PresentVipAsyncTask;
import com.iqiyi.vip.present.utils.PackagePresentVip;

/**
 * Created at: 2022-04-12
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PresentDataComponent {

    @Resource
    PresentConfigService presentConfigService;
    @Resource
    PresentRecordService presentRecordService;

    public PresentVipAsyncTask.TaskData getPresentOrderTask(OrderMessage message,
        PresentConfig presentConfig, PresentPerform presentPerform, PresentCondition presentCondition) {

        PresentVipRequest request = PackagePresentVip.packagePresentVipRequest(message, presentConfig);
        Integer status = getOrderStatus(message, presentConfig, presentPerform);
        PresentOrder presentOrder = PackagePresentVip.packagePresentOrderRequest(message, presentConfig,
            status, null, request, EnumOrderTypeCode.YES.getCode());
        presentOrder.setPresentConditionId(presentCondition.getId());
        presentOrder.setPresentPerformId(presentPerform.getId());
        if (presentPerform.getAmount() != null) {
            presentOrder.setProductAmount(presentPerform.getAmount());
        }
        PresentVipAsyncTask.TaskData taskData = new PresentVipAsyncTask.TaskData();
        taskData.setRequest(request);
        taskData.setPresentOrder(presentOrder);
        return taskData;
    }

    /**
     * 获取订单的状态
     */
    private Integer getOrderStatus(OrderMessage message, PresentConfig presentConfig, PresentPerform presentPerform) {
        Integer status = EnumOrderStatusCode.NO_PRESENT.getCode();
        if (EnumVipDirectlySendCode.RECEIVE_EVERY.getCode().equals(presentPerform.getIsDirectlySend())) {
            status = EnumOrderStatusCode.NOT_RECEIVE.getCode();
        }
        // 一组的判断如果组内领取过一次,则直接赠送
        else if (EnumVipDirectlySendCode.RECEIVE_ONE.getCode().equals(presentPerform.getIsDirectlySend())) {
            //组装在一个group里的买赠关系，构建一个recordList，判断是否都领取过
            List<PresentConfig> configListByGroup = presentConfigService.queryConfigListByGroup(presentConfig.getGrouping());
            boolean flag = presentRecordService.queryByGroup(configListByGroup, message.getUid());
            if (!flag) {
                status = EnumOrderStatusCode.NOT_RECEIVE.getCode();
            }
        }
        return status;
    }

}
