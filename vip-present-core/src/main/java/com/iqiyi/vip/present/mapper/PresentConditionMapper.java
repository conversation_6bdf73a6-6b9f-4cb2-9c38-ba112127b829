package com.iqiyi.vip.present.mapper;

import com.iqiyi.vip.present.model.PresentCondition;

import java.util.List;

public interface PresentConditionMapper extends BaseMapper{
    int deleteByPrimaryKey(Long id);

    int insert(PresentCondition record);

    int insertSelective(PresentCondition record);

    PresentCondition selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PresentCondition record);

    int updateByPrimaryKey(PresentCondition record);

    List<PresentCondition> queryConditionByIds(List<Long> list);

    List<PresentCondition> queryConditions();
}