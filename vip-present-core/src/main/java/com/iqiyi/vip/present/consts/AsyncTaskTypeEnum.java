package com.iqiyi.vip.present.consts;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum AsyncTaskTypeEnum {


    SYNC_CONTENT_TO_BOSS(111, 1, new String[]{"1s", "3s", "10s", "3m"}),
    SYNC_VIP_ORDER_TO_PRESENT(112, 1, new String[]{"1s", "5s", "10s", "30s", "1m", "5m", "30m", "1h", "3h", "6h"});

    private Integer poolType;
    private Integer priority;
    private String[] retryInterval;
}
