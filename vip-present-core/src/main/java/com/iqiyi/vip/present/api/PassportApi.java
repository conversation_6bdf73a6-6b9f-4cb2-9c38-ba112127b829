package com.iqiyi.vip.present.api;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.math.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import com.qiyi.vip.commons.web.context.UserInfo;
import com.iqiyi.vip.present.apiresponse.HttpClientResponseDTO;
import com.iqiyi.vip.present.commonInner.HttpResultVo;
import com.iqiyi.vip.present.out.passport.PassportUserVo;
import com.iqiyi.vip.present.service.RestTemplateService;
import com.iqiyi.vip.present.utils.EncodeUtils;

/**
 * passport API User: zhangdaoguang Date: 2019/3/18 Time: 14:09
 */
@Slf4j
@Service
public class PassportApi {

    public static final String INTERNATIONAL_SITE_FLAG = "i18n";
    private static final String REQ_PARAM_FIELDS = "fields";
    private static final String REQ_PARAM_FIELDS_VALUE = "private";
    private static final String SPRING_PROFILES_ACTIVE = "spring.profiles.active";

    @Value("${passport.url.byUid}")
    private String byUidUrl;
    @Value("${passport.source:boss}")
    private String source;
    @Value("${passport.agent.type:167}")
    private String agentType;
    @Value("${passport.secret.key:bSsIlop6JD8tW7BdPLQ2foAL}")
    private String secretKey;

    @Value("${passport.user.info}")
    private String passportUserInfoUrl;

    @Autowired
    private RestTemplateService restTemplateService;

    /**
     * 根据authcookie获取UID
     */
    public Long getUid(String ticket) {
        if (StringUtils.isBlank(ticket)) {
            return null;
        }

        //根据p00001获取uid
        Map<String, String> param = Maps.newTreeMap();
        param.put("authcookie", ticket);
        param.put("agenttype", agentType);
        param.put("source", source);
        param.put("timestamp", String.valueOf(System.currentTimeMillis()));
        param.put("sign", this.sign(param, secretKey));
        PassportUserVo passportUserVo = restTemplateService.postForResult(passportUserInfoUrl,
                param, new ParameterizedTypeReference<HttpResultVo<PassportUserVo>>() {
                },false);
        if (passportUserVo == null || passportUserVo.getUserinfo() == null) {
            return null;
        }
        Long uid = passportUserVo.getUserinfo().getUid();
        log.info("[passport][getUid][uid: {}]", uid);
        return uid;
    }

    /**
     * 根据条件获取用户信息,条件为：uid, nickname, username <br> http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********
     */
    public UserInfo getByUid(Long uid) {
        Map paramMap = Maps.newHashMap();
        paramMap.put("uid", String.valueOf(uid));
        paramMap.put("t", this.passportParamT());
        paramMap.put("source", source);
        paramMap.put(REQ_PARAM_FIELDS, REQ_PARAM_FIELDS_VALUE);
        paramMap.put("agenttype", agentType);
        paramMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
        paramMap.put("sign", this.sign(paramMap, secretKey));
        final int RETRY_TIMES = 2;
        for (int i = 0; i < RETRY_TIMES; i++) {
            HttpClientResponseDTO<Map<String, Object>> responseDTO = restTemplateService.postForObject(byUidUrl, paramMap, HttpClientResponseDTO.class,false);
            if (isBusinessFailed(responseDTO)) {
                continue;
            }
            Map<String, Object> data = responseDTO.getData();
            if (isIntlEnvironment()) {
                return this.getUserInfo(MapUtils.getMap(data, "userinfo"));
            }
            log.info("[passport][getByUid][result: {}]", data.toString());
            return this.getUserInfo(data);
        }
        return null;
    }

    private Boolean isBusinessFailed(HttpClientResponseDTO<Map<String, Object>> responseDTO) {
        return responseDTO == null || !responseDTO.suc() || MapUtils.isEmpty(responseDTO.getData());

    }

    private Boolean isIntlEnvironment() {
        return INTERNATIONAL_SITE_FLAG.equals(System.getProperty(SPRING_PROFILES_ACTIVE));
    }


    private UserInfo getUserInfo(Map<String, Object> data) {
        if (data == null) {
            return null;
        }
        UserInfo user = new UserInfo();
        user.setId(MapUtils.getLong(data, "uid"));
        user.setName(MapUtils.getString(data, "nickname"));
        user.setEmail(MapUtils.getString(data, "email"));
        user.setPhone(MapUtils.getString(data, "phone", MapUtils.getString(data, "mobile")));
        return user;
    }

    /**
     * passport签名验证 sign方式签名
     */
    private String sign(Map<String, String> params, String secretKey) {
        SortedMap<String, String> sortedParams = new TreeMap<>(params);
        StringBuilder sb = new StringBuilder();
        for (String key : sortedParams.keySet()) {
            String val = sortedParams.get(key);
            sb.append(key).append("=").append(StringUtils.defaultIfEmpty(val, "")).append("|");
        }
        return EncodeUtils.MD5(sb.append(secretKey).toString(), "UTF-8");
    }

    /**
     * 时间戳
     */
    private String passportParamT() {
        long curTime = System.currentTimeMillis();
        int random = RandomUtils.nextInt(900) + 100;
        return String.valueOf(curTime) + String.valueOf(random);
    }
}
