package com.iqiyi.vip.present.service.impl;

import com.google.common.collect.Lists;

import com.iqiyi.vip.present.config.GuavaCacheConfig;
import com.iqiyi.vip.present.mapper.PresentProductMapper;
import com.iqiyi.vip.present.model.PresentProduct;
import com.iqiyi.vip.present.service.PresentProductService;

import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 产品服务
 *
 * <AUTHOR>
 * @date 2019/12/16 15:41
 */
@CacheConfig(cacheManager = GuavaCacheConfig.GUAVA_CACHE_MANAGER)
@Service
public class PresentProductServiceImpl implements PresentProductService {

    @Resource
    private PresentProductMapper presentProductMapper;

    @Override
    public void insertProduct(PresentProduct product) {
        presentProductMapper.insertSelective(product);
    }

    @Override
    public int updateProduct(PresentProduct product) {
        return presentProductMapper.updateByPrimaryKey(product);
    }

    @Override
    public int saveOrUpdate(PresentProduct product) {
        if (StringUtils.isEmpty(product.getCode())) {
            return 0;
        }
        PresentProduct presentProduct = presentProductMapper.selectByCode(product.getCode());
        Date date = new Date();
        if (presentProduct != null) {
            product.setUpdateTime(date);
            return presentProductMapper.updateByCode(product);
        } else {
            product.setCreateTime(date);
            product.setUpdateTime(date);
            return presentProductMapper.insertSelective(product);
        }
    }

    @Override
    public PresentProduct getByCode(String code) {
        return presentProductMapper.selectByCode(code);
    }

    @Cacheable(value = "getById", keyGenerator = "customKeyGenerator")
    @Override
    public PresentProduct getById(Long id) {
        if (id == null) {
            return null;
        }
        return presentProductMapper.selectByPrimaryKey(id.intValue());
    }

    @Override
    public List<String> getCodesByVipType(String viptype) {
        List<PresentProduct> products = presentProductMapper.getByVipType(viptype);
        List<String> list = Lists.newArrayList();
        for (PresentProduct product : products) {
            list.add(product.getCode());
        }
        return list;
    }

    @Override
    public String getCodeById(Long id) {
        return presentProductMapper.getCodeById(id);
    }
}