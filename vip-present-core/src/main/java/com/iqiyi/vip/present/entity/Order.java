package com.iqiyi.vip.present.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

import com.iqiyi.vip.present.model.dto.AutoRenewType;
import com.iqiyi.vip.present.enums.OrderStatus;
import com.iqiyi.vip.present.model.dto.OrderType;


/**
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Order {
    private Long id;
    private String name;

    /**
     * 标识字段
     */
    private String orderCode;
    private String tradeCode;
    private String parentOrderCode;

    /**
     * 因之前order-dal中的ordersValue对应的orders，因此需要重新指定下名称
     */
    @JsonProperty("ordersValue")
    private String orders;

    private String tradeNo;
    private String centerCode;
    private String serviceOrderNo;

    /**
     * 用户字段
     */
    private Long userId;
    private String accountId;
    private String userIp;

    /**
     * 支付字段
     */
    private Integer payType;
    private String centerPayType;
    private Integer centerPayService;

    /**
     * 产品字段
     */
    private Long productId;
    private Integer amount = 1;
    private Integer autoRenew = AutoRenewType.NONE.getValue();
    private Integer productType;
    private Long contentId;

    /**
     * 状态字段
     */
    private Integer status = OrderStatus.UNPAID.getValue();
    private String notifyResult;

    /**
     * 时间字段
     */
    private Date createTime = new Date();
    private Date updateTime = new Date();
    private Date payTime;
    private Date tradeCreate; // TODO Deprecated
    private Date tradePayment; // TODO Deprecated

    /**
     * 业务方ID
     */
    private Long serviceId;

    /**
     * 同步通知支付结果url
     */
    private String returnUrl;
    /**
     * 异步通知支付结果url
     */
    private String notifyUrl;

    /**
     * 统计字段
     */
    private Long platform;
    private Long channel;
    private Long pushChannel;
    private Long gateway;
    private String cartCode;
    private String fv;

    /**
     * 表明产品类型 0:单点 1：会员 2：套餐 3: 电影票
     */
    private Integer type;

    /**
     * 价格字段
     */
    private Integer fee;
    private Integer realFee;
    private Integer productFee;
    private Integer originalPrice;
    private Integer couponFee = 0;
    private Integer couponSettlementFee = 0;
    private Integer settlementFee = 0;

    /**
     * 内容字段
     */
    private String contentUrl; // TODO Deprecated
    private String pictureUrl; // TODO Deprecated
    private String businessValues;

    private Integer renewalsFlag = 0;

    /**
     * 存放代扣方式 dut_type
     */
    private Integer renewType;

    private String skuId;

    private Integer skuAmount;

    /**
     * 权益字段
     */
    private Date startTime;
    private Date deadline;

    private String frVersion;
    private String sendMsgId;

    private Order[] goodsOrder;

    /**
     * 其它字段
     */
    private String partner;
    private String refer;
    private Integer chargeType;

    private OrderType orderType;

    private String currencyUnit;

    private String partnerParam;

}
