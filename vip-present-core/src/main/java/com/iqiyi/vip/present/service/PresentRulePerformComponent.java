package com.iqiyi.vip.present.service;

import com.iqiyi.vip.present.consts.EnumPerformStatusCode;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.data.PresentRulePerform;
import com.iqiyi.vip.present.model.PresentCondition;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.model.PresentPerform;
import com.iqiyi.vip.present.utils.PresentConfigCacheComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/4/10
 * @apiNote
 */
@Component
@Slf4j
public class PresentRulePerformComponent {

    @Resource
    private PresentRuleComponent presentRuleComponent;

    public PresentRulePerform checkRulePerform(OrderMessage message, PresentConfig presentConfig) {
        //条件匹配
        PresentCondition targetCondition = presentRuleComponent.getTargetCondition(message, presentConfig);
        if (targetCondition == null) {
            log.info("条件无效，什么也不做, orderCode: {}, presentConfigId:{}, presentConfigName:{}", message.getOrderCode(), presentConfig.getId(), presentConfig.getRemark());
            return null;
        }

        //找到执行规则
        PresentPerform presentPerform = PresentConfigCacheComponent.performMap.get(targetCondition.getPerformId());
        if (presentPerform == null || EnumPerformStatusCode.NONE.getCode().equals(presentPerform.getStatus())) {
            log.info("执行规则无效，什么也不做, orderCode: {}, presentConfigId:{}, presentConfigName:{}", message.getOrderCode(), presentConfig.getId(), presentConfig.getRemark());
            return null;
        }

        return new PresentRulePerform(targetCondition, presentPerform);
    }
}
