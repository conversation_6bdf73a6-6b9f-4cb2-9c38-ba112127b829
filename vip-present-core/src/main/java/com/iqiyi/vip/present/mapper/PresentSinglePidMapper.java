package com.iqiyi.vip.present.mapper;

import com.iqiyi.vip.present.model.PresentSinglePid;

public interface PresentSinglePidMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PresentSinglePid record);

    int insertSelective(PresentSinglePid record);

    PresentSinglePid selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PresentSinglePid record);

    int updateByPrimaryKeyWithBLOBs(PresentSinglePid record);

    int updateByPrimaryKey(PresentSinglePid record);

    PresentSinglePid selectByPid(String pid);
}