package com.iqiyi.vip.present.service.impl;

import com.iqiyi.vip.present.dao.PresentRecordDao;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.model.PresentRecord;
import com.iqiyi.vip.present.service.PresentRecordService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
public class PresentRecordServiceImpl implements PresentRecordService {
    @Resource
    PresentRecordDao presentRecordDao;
    
    @Override
    public boolean queryByGroup(List<PresentConfig> configListByGroup, Long uid) {
        for (PresentConfig presentConfig : configListByGroup) {
            PresentRecord record = new PresentRecord();
            record.setBuyUid(uid);
            record.setBuyType(presentConfig.getBvipType());
            record.setPresentType(presentConfig.getPvipType());
            List<PresentRecord> list = presentRecordDao.queryRecordByUidAndVipType(record);
            if (CollectionUtils.isNotEmpty(list)) {
                return true;
            }
        }
        return false;
    }

    @Override
    @Transactional(propagation = Propagation.SUPPORTS)
    public void insertRecord(PresentRecord presentRecord) {
        presentRecordDao.insertRecord(presentRecord);
    }

    @Override
    public List<PresentRecord> queryRecordByParams(PresentRecord record) {
        List<PresentRecord> list = presentRecordDao.queryRecordByUidAndVipType(record);
        return list;
    }
}
