package com.iqiyi.vip.present.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 订单持久化层实体类
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table("orders")
public class PersistOrder extends Order {
    @Id
    @Override
    public Long getId() {
        return super.getId();
    }

    @Transient
    @Override
    public Order[] getGoodsOrder() {
        return super.getGoodsOrder();
    }
}
