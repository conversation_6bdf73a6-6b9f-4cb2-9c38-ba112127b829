package com.iqiyi.vip.present.utils;

import com.iqiyi.vip.present.consts.PresentConstants;
import com.iqiyi.vip.present.model.PresentCondition;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.model.PresentPerform;
import com.iqiyi.vip.present.service.PresentConditionService;
import com.iqiyi.vip.present.service.PresentConfigService;
import com.iqiyi.vip.present.service.PresentPerformService;
import com.iqiyi.vip.present.service.PresentProductService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Created at: 2021-01-19
 *
 * 买赠规则本地缓存工具类
 *
 * <AUTHOR>
 */
@Slf4j
@EnableScheduling
@Component
public class PresentConfigCacheComponent {

    @Resource
    PresentConfigService presentConfigService;
    @Resource
    PresentConditionService presentConditionService;
    @Resource
    PresentPerformService presentPerformService;
    @Resource
    PresentProductService presentProductService;

    public static Map<String, List<PresentConfig>> configMap=null;

    public static Map<Long, PresentCondition> conditionMap = null;

    public static Map<Long, PresentPerform> performMap = null;

    public static List<String> diamondPids = null;

    @PostConstruct
    @Scheduled(cron = "0 0/10 * * * ?")
    public void getPresentConfig(){
        Map<String, List<PresentConfig>> map = presentConfigService.queryPresentConfig();
        if (map != null) {
            configMap = map;
        }
        Map<Long, PresentCondition> cmap = presentConditionService.queryConditions();
        if (cmap != null) {
            conditionMap = cmap;
        }
        Map<Long, PresentPerform> pmap = presentPerformService.queryPerforms();
        if (pmap != null) {
            performMap = pmap;
        }

        List<String> list = presentProductService.getCodesByVipType(PresentConstants.DIAMOND_VIP_TYPE_CODE);
        if (CollectionUtils.isNotEmpty(list)) {
            diamondPids = list;
        }
    }

}
