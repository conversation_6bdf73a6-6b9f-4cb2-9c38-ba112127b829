package com.iqiyi.vip.present.service.impl;

import com.alibaba.fastjson.JSON;
import com.iqiyi.vip.present.commonInner.HttpResultVo;
import com.iqiyi.vip.present.service.RestTemplateService;
import com.iqiyi.vip.present.utils.OrderRehearsal;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

import java.lang.reflect.Type;
import java.net.URI;
import java.util.Iterator;
import java.util.Map;

@Service
@Slf4j
public class RestTemplateServiceImpl implements RestTemplateService {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private RestTemplate lbRestTemplate;

    @Override
    public <T> T postForObject(String url, Map<String, Object> params, Class<T> clazz,Boolean lb) {
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            MultiValueMap<String, Object> headers = new LinkedMultiValueMap<>();
            headers.add("Content-Type", MediaType.APPLICATION_FORM_URLENCODED_VALUE);
            MultiValueMap multiValueMap = new LinkedMultiValueMap();
            multiValueMap.setAll(params);
            RequestEntity requestEntity = new RequestEntity(multiValueMap, headers, HttpMethod.POST, new URI(url));
            T body = choseRestTemplate(lb).postForObject(new URI(url), requestEntity, clazz);
        log.info("[HTTP-post][url:{}][cost:{}][params:{}][result:{}]", url, stopWatch.getTime(), params, JSON.toJSONString(body));
        return body;
    } catch (Exception e) {
        log.error("[HTTP-post][Exception][url:{}][cost:{}][params:{}]", url, stopWatch.getTime(), params, e);
    }
        return null;
    }

    public <T> T postForObject(String url, Map<String, Object> params, Class<T> clazz,Boolean lb,String isPressureTest) {
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            MultiValueMap<String, Object> headers = new LinkedMultiValueMap<>();
            headers.add("Content-Type", MediaType.APPLICATION_FORM_URLENCODED_VALUE);
            if(StringUtils.isNotBlank(isPressureTest)){
                headers.add(OrderRehearsal.PRESSURE_TEST_MODE, isPressureTest);
            }
            MultiValueMap multiValueMap = new LinkedMultiValueMap();
            multiValueMap.setAll(params);
            RequestEntity requestEntity = new RequestEntity(multiValueMap, headers, HttpMethod.POST, new URI(url));
            T body = choseRestTemplate(lb).postForObject(new URI(url), requestEntity, clazz);
            log.info("[HTTP-post][url:{}][cost:{}][params:{}][result:{}]", url, stopWatch.getTime(), params, JSON.toJSONString(body));
            return body;
        } catch (Exception e) {
            log.error("[HTTP-post][Exception][url:{}][cost:{}][params:{}]", url, stopWatch.getTime(), params, e);
        }
        return null;
    }

    @Override
    public <T> T post(String url, Map<String, String> params, Class<T> clazz,Boolean lb) {
        return postForResult(url, params, new ParameterizedTypeReference<HttpResultVo<T>>() {
            @Override
            public Type getType() {
                return clazz.getGenericSuperclass();
            }
        },lb);
    }

    @Override
    public <T> T postForEntity(String url, Map<String, String> params, ParameterizedTypeReference<T> type,Boolean lb) {
        try {
            MultiValueMap multiValueMap = new LinkedMultiValueMap();
            multiValueMap.setAll(params);
            RequestEntity requestEntity = new RequestEntity(multiValueMap, null, HttpMethod.POST, new URI(url));
            ResponseEntity<T> responseEntity = choseRestTemplate(lb).exchange(requestEntity, type);
            T body = responseEntity.getBody();
            log.info("[HTTP-post][url:{}][params:{}][result:{}]", url, params, JSON.toJSONString(body));
            return body;
        } catch (Exception e) {
            log.error("[HTTP-post][Exception][url:{}][params:{}]", url, params, e);
        }
        return null;
    }

    @Override
    public <T> T postForResult(String url, Map<String, String> params, ParameterizedTypeReference<HttpResultVo<T>> type,Boolean lb) {
        HttpResultVo<T> result = postForEntity(url, params, type, lb);
        return result == null ? null : result.getData();
    }

    @Override
    public <T> T getForResult(String url, Map<String, String> params, Class<T> clazz,Boolean lb) {
        StringBuffer stringBuffer = new StringBuffer(url);
        Iterator iterator = params.entrySet().iterator();
        if (iterator.hasNext()) {
            stringBuffer.append("?");
            Object element;
            while (iterator.hasNext()) {
                element = iterator.next();
                Map.Entry<String, Object> entry = (Map.Entry) element;
                //过滤value为null，value为null时进行拼接字符串会变成 "null"字符串
                if (entry.getValue() != null) {
                    stringBuffer.append(element).append("&");
                }
                url = stringBuffer.substring(0, stringBuffer.length() - 1);
            }
        }
        return choseRestTemplate(lb).getForObject(url, clazz);
    }

    @Override
    public <T> T postForObjectThrow(String url, Map<String, Object> params, Class<T> clazz,Boolean lb) throws Exception{
        StopWatch stopWatch = StopWatch.createStarted();
        MultiValueMap multiValueMap = new LinkedMultiValueMap();
        multiValueMap.setAll(params);
        RequestEntity requestEntity = new RequestEntity(multiValueMap, null, HttpMethod.POST, new URI(url));
        T body = choseRestTemplate(lb).postForObject(new URI(url), requestEntity, clazz);
        log.info("[HTTP-post][url:{}][cost:{}][params:{}][result:{}]", url, stopWatch.getTime(), params, JSON.toJSONString(body));
        return body;
    }

    @Override
    public RestTemplate choseRestTemplate(Boolean lb) {

        if(lb==null || !lb){
            return restTemplate;
        }
        return lbRestTemplate;
    }
}
