package com.iqiyi.vip.present.mapper;

import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;

import com.iqiyi.vip.present.model.PresentAsyncTask;

public interface PresentAsyncTaskMapper {

    int insert(PresentAsyncTask record);

    int updateByPrimaryKey(PresentAsyncTask record);

    int makeTaskProcessing(Long id);

    int makeTaskUnProcess(Long id);

    int restoreTaskForRetry(@Param("id") Long id, @Param("nextRunTime") Timestamp nextRunTime);

    int deleteByPrimaryKey(Long id);

    PresentAsyncTask selectByPrimaryKey(Long id);

    PresentAsyncTask selectByTaskId(String taskId);
}