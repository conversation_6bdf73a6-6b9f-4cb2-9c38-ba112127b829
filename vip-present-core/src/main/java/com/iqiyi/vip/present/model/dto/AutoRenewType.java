package com.iqiyi.vip.present.model.dto;


import java.util.Objects;

/**
 * <AUTHOR>
 */

public enum AutoRenewType {
    NONE(0), OPEN_AUTO_RENEW_ONE_MONTH(1), DEDUCT_ORDER(2), OPEN_AUTO_RENEW_OTHER_MONTH(3);

    private final Integer value;

    AutoRenewType(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }


    public static AutoRenewType getAutoRenewTypeByAutoRenewTypeInt(Integer autoRenew) {
        if (autoRenew == null) {
            return null;
        }
        for (AutoRenewType autoRenewType : AutoRenewType.values()) {
            if (Objects.equals(autoRenewType.value, autoRenew)) {
                return autoRenewType;
            }
        }
        return null;
    }
}
