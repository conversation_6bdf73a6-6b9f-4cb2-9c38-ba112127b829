package com.iqiyi.vip.present.out.passport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 变量名不可变更
 */
//passport的返回值
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserInfoVo {
    /**
     * 全局uid
     */
    private String suid;
    /**
     * 用户uid
     */
    private Long uid;
    private String nickname;
    private Integer pwdScore;
    private String phone;
    private String email;
    private Date jointime;
    private String area_code;
    private String user_name;
    private Integer accountType;
    private Integer activated;
    private String regip;
    private String icon;
    private Long pru;
}
