package com.iqiyi.vip.present.apiresponse;

import lombok.Data;

/**
 * httClient响应结果
 *
 * <AUTHOR> on 2018/7/29
 */
@Data
public class HttpClientResponseDTO<T> {

    private String message;
    private String code;
    private String msg;
    private T data;

    public HttpClientResponseDTO() {

    }

    public HttpClientResponseDTO(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static HttpClientResponseDTO create(HttpClientCodeEnum codeMsgEnum) {
        return new HttpClientResponseDTO(codeMsgEnum.getCode(), codeMsgEnum.getMsg());
    }

    public static HttpClientResponseDTO createSuc(String message) {
        return new HttpClientResponseDTO(HttpClientCodeEnum.SUC.getCode(), message);
    }

    public boolean suc() {
        return HttpClientCodeEnum.SUC.getCode().equals(this.getCode());
    }

    /**
     * org.apache.http.NoHttpResponseException 重试一次 (该异常均1ms就返回了可以重试)
     *
     * @return true:需要重试, false:不需要
     */
    public boolean needRetry() {
        return HttpClientCodeEnum.HTTP_ERROR_NO_HTTP_RESPONSE.getCode().equals(this.getCode());
    }

    public boolean exception() {
        return (HttpClientCodeEnum.HTTP_ERROR.getCode().equals(this.getCode())
            || HttpClientCodeEnum.HTTP_ERROR_CONNECT_TIME_OUT.getCode().equals(this.getCode())
            || HttpClientCodeEnum.HTTP_ERROR_SOCKET_TIME_OUT.getCode().equals(this.getCode())
            || HttpClientCodeEnum.HTTP_ERROR_UNKNOWN_HOST.getCode().equals(this.getCode())
            || HttpClientCodeEnum.HTTP_ERROR_CONNECTION_REFUSED.getCode().equals(this.getCode())
            || HttpClientCodeEnum.HTTP_ERROR_NO_HTTP_RESPONSE.getCode().equals(this.getCode())
        );
    }
}

