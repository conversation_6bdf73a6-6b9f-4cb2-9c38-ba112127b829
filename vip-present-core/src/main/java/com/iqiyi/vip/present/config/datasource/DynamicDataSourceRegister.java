package com.iqiyi.vip.present.config.datasource;

import com.google.common.collect.Maps;
import com.iqiyi.vip.present.constants.ShardingConstants;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.GenericBeanDefinition;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.env.Environment;
import org.springframework.core.type.AnnotationMetadata;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * 数据源配置
 */
@Configuration
@Slf4j
public class DynamicDataSourceRegister implements ImportBeanDefinitionRegistrar, EnvironmentAware {

    private Environment environment;

    private Map<Object, Object> dataSources = new HashMap<>();

//    @Bean(name = "dynamicDataSource")
//    protected DataSource DynamicDataSource() {
//        DynamicDataSource dynamicDataSource = new DynamicDataSource();
//        dynamicDataSource.setTargetDataSources(dataSources);
//        return dynamicDataSource;
//    }

    /**
     * 注入SpringBoot环境信息，加载多数据源配置
     */
    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
        log.info("动态数据源注册----->SpringBoot环境信息注入OK");
        log.info("---------------动态数据源注册start----------------");
        initOrderShardingDataSource(environment);
        log.info("---------------动态数据源注册end----------------");
    }


    /**
     * 注册数据源到spring容器中
     */
    @Override
    public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry) {
        log.info("DynamicDataSourceRegister.registerBeanDefinitions()");

        Map<Object, Object> targetDataSources = Maps.newHashMap();
        targetDataSources.putAll(dataSources);
        // 创建DynamicDataSource 对应的 BeanDefinition
        GenericBeanDefinition beanDefinition = new GenericBeanDefinition();
        beanDefinition.setBeanClass(DynamicDataSource.class);
        beanDefinition.setSynthetic(true);
        // 设置为primary bean，确保能够被@Qualifier正确识别
        beanDefinition.setPrimary(true);
        //添加属性：AbstractRoutingDataSource.defaultTargetDataSource
        MutablePropertyValues mpv = beanDefinition.getPropertyValues();
        mpv.addPropertyValue("targetDataSources", targetDataSources);
        //注册到spring中(默认的数据源名称就是"dataSource", 这个很重要)
        registry.registerBeanDefinition("orderDataSource", beanDefinition);
    }

    /**
     * 初始化数据源
     */
    private void initOrderShardingDataSource(Environment environment) {
        log.info("初始化订单分库分表数据源...");
        Properties commonProperties = new Properties();

        // 直接从environment获取属性值，如果为null则使用默认值
        String maxLifetime = environment.getProperty("spring.datasource.common.max-lifetime", "1800000");
        String minimumIdle = environment.getProperty("spring.datasource.common.minimum-idle", "5");
        String maximumPoolSize = environment.getProperty("spring.datasource.common.maximum-pool-size", "20");
        String connectionTimeout = environment.getProperty("spring.datasource.common.connection-timeout", "30000");
        String autoCommit = environment.getProperty("spring.datasource.common.auto-commit", "true");
        String idleTimeout = environment.getProperty("spring.datasource.common.idle-timeout", "600000");

        commonProperties.setProperty("max-lifetime", maxLifetime);
        commonProperties.setProperty("minimum-idle", minimumIdle);
        commonProperties.setProperty("maximum-pool-size", maximumPoolSize);
        commonProperties.setProperty("connection-timeout", connectionTimeout);
        commonProperties.setProperty("auto-commit", autoCommit);
        commonProperties.setProperty("idle-timeout", idleTimeout);
        String shardingDatabaseUrls = environment.getProperty("order.sharding.database.urls");
        String userName = environment.getProperty("order.sharding.database.username");
        String password = environment.getProperty("order.sharding.database.password");
        String[] datasourceUrls = StringUtils.split(shardingDatabaseUrls, ",");
        for (int i = 0; i < datasourceUrls.length; i++) {
            HikariConfig config = new HikariConfig();
            String datasourceUrl = datasourceUrls[i];
            String dsName = ShardingConstants.SHARDING_DATABASE_NAME_PREFIX + i;
            config.setJdbcUrl(datasourceUrl);
            config.setUsername(userName);
            config.setPassword(password);
            config.setPoolName(dsName);

            config.setMaxLifetime(Long.parseLong(commonProperties.getProperty("max-lifetime")));
            config.setMinimumIdle(Integer.parseInt(commonProperties.getProperty("minimum-idle")));
            config.setMaximumPoolSize(Integer.parseInt(commonProperties.getProperty("maximum-pool-size")));
            config.setConnectionTimeout(Long.parseLong(commonProperties.getProperty("connection-timeout")));
            config.setAutoCommit(Boolean.parseBoolean(commonProperties.getProperty("auto-commit")));
            config.setIdleTimeout(Long.parseLong(commonProperties.getProperty("idle-timeout")));

            HikariDataSource dataSource = new HikariDataSource(config);
            dataSources.put(dsName, dataSource);
        }
    }
}

