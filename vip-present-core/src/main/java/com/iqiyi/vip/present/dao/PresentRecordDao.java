package com.iqiyi.vip.present.dao;

import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.present.mapper.PresentRecordMapper;
import com.iqiyi.vip.present.model.PresentRecord;

@Repository
public  class PresentRecordDao extends BaseDao<PresentRecord> {

    @Resource
    PresentRecordMapper presentRecordMapper;
    
    public List<PresentRecord> queryRecordByUidAndVipType(PresentRecord record) {
        return presentRecordMapper.queryRecordByUidAndVipType(record);
    }

    public void insertRecord(PresentRecord record) {
        presentRecordMapper.insert(record);
    }
}
