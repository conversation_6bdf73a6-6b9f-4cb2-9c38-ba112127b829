package com.iqiyi.vip.present.data;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SingleProductMessage {
    private String msgId;
    private Long id;
    private String area;
    private String businessCharge;
    private String businessCode;
    private String code;
    private String cooperatorName;
    private String cooperatorUid;
    private String currencySymbol;
    private String currencyUnit;
    private Long deadline;
    private String description;
    private String idempotencyId;
    private String name;
    private String operator;
    private Long originalPrice;
    private Integer period;
    private Integer periodUnit;
    private Integer prePaid;
    private Long price;
    private Integer rebuy;
    private Integer serviceType;
    private Integer status;
    private Integer supportExp;
    private Integer type;
    private Integer version;
}
