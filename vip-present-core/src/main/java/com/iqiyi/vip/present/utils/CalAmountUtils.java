package com.iqiyi.vip.present.utils;

import com.iqiyi.vip.present.consts.CalAmountTypeEnum;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.model.SupplyPresentRecord;

/**
 * 计算amount User: zhangdaoguang Date: 2019/3/15 Time: 9:37
 */
public class CalAmountUtils {

    /**
     * 计算赠送天数
     */
    public static Integer calPresentAmount(OrderMessage message, PresentConfig presentConfig) {
        Integer calAmountType = getCalAmountType(presentConfig);
        Integer amount = null;
        if (CalAmountTypeEnum.MESSAGE_TIME_DAYS.getType().equals(calAmountType)) {
            amount = DateUtils.getDayInterval(message.getStartTime(), message.getEndTime());
        } else if (CalAmountTypeEnum.MESSAGE_AMOUNT.getType().equals(calAmountType)) {
            amount = message.getAmount();
        }

        // 兼容一下,出现过订单message里startTime大于endTime的情况
        if (amount == null || amount < 0) {
            amount = 0;
        }
        return amount;
    }

    /**
     * 计算退款天数
     * <br> http://wiki.qiyi.domain/pages/viewpage.action?pageId=52055069
     */
    public static Integer calRefundAmount(OrderMessage message, PresentOrder presentOrder, PresentConfig presentConfig) {
        // present_order表没加present_config_id前,都是按照 消息权益开始和结束时间间隔(天) 计算的数量
        Integer calAmountType = getCalAmountType(presentConfig);
        Integer messageAmount = null;
        Integer orderAmount = null;
        // 退款时长单位与购买时长一致
        if (CalAmountTypeEnum.MESSAGE_TIME_DAYS.getType().equals(calAmountType)) {
            messageAmount = Math.abs(DateUtils.getDayInterval(message.getStartTime(), message.getEndTime()));
            orderAmount = Math.abs(DateUtils.getDayInterval(
                presentOrder.getDeadlineStartTime(), presentOrder.getDeadlineEndTime()));
        } else if (CalAmountTypeEnum.MESSAGE_AMOUNT.getType().equals(calAmountType)) {
            messageAmount = Math.abs(message.getAmount());
            orderAmount = Math.abs(presentOrder.getProductAmount());
        }
        Integer amount = null;
        if (messageAmount != null) {
            amount = (messageAmount < orderAmount ? messageAmount : orderAmount);
        }
        // 兼容一下,退款时长为负数会出现加权益的情况
        if (amount == null || amount < 0) {
            amount = 0;
        }
        return amount;
    }


    //不影响买赠其它正常单，针对基础会员补增单的退单天数进行单独计算
    public static Integer calRefundBasicAmount(OrderMessage message) {
        Integer messageAmount = Math.abs(DateUtils.getDayInterval(message.getStartTime(), message.getEndTime()));
        // 兼容一下,退款时长为负数会出现加权益的情况
        if (messageAmount == null || messageAmount < 0) {
            messageAmount = 0;
        }
        return messageAmount;
    }

    /**
     * 根据毫秒计算要赠送的天数,向下取整
     */
    public static int calFloorAmountByDayMills(long totalTimeInMillis) {
        double totalTime = totalTimeInMillis * 1.0D;
        double floorAmount = Math.floor(totalTime / 1000 / 60 / 60 / 24);
        int amount = (int) floorAmount;
        if (amount <= 0) {
            amount = 0;
        }
        return amount;
    }

    /**
     * 根据毫秒计算要赠送的天数,向上取整
     */
    public static int calCeilAmountByDayMills(long totalTimeInMillis) {
        double totalTime = totalTimeInMillis * 1.0D;
        double ceilAmount = Math.ceil(totalTime / 1000 / 60 / 60 / 24);
        int amount = (int) ceilAmount;
            if (totalTime > 0 && amount <= 0) {
                amount = 1;
            }
            return amount;
        }


        private static Integer getCalAmountType(PresentConfig presentConfig) {
        Integer calAmountType = CalAmountTypeEnum.MESSAGE_TIME_DAYS.getType();
        if (presentConfig != null && presentConfig.getCalAmountType() != null) {
            calAmountType = presentConfig.getCalAmountType();
        }
        return calAmountType;
    }
}
