package com.iqiyi.vip.present.consts;

import lombok.Getter;

@Getter
public enum EnumResultCode {

    /**
     * 枚举定义
     */
    SUCCESS("A00000", "成功"),
    PARAM("Q00301", "参数错误"),
    NO_USER("Q00302", "未登录用户"),
    NO_PRESENT("Q00404", "无赠送权益可领取"),
    ERROR_CONFIG("Q00607", "无效的会员身份配置信息"),
    NOT_ALL_SUCCESS("Q00609", "没有全部领取成功，需要重试"),
    SYSTEM_ERROR("Q00332", "系统错误"),
    NOT_RECEIVE("Q00612", "没有领取过"),
    NOT_MATCH("Q00613", "不匹配处理条件"),
    OUTER_SERVICE_ERROR("Q00614", "请求外部服务异常"),
    NO_NEED_PRESENT("Q00615", "业务逻辑判断出无需赠送");



    private String code;

    private String msg;

    EnumResultCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
