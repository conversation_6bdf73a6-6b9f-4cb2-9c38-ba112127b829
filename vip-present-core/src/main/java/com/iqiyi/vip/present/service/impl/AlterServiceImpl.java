package com.iqiyi.vip.present.service.impl;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.vip.present.api.FeiShuApi;
import com.iqiyi.vip.present.entity.CheckRule;
import com.iqiyi.vip.present.model.dto.FeiShuNotifyParam;
import com.iqiyi.vip.present.model.dto.ProcessContext;
import com.iqiyi.vip.present.service.AlterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 告警通知
 *
 * @Author: <PERSON>
 * @Date: 2022/9/15
 */
@Slf4j
@Component
public class AlterServiceImpl implements AlterService {

    @Value("${spring.profiles.active:default}")
    private String activeProfiles;
    @Resource
    private FeiShuApi feiShuApi;
    @Resource
    private CloudConfig cloudConfig;

    @Override
    public boolean buildAndSendRuleAlter(List<String> orderCodes, ProcessContext processContext) {
        CheckRule checkRule = processContext.getCheckRule();
        if (checkRule == null) {
            log.error("Order list is empty or ruleId is null. rule:{}", checkRule);
            return false;
        }
        
        FeiShuNotifyParam feiShuNotifyParam = FeiShuNotifyParam.builder()
            .emails(getEmails())
            .templateId("AAqj2QPdEENHb")
            .topic(getTopic("买赠补偿告警-", processContext))
            .time(DateUtil.now())
            .count(String.valueOf(orderCodes.size()))
            .timeRange(processContext.getTimeRange())
            .orderDetails(extractFirstFiveOrderCodes(orderCodes))
            .build();
        Map<String, Object> notifyParam = getNotifyParam(feiShuNotifyParam);
        feiShuApi.notify(notifyParam);
        log.info("Send alter success. alterContext:{}", notifyParam);
        return true;
    }

    private String getTopic(String prefix, ProcessContext processContext) {
        CheckRule checkRule = processContext.getCheckRule();
        String topic = prefix + checkRule.getName();
        if ("test".equals(activeProfiles) || "dev".equals(activeProfiles)) {
            topic = topic + "-测试";
        }
        if("i18n".equals(activeProfiles)) {
            topic = topic + "-国际站";
        }
        Integer startIndex = processContext.getDbStartIndex();
        Integer endIndex = processContext.getDbEndIndex();
        if (startIndex != null && endIndex != null) {
            Integer dbStartIndex = startIndex + 1;
            Integer dbEndIndex = endIndex + 1;
            topic = topic + "(分库范围: " + dbStartIndex + "-" + dbEndIndex + ")";
        }
        return topic;
    }

    @Override
    public boolean buildAndSendRuleOK(List<String> orderCodes, ProcessContext processContext) {
        CheckRule checkRule = processContext.getCheckRule();
        if (CollectionUtils.isEmpty(orderCodes) || checkRule == null) {
            log.info("Order list is empty or ruleId is null. rule:{}", checkRule);
            return false;
        }
        Integer totalCount = processContext.getTotalCount();
        FeiShuNotifyParam feiShuNotifyParam = FeiShuNotifyParam.builder()
            .emails(getEmails())
            .templateId("AAqj2EjFY4afo")
            .topic(getTopic("买赠补偿完成-", processContext))
            .time(processContext.getTime())
            .count(totalCount != null ? totalCount.toString() : String.valueOf(orderCodes.size()))
            .okCount(String.valueOf(processContext.getOkCount()))
            .timeRange(processContext.getTimeRange())
            .orderDetails(extractFirstFiveOrderCodes(orderCodes))
            .cost(String.valueOf(processContext.getCost()))
            .build();
        feiShuApi.notify(getNotifyParam(feiShuNotifyParam));
        log.info("Send alter success. alterContext:{}", feiShuNotifyParam);
        return true;
    }

    private List<String> getEmails() {
        String emails = cloudConfig.getProperty("feiShu_receiver", "linpeihui");
        if (StringUtils.isBlank(emails)) {
            return Lists.newArrayList();
        }
        return Arrays.stream(emails.split(",")).map(String::trim).collect(Collectors.toList());
    }

    private Map<String, Object> getNotifyParam(FeiShuNotifyParam param) {
        // 构造外层Map
        Map<String, Object> params = new HashMap<>();
        params.put("emails", param.getEmails());
        params.put("templateId", param.getTemplateId());

        // 构造 `templateVariable`
        Map<String, String> templateVariableMap = new HashMap<>();
        templateVariableMap.put("topic", param.getTopic());
        templateVariableMap.put("time", param.getTime());
        templateVariableMap.put("count", param.getCount());
        if (StringUtils.isNotBlank(param.getOkCount())) {
            templateVariableMap.put("okCount", param.getOkCount());
        }
        templateVariableMap.put("timeRange", param.getTimeRange());
        templateVariableMap.put("orderDetails", param.getOrderDetails());
        templateVariableMap.put("cost", param.getCost() + "ms");

        params.put("templateVariable", templateVariableMap);
        return params;
    }

    private String extractFirstFiveOrderCodes(List<String> orderList) {
        return orderList.stream()
            .limit(5) // 限制提取前5个元素
            .collect(Collectors.joining("\n"));
    }
}
