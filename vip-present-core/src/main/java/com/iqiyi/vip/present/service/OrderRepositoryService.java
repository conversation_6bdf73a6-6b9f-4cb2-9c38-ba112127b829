package com.iqiyi.vip.present.service;

import com.iqiyi.vip.present.entity.CheckRule;
import com.iqiyi.vip.present.entity.Order;
import com.iqiyi.vip.present.entity.ShardingInfo;

import java.sql.Timestamp;
import java.util.List;

public interface OrderRepositoryService {
    Order findByOrderCode(String orderCode, String tableName);

    List<Order> findByOrderToCheck(Integer datasourceIndex, Timestamp startTime, Timestamp endTime, CheckRule checkRule);

    ShardingInfo calculateShardingInfo(String orderCode);
}
