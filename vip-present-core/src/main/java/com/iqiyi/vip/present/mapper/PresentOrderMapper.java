package com.iqiyi.vip.present.mapper;

import com.iqiyi.vip.present.model.PresentOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface PresentOrderMapper  extends BaseMapper<PresentOrder>{
    int deleteByPrimaryKey(Long id);

    int insert(PresentOrder record);

    int insertSelective(PresentOrder record);

    PresentOrder selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PresentOrder record);

    int updateByPrimaryKey(PresentOrder record);

    List<PresentOrder> queryOrderByParams(PresentOrder order);

    int updateOrderStatus(@Param("presentOrder") PresentOrder presentOrder, @Param("oldStatus") Integer oldStatus);

    int updateStatusFromOld(@Param("uid") Long uid, @Param("id") Long id, @Param("newStatus") Integer newStatus, @Param("oldStatus") Integer oldStatus);

    List<PresentOrder> queryByParamsAndDate(@Param("presentOrder") PresentOrder presentOrderRequest,
        @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("tableNo") String tableNo);

    List<PresentOrder> queryOrderByPageParams(Map<String, Object> params);

    Integer findOrderTotalCounts(Map<String, Object> params);

    /**
     * 奇异果送黄金，n天未领取订单列表用于精准触达发短信
     * @return
     */
    List<PresentOrder> queryKiwiPresentGoldNotReceiveOrder(@Param("diffDay") Integer diffDay, @Param("tableNo") String tableNo);

    List<PresentOrder> queryKiwiPresentGoldNotReceiveOrderByRange(@Param("startTime") String startTime, @Param("endTime") String endTime,
                                                                  @Param("tableNo") String tableNo, @Param("configId") Integer configId,
                                                                  @Param("limitNum") Integer limitNum);

    List<PresentOrder> queryManualPresentOrders(@Param("startTime") String startTime, @Param("endTime") String endTime,
        @Param("tableNo") String tableNo, @Param("configIdList") List<Integer> configIdList, @Param("limitNum") Integer limitNum);

    PresentOrder queryByPresentTradeCode(@Param("uid") Long uid,@Param("presentTradeCode") String presentTradeCode);
}