package com.iqiyi.vip.present.service.impl;

import com.google.common.collect.Lists;
import com.iqiyi.vip.present.dao.OrderDao;
import com.iqiyi.vip.present.entity.CheckRule;
import com.iqiyi.vip.present.entity.Order;
import com.iqiyi.vip.present.entity.PersistOrder;
import com.iqiyi.vip.present.entity.ShardingInfo;
import com.iqiyi.vip.present.service.OrderRepositoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class OrderRepositoryServiceImpl implements OrderRepositoryService {
    private static final String ORDER_CODE_SEPARATOR = ",";

    @Value("${order.sharding.tableSize:64}")
    private Integer dbTableSize;

    @Value("${order.sharding.database.urls}")
    private String databaseUrls;

    @Resource
    private OrderDao orderDao;

    @Override
    public Order findByOrderCode(String orderCode, String tableName) {
        if (StringUtils.isBlank(orderCode)) {
            return null;
        }

        if (StringUtils.isBlank(tableName)) {
            log.warn("表名为空，无法查询订单: {}", orderCode);
            return null;
        }

        // 注意：数据源切换应该在调用方进行，这里只负责根据表名查询
        // 调用方需要先调用 DynamicDataSourceContextHolder.setDataSourceKey(dbName)
        try {
            PersistOrder persistOrder = orderDao.findByOrderCode(tableName, orderCode);
            return persistOrder;
        } catch (Exception e) {
            // 检查是否是表不存在的错误
            if (isTableNotExistError(e)) {
                log.error("表不存在错误 - 订单号: {}, 表名: {}, 错误信息: {}",
                         orderCode, tableName, e.getMessage());
                return null;
            } else {
                log.error("查询订单时发生未知错误 - 订单号: {}, 表名: {}", orderCode, tableName, e);
                throw e;
            }
        }
    }

    /**
     * 根据订单号计算分库分表信息
     * 参考实际的分库分表逻辑，基于ModAndRangeShardingAlgorithm的实现
     */
    public ShardingInfo calculateShardingInfo(String orderCode) {
        try {
            if (orderCode.length() < 19) {
                log.warn("订单号长度不足，无法进行分库分表计算: {}", orderCode);
                return null;
            }

            String shardingKey = orderCode.substring(11, 19);
            long shardingValue = Long.parseLong(shardingKey);

            // 获取数据源数量
            String[] databaseUrlArray = databaseUrls.split(",");
            int dataSourceSize = databaseUrlArray.length;

            // 根据实际的分库分表逻辑计算
            // 总的分片数量 = dataSourceSize * dbTableSize
            int totalShards = dataSourceSize * dbTableSize;

            // 计算分表索引
            int tableIndex = (int) (shardingValue % totalShards);

            // 计算数据库
            int databaseIndex = tableIndex / dbTableSize;

            // 构建表名（4位补零）
            DecimalFormat format = new DecimalFormat("0000");
            String tableName = "orders_" + format.format(tableIndex);

            log.debug("订单号: {}, 分库分表键: {}, 数据库索引: {}, 表索引: {}, 表名: {}",
                     orderCode, shardingKey, databaseIndex, tableIndex, tableName);

            return new ShardingInfo(databaseIndex, tableName);
        } catch (Exception e) {
            log.error("计算订单号 {} 的分库分表信息时发生异常", orderCode, e);
            return null;
        }
    }

    /**
     * 检查是否是表不存在的错误
     */
    private boolean isTableNotExistError(Exception e) {
        String errorMessage = e.getMessage();
        if (errorMessage == null) {
            return false;
        }
        return errorMessage.contains("doesn't exist") ||
               errorMessage.contains("Table") && errorMessage.contains("doesn't exist");
    }

    /**
     * 使用备用策略查询订单
     * 当目标表不存在时，尝试其他可能的表
     */
    private PersistOrder findByOrderCodeWithFallback(String orderCode, ShardingInfo originalShardingInfo) {
        log.warn("开始使用备用策略查询订单: {}, 原始表名: {}", orderCode, originalShardingInfo.getTableName());

        // 策略1: 尝试使用表索引0（通常是最基础的表）
        try {
            String fallbackTableName = "orders_0000";
            log.info("尝试备用表: {}", fallbackTableName);
            PersistOrder order = orderDao.findByOrderCode(fallbackTableName, orderCode);
            if (order != null) {
                log.info("在备用表 {} 中找到订单: {}", fallbackTableName, orderCode);
                return order;
            }
        } catch (Exception e) {
            log.debug("备用表 orders_0000 查询失败: {}", e.getMessage());
        }

        // 策略2: 尝试使用简化的分表逻辑（取模较小的数）
        try {
            String shardingKey = orderCode.substring(11, 19);
            long shardingValue = Long.parseLong(shardingKey);
            int simpleTableIndex = (int) (shardingValue % 4); // 使用较小的模数
            String simpleTableName = String.format("orders_%04d", simpleTableIndex);

            log.info("尝试简化分表策略，表名: {}", simpleTableName);
            PersistOrder order = orderDao.findByOrderCode(simpleTableName, orderCode);
            if (order != null) {
                log.info("在简化分表 {} 中找到订单: {}", simpleTableName, orderCode);
                return order;
            }
        } catch (Exception e) {
            log.debug("简化分表策略查询失败: {}", e.getMessage());
        }

        log.error("所有备用策略都失败，无法找到订单: {}", orderCode);
        return null;
    }



    @Override
    public List<Order> findByOrderToCheck(Integer datasourceIndex, Timestamp startTime, Timestamp endTime, CheckRule checkRule) {
        int startTableIndex = datasourceIndex * dbTableSize;
        int endTableIndex = startTableIndex + dbTableSize - 1;
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        List<Order> result = Lists.newArrayList();
        String startTimeStr = timeFormatter.format(startTime.toLocalDateTime());
        String endTimeStr = timeFormatter.format(endTime.toLocalDateTime());
        DecimalFormat format = new DecimalFormat("0000");
        for (int i = startTableIndex; i <= endTableIndex; i++) {
            long start = System.currentTimeMillis();
            String tableName = "orders_" + format.format(i);
            PersistOrder[] ordersToCheck = orderDao.findOrderToCheck(tableName, startTime, endTime, checkRule.getCondition());
            if (ArrayUtils.isNotEmpty(ordersToCheck)) {
                result.addAll(Arrays.asList(ordersToCheck));
            }
            log.info("规则名称:{}, 表名:{}, 耗时:{} ms,记录条数:{},时间范围:[{}]-[{}]", checkRule.getName(), tableName, System.currentTimeMillis() - start, ordersToCheck.length, startTimeStr, endTimeStr);
        }
        return result;
    }

}
