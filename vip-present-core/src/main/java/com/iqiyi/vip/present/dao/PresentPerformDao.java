package com.iqiyi.vip.present.dao;

import com.iqiyi.vip.present.mapper.PresentPerformMapper;
import com.iqiyi.vip.present.model.PresentPerform;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class PresentPerformDao extends BaseDao<PresentPerform> {

    @Resource
    PresentPerformMapper presentPerformMapper;

    public PresentPerform queryPresentPerformById(Long performId) {
        return presentPerformMapper.selectByPrimaryKey(performId);
    }

    public List<PresentPerform> queryPerforms() {
        return presentPerformMapper.queryPerforms();
    }
}
