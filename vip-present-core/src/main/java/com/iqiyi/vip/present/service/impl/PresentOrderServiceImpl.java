package com.iqiyi.vip.present.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.iqiyi.lego.rocketmq.core.StringRocketMQTemplate;
import com.iqiyi.vip.present.apiresponse.Page;
import com.iqiyi.vip.present.apiresponse.PresentOrderVO;
import com.iqiyi.vip.present.consts.EnumOrderStatusCode;
import com.iqiyi.vip.present.consts.EnumOrderTypeCode;
import com.iqiyi.vip.present.consts.VipTypeEnum;
import com.iqiyi.vip.present.dao.PresentOrderDao;
import com.iqiyi.vip.present.data.VipRightsGrantMessage;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.service.PresentConfigService;
import com.iqiyi.vip.present.service.PresentOrderService;
import com.iqiyi.vip.present.service.RocketMqSendService;
import com.iqiyi.vip.present.task.PresentVipAsyncTask;
import com.iqiyi.vip.present.task.RefundVipAsyncTask;
import com.iqiyi.vip.present.task.SendVipTask;
import com.iqiyi.vip.present.utils.CloudConfigUtil;
import com.iqiyi.vip.present.utils.DateUtils;
import com.iqiyi.vip.repository.ClusterAsyncTaskManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
//@Transactional(rollbackFor = RuntimeException.class, propagation = Propagation.SUPPORTS)
public class PresentOrderServiceImpl implements PresentOrderService {

    public static final int NO_TASK_STATUS = 9;
    @Resource
    PresentOrderDao presentOrderDao;

    @Autowired
    private ClusterAsyncTaskManager clusterAsyncTaskManager;

    @Resource
    PresentConfigService presentConfigService;
    @Resource
    private RocketMqSendService rocketMqSendService;


    @Override
//    @Transactional(propagation = Propagation.SUPPORTS)
    public List<PresentOrder> queryOrderByParams(PresentOrder presentOrderRequest) {
        return presentOrderDao.queryOrderByParams(presentOrderRequest);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertVipOrderPresent(PresentOrder presentOrder) {
        try {
            return presentOrderDao.insert(presentOrder);
        } catch (DuplicateKeyException e) {
            log.error("PresentOrderServiceImpl insertVipOrderPresent presentOrder:{}", JSONObject.toJSONString(presentOrder), e);
        }
        return 0;
    }

    @Override
    @Transactional(propagation = Propagation.SUPPORTS)
    public int updateOrderStatus(PresentOrder presentOrder, Integer oldStatus) {
        presentOrder.setUpdateTime(DateUtils.getCurrentTime());
        return presentOrderDao.updateOrderStatus(presentOrder, oldStatus);
    }

    @Override
    public void saveOrderPresentAndTask(List<PresentVipAsyncTask.TaskData> taskDatas) {
        for (PresentVipAsyncTask.TaskData taskData : taskDatas) {
            PresentOrder presentOrder = taskData.getPresentOrder();
            if (presentOrder == null) {
                log.info("presentOrder is null, taskData:{}", taskData);
                continue;
            }

            String originalGiftBatchNo = taskData.getRequest().getOriginalGiftBatchNo();
            if (noNeedCreatePresentSportVipTask(presentOrder, originalGiftBatchNo)) {
                presentOrder.setStatus(NO_TASK_STATUS);
                insertVipOrderPresent(presentOrder);
                log.info("save presentOrder, not present Sport Vip, presentOrder:{}", presentOrder);
                continue;
            }

            if (noNeedCreatePresentQiyuVipTask(presentOrder, originalGiftBatchNo)) {
                presentOrder.setStatus(NO_TASK_STATUS);
                insertVipOrderPresent(presentOrder);
                log.info("save presentOrder, not present qiyu Vip, presentOrder:{}", presentOrder);
                continue;
            }

            int row = insertVipOrderPresent(presentOrder);
            if (row == 0) {
                log.info("不进行赠送，此单在数据库存在，已经赠送过 [presentOrder:{}][row:{}]",
                    JSONObject.toJSONString(presentOrder), String.valueOf(row));
                continue;
            }
            //使用地较多 不影响老逻辑 不修改updatesql
            if(null==presentOrder.getId()||0==presentOrder.getId()){
                PresentOrder presentOrderDto = presentOrderDao.queryByPresentTradeCode(presentOrder.getUid(),presentOrder.getPresentTradeCode());
                if(null!=presentOrderDto){
                    log.info("queryByPresentTradeCode presentOrderDto result:{}",presentOrderDto);
                    presentOrder.setId(presentOrderDto.getId());
                }
            }

            //开始送
            if (EnumOrderStatusCode.NO_PRESENT.getCode().equals(presentOrder.getStatus())) {
                rocketMqSendService.sendVipRightsGrantMessage(new VipRightsGrantMessage(taskData.getRequest(), presentOrder));
                //clusterAsyncTaskManager.insertTask(new PresentVipAsyncTask(taskData.getRequest(), presentOrder));
            }
        }
    }

    private boolean noNeedCreatePresentSportVipTask(PresentOrder presentOrder, String originalGiftBatchNo) {
        return isSpecificActOrder(originalGiftBatchNo)
            && presentOrder != null
            && String.valueOf(VipTypeEnum.sport.getVipType()).equals(presentOrder.getPresentType())
            && CloudConfigUtil.noNeedCreatePresentSportVipTask();
    }

    private boolean noNeedCreatePresentQiyuVipTask(PresentOrder presentOrder, String originalGiftBatchNo) {
        return isSpecificActOrder(originalGiftBatchNo)
            && presentOrder != null
            && String.valueOf(VipTypeEnum.qiyu.getVipType()).equals(presentOrder.getPresentType())
            && CloudConfigUtil.noNeedCretaePresentQiyuVipTask();
    }


    private boolean isSpecificActOrder(String originalGiftBatchNo) {
        if (StringUtils.isBlank(originalGiftBatchNo)) {
            return false;
        }
        return Splitter.on(",").splitToList(CloudConfigUtil.getNationalDayDiamondActGiftBatchNo()).contains(originalGiftBatchNo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderPresenRefundtAndTask(List<PresentOrder> orders, List<Map<String, Object>> taskParams) {
        for (PresentOrder presentOrder : orders) {
            updateOrderStatus(presentOrder, EnumOrderStatusCode.NOT_RECEIVE.getCode());
        }
        for (Map<String, Object> param : taskParams) {
            clusterAsyncTaskManager.insertTask(new RefundVipAsyncTask(param));

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderPresenRefundtAndTask(PresentOrder order, Map<String, String> taskParam) {
        int status = order.getStatus();
        order.setStatus(EnumOrderStatusCode.REFUND_SUCCESS.getCode());
        order.setOrderType(EnumOrderTypeCode.NOT.getCode());
        updateOrderStatus(order, status);
        clusterAsyncTaskManager.insertTask(new SendVipTask(taskParam));

    }

    @Override
    public List<PresentOrder> queryByParamsAndDate(PresentOrder presentOrderRequest, String startTime, String endTime) {
        List<PresentOrder> orderList = Lists.newCopyOnWriteArrayList();
        for (int i = 0; i < 100; i++) {
            String tableNo = String.format("%02d", i);
            List<PresentOrder> tmpList = presentOrderDao.queryByParamsAndDate(
                presentOrderRequest, startTime, endTime, tableNo);
            orderList.addAll(tmpList);
        }
        return orderList;
    }
    @Override
    public Page<PresentOrderVO> findCardBatchPage(Map<String, Object> params, String presentCode) {
        Page<PresentOrderVO> page = new Page<>();
        if (presentCode != null) {
            params.put("presentConfigId", 50);
        }
        List<PresentOrder> presentOrderList = presentOrderDao.queryOrderByPageParams(params);
        List<PresentOrderVO> responseVOs = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(presentOrderList)) {
            Map<Long, PresentConfig> configMap = presentConfigService.getIdPresentConfigMap();
            for (PresentOrder presentOrder : presentOrderList) {
                PresentOrderVO presentOrderVO = new PresentOrderVO();
                BeanUtils.copyProperties(presentOrder, presentOrderVO);
                PresentConfig presentConfig = configMap.get(presentOrder.getPresentConfigId());
                presentOrderVO.setBuyCode(presentConfig.getBuyCode());
                presentOrderVO.setPresentCode(presentConfig.getPresentCode());
                responseVOs.add(presentOrderVO);
            }
        }
        page.setResult(responseVOs);
        page.setTotalCount(presentOrderDao.findOrderTotalCounts(params));
        return page;
    }

    @Override
    public List<PresentOrder> queryOrderByPageParams(Map<String, Object> params) {
        return presentOrderDao.queryOrderByPageParams(params);
    }
    @Override
    public Integer findOrderTotalCounts(Map<String, Object> params) {
        return presentOrderDao.findOrderTotalCounts(params);
    }

    @Override
    public List<PresentOrder> queryKiwiPresentGoldNotReceiveOrder() {
        List<PresentOrder> orderList = Lists.newCopyOnWriteArrayList();
        for (int i = 0; i < 100; i++) {
            String tableNo = String.format("%02d", i);
            List<PresentOrder> twoDayNotReceiveOrders = presentOrderDao.queryKiwiPresentGoldNotReceiveOrder(2, tableNo);
            List<PresentOrder> sevenDayNotReceiveOrders = presentOrderDao.queryKiwiPresentGoldNotReceiveOrder(7, tableNo);
            List<PresentOrder> monthNotReceiveOrders = presentOrderDao.queryKiwiPresentGoldNotReceiveOrder(32, tableNo);

            orderList.addAll(twoDayNotReceiveOrders);
            orderList.addAll(sevenDayNotReceiveOrders);
            orderList.addAll(monthNotReceiveOrders);
        }
        return orderList;
    }

    @Override
    public PresentOrder queryByPresentTradeCode(Long uid,String presentTradeCode){
        return presentOrderDao.queryByPresentTradeCode(uid,presentTradeCode);
    }
}
