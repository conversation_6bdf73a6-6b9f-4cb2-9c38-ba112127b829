package com.iqiyi.vip.present.out;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version v 1.0 2018/8/21 10:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PresentVipResponse {

    private String message;
    private String code;
    private ResponseData data;

    public boolean isSuccess() {
        return Objects.equals(this.code, "A00000") || Objects.equals(this.code, "A00001")
            || Objects.equals(this.code, "Q00348");
    }

    public boolean isUserNotFound() {
        return Objects.equals(this.code, "USER_NOT_FOUND");
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ResponseData {

        private String orderCode;
        private Long uid;
    }
}
