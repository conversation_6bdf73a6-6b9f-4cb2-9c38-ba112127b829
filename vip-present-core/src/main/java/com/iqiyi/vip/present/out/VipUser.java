package com.iqiyi.vip.present.out;

import lombok.Data;

import java.util.Map;

/**
 * 调用会员信息服务接口VO
 * wiki: http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/7/11.
 */
//会员信息服务的返回值
@Data
public class VipUser {

    /**
     * 用户的passportId
     */
    private Long uid;
    /**
     * 会员类型，可参考：vipTypes定义
     * wiki: http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********
     */
    private String vipType;
    /**
     * 等级，无等级显示null
     */
    private String level;
    /**
     * “0”表示不自动续费，“1”表示自动续费
     */
    private String autoRenew;
    /**
     * 付费标识
     * 0:从来没有付费, 1:至少付费一次。
     */
    private Integer paidSign;
    /**
     * 支付类型，默认为null，0：预付费用户，1:手机包月用户 ，2：运营商包月
     */
    private String payType;
    /**
     * 会员状态，0是临时封停 2是永久封停 1有效 3过期
     */
    private String status;
    /**
     * 0 表示已过期
     * 1 表示未过期
     */
    private String type;
    /**
     * 剩余会员有效期，距到期日期天数
     */
    private String surplus;
    /**
     * 年费会员过期标识
     * 0：未过期，是有效年费会员
     * 1：已过期，不是有效年费会员
     */
    private String yearExpire;
    /**
     * t     long    会员过期的timestamp
     * date	string	过期时间，格式：xxxx年xx月xx日
     */
    private Map<String, Object> deadline;
}
