package com.iqiyi.vip.present.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class PresentAsyncTask implements Serializable {

    private static final long serialVersionUID = 5491278688966706128L;

    /**
     * 不在队列
     */
    public final static int TASK_IN_QUEUE_NO = 0;
    /**
     * 在队列中
     */
    public final static int TASK_IN_QUEUE_YES = 1;

    private Long id;

    private String className;

    private String data;
    /**
     * task 唯一标识
     */
    private String taskId;
    /**
     * 默认不在队列
     */
    private Integer inQueue;

    private Timestamp timerRunAt;
    /**
     * 已执行次数
     */
    private Integer execCount;

    private Integer priority;

    private Timestamp createTime;

    private Timestamp updateTime;

}