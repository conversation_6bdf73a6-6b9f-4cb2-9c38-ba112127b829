package com.iqiyi.vip.present.model.dto;

import com.iqiyi.vip.present.entity.CheckRecord;
import com.iqiyi.vip.present.entity.CheckRule;
import com.iqiyi.vip.present.entity.CompensationStrategy;
import com.iqiyi.vip.present.entity.Order;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 补偿订单处理上下文
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProcessContext {

    /**
     * 数据库分库开始索引
     */
    private Integer dbStartIndex;

    /**
     * 数据库分库结束索引
     */
    private Integer dbEndIndex;

    /**
     * 订单补偿规则
     */
    private CheckRule checkRule;

    /**
     * 补偿策略
     */
    private CompensationStrategy compensationStrategy;

    /**
     * 待补偿订单
     */
    private List<Order> orderList;

    private String orderCodes;


    private String timeRange;

    private String time;

    private Integer okCount;

    private Integer totalCount;

    private Integer cost;

    /**
     * 补偿时机：0：手动 1：线上自动
     */
    private Integer compensateTimeType;

    private List<CheckRecord> checkRecords;

}
