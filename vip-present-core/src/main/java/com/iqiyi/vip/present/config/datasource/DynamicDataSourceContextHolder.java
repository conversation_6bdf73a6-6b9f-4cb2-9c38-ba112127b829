package com.iqiyi.vip.present.config.datasource;

/**
 * <AUTHOR> kong<PERSON><PERSON><PERSON>
 * Date: 2017/4/2
 * Time: 上午9:02
 * Mail: <EMAIL>
 * Description: 动态数据源上下文
 */
public class DynamicDataSourceContextHolder {

    private static final ThreadLocal<String> contextHolder = new ThreadLocal<>();

    /**
     * 使用setDataSourceType设置当前的
     */
    public static void setDataSourceKey(String dataSourceKey) {
        contextHolder.set(dataSourceKey);
    }

    static String getDataSourceKey() {
        return contextHolder.get();
    }

    public static void removeDataSourceKey() {
        contextHolder.remove();
    }
}