package com.iqiyi.vip.present.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.iqiyi.vip.present.entity.CheckRecord;
import com.iqiyi.vip.present.entity.Order;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CompensateDTO {

    private Order order;

    private CheckRecord checkRecord;

    private String payCenterPartnerId;

}
