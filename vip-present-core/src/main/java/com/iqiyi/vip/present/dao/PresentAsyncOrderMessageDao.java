package com.iqiyi.vip.present.dao;

import com.iqiyi.vip.present.mapper.PresentAsyncOrderMessageMapper;
import com.iqiyi.vip.present.model.PresentAsyncOrderMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/25
 * @apiNote
 */
@Repository
@Slf4j
public class PresentAsyncOrderMessageDao {

    @Resource
    private PresentAsyncOrderMessageMapper presentAsyncOrderMessageMapper;

    public int insert(PresentAsyncOrderMessage orderMessage) {
        return presentAsyncOrderMessageMapper.insertSelective(orderMessage);
    }

    public int delete(String orderCode) {
        return presentAsyncOrderMessageMapper.delete(orderCode);
    }

    public List<PresentAsyncOrderMessage> getByUidAndProductSubType(Long uid, Integer productSubType) {
        return presentAsyncOrderMessageMapper.queryMessages(uid, productSubType);
    }
}
