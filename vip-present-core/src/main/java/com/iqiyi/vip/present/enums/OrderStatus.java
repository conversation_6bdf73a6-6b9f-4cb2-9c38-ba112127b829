package com.iqiyi.vip.present.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 */

public enum OrderStatus {
    UNPAID(7),

    /**
     * 已支付
     */
    PAID(18),
    /**
     * 已支付待履约
     */
    PAID_WAIT_FULFILL(1),

    CANCEL(3),
    NEGATIVE(5),
    NEGATIVE_PAID(6),
    CANCEL_WITH_COUPONUNFREEZE(8),
    APPLE_CANCEL_SUB(9),
    /**
     * 预支付待履约
     */
    PRE_PAID_NO_RIGHTS(10),
    PRE_NEGATIVE_ING(11),
    PRE_NEGATIVE_SUCCESS(12);

    private final Integer value;

    OrderStatus(Integer value) {
        this.value = value;
    }


    public Integer getValue() {
        return value;
    }

    public static OrderStatus getOrderstatus(Integer status) {
        if (status == null) {
            return null;
        }
        for (OrderStatus orderStatus : OrderStatus.values()) {
            if (Objects.equals(orderStatus.value, status)) {
                return orderStatus;
            }
        }
        return null;
    }
}
