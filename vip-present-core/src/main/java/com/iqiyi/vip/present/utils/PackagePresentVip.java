package com.iqiyi.vip.present.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.iqiyi.vip.present.apirequest.PresentRequest;
import com.iqiyi.vip.present.consts.PresentConstants;
import com.iqiyi.vip.present.consts.StringConstants;
import com.iqiyi.vip.present.data.MultilingualData;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.data.PresentOrderData;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.out.PresentVipRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * 封装类 User: zhangdaoguang Date: 2019/3/20 Time: 14:18
 */
@Slf4j
public class PackagePresentVip {

    /**
     * 赠送vip请求封装
     */
    public static PresentVipRequest packagePresentVipRequest(OrderMessage message, PresentConfig presentConfig) {
        PresentVipRequest request = new PresentVipRequest();
        request.setUid(message.getUid());
        request.setPid(presentConfig.getPresentCode());
        Integer baseAmount = CalAmountUtils.calPresentAmount(message, presentConfig);
        request.setAmount(baseAmount);
        //设置唯一actCode避免重复赠送问题
        request.setActCode(PresentConstants.ACTCODE);
        request.setTradeCode(presentVipTradeCode(message.getOrderCode(), presentConfig.getPresentCode()));
        request.setPayType(presentConfig.getPayType());
        request.setOriginalOrderCode(message.getOrderCode());
        request.setFv(!Strings.isNullOrEmpty(message.getFv()) ? message.getFv() : StringConstants.DEAULT_FV);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(message.getPlatformCode())) {
            request.setPlatform(message.getPlatformCode());
        }
        if (PresentConstants.TYPE.equals(message.getType())) {
            request.setIsTest(Boolean.TRUE);
        }

        // 购买的产品code、数量、类型等信息
        String buyPid = "";
        JSONObject jsonObject = JSON.parseObject(message.getRefer());
        if(null!=jsonObject){
            buyPid = null==jsonObject.get("buyPid")?"":(String) jsonObject.get("buyPid");
        }
        if(StringUtils.isNotBlank(buyPid)){//增单
            request.setBuyPid((String) jsonObject.get("buyPid"));
            request.setTradeCode(presentNewVipTradeCode(message, presentConfig.getPresentCode()));
            request.setOriginalOrderCode(presentOriginalOrderCode(message));
        }else{
            request.setBuyPid(message.getPid());
        }
        request.setBuyPidAmt(message.getAmount());
        // 0-普通，1-连包
        request.setBuyPidType(message.isAutoRenewProduct() ? 1 : 0);
        request.setChargeType(message.getChargeType());
        request.setOriginalGiftBatchNo(message.getGiftBatchNo());
        return request;
    }


    /**
     * 补赠权益请求封装
     */
    public static PresentVipRequest packagePresentVipRequest(OrderMessage message, PresentOrder presentOrder, PresentConfig presentConfig, Integer receiveType) {
        PresentVipRequest request = new PresentVipRequest();
        request.setUid(presentOrder.getUid());
        request.setPid(presentConfig.getPresentCode());
        //计算剩余权益
        Integer baseAmount = DateUtils.getRestDay(presentOrder, receiveType);
        request.setAmount(baseAmount);
        //设置唯一actCode避免重复赠送问题
        request.setActCode(PresentConstants.ACTCODE);
        request.setTradeCode(presentOrder.getPresentTradeCode());
        request.setPayType(presentConfig.getPayType());
        request.setOriginalOrderCode(presentOrder.getOrderCode());
        request.setFv(presentOrder.getFv());
        if (StringUtils.isNotEmpty(message.getPlatformCode())) {
            request.setPlatform(message.getPlatformCode());
        }
        if (PresentConstants.TYPE.equals(message.getType())) {
            request.setIsTest(Boolean.TRUE);
        }

        // 购买的产品code、数量、类型等信息
        String buyPid = "";
        JSONObject jsonObject = JSON.parseObject(message.getRefer());
        if(null!=jsonObject){
            buyPid = null==jsonObject.get("buyPid")?"":(String) jsonObject.get("buyPid");
        }
        if(StringUtils.isNotBlank(buyPid)){//增单
            request.setBuyPid(buyPid);
        }else{
            request.setBuyPid(message.getPid());
        }
        //request.setBuyPid(message.getPid());
        request.setBuyPidAmt(message.getAmount());
        // 0-普通，1-连包
        request.setBuyPidType(message.isAutoRenewProduct() ? 1 : 0);
        request.setChargeType(message.getChargeType());
        return request;
    }

    /**
     * 赠送多语言单点请求封装
     */
    public static PresentVipRequest packageVodPresentVipRequest(OrderMessage message, PresentConfig presentConfig,
                                                                MultilingualData data) {
        PresentVipRequest request = new PresentVipRequest();
        request.setUid(message.getUid());
        /* 多语言aid pid 通过权益接口获取 */
        request.setPid(data.getVodProductCode());
        request.setAid(String.valueOf(data.getNestedQpid()));
        /* 单点权益数量默认为1 */
        request.setAmount(1);
        //设置唯一actCode避免重复赠送问题
        request.setActCode(PresentConstants.ACTCODE);
        request.setTradeCode(presentVipTradeCode(message.getOrderCode(), String.valueOf(data.getNestedQpid())));
        request.setPayType(presentConfig.getPayType());
        request.setOriginalOrderCode(message.getOrderCode());
        request.setFv(!Strings.isNullOrEmpty(message.getFv()) ? message.getFv() : StringConstants.DEAULT_FV);
        if (StringUtils.isNotEmpty(message.getPlatformCode())) {
            request.setPlatform(message.getPlatformCode());
        }
        if (PresentConstants.TYPE.equals(message.getType())) {
            request.setIsTest(Boolean.TRUE);
        }

        // 购买的产品code、数量、类型等信息
        request.setBuyPid(message.getPid());
        request.setBuyPidAmt(message.getAmount());
        // 0-普通，1-连包
        request.setBuyPidType(message.isAutoRenewProduct() ? 1 : 0);
        request.setChargeType(message.getChargeType());

        return request;
    }


    /**
     * 赠送vip请求封装
     */
    public static PresentVipRequest packagePresentVipRequest(PresentOrderData orderData,
        PresentRequest presentRequest ,Integer receiveType) {
        PresentVipRequest request = new PresentVipRequest();
        request.setUid(presentRequest.getUid());
        request.setPid(orderData.getPresentCode());
        Integer baseAmount = DateUtils.getDay(orderData, receiveType);
        request.setAmount(baseAmount);
        //设置唯一actCode避免重复赠送问题
        request.setActCode(PresentConstants.ACTCODE);
        request.setTradeCode(presentVipTradeCode(orderData.getOrderCode(), orderData.getPresentCode()));
        request.setPayType(orderData.getPayType());
        request.setOriginalOrderCode(orderData.getOrderCode());
        request.setFv(StringUtils.isNotBlank(orderData.getFv()) ? orderData.getFv() : StringConstants.DEAULT_FV);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(presentRequest.getPlatform())) {
            request.setPlatform(presentRequest.getPlatform());
        }
        return request;
    }

    /**
     * 赠送vip请求封装
     */
    public static PresentVipRequest packagePresentVipJobRequest(PresentOrder presentOrder, PresentConfig presentConfig, Integer receiveType) {
        PresentVipRequest request = new PresentVipRequest();
        request.setUid(presentOrder.getUid());
        request.setPid(presentConfig.getPresentCode());
        Integer baseAmount = DateUtils.getRestDay(presentOrder, receiveType);
        request.setAmount(baseAmount);
        //设置唯一actCode避免重复赠送问题
        request.setActCode(PresentConstants.ACTCODE);
        request.setTradeCode(presentOrder.getPresentTradeCode());
        request.setPayType(presentConfig.getPayType());
        request.setOriginalOrderCode(presentOrder.getOrderCode());
        request.setFv(presentOrder.getFv());
        return request;
    }
    /**
     * 多语言单点PresentOrder封装
     */
    public static PresentOrder packageVodPresentOrderRequest(OrderMessage message, PresentConfig presentConfig,
                                                             Integer status, String presentOrderCode,
                                                             PresentVipRequest request, Integer orderType) {
        PresentOrder presentOrder = new PresentOrder();
        presentOrder.setMsgId(message.getMsgId());
        presentOrder.setOrderCode(message.getOrderCode());
        presentOrder.setPresentTradeCode(request.getTradeCode());
        //买的会员类型
        presentOrder.setBuyType(presentConfig.getBvipType());
        //赠的会员类型
        presentOrder.setPresentType(presentConfig.getPvipType());
        //赠的数量
        presentOrder.setProductAmount(request.getAmount());
        //赠送状态；0：已赠送；1：未赠送；2：赠送中；3：退款成功；
        presentOrder.setStatus(status);
        presentOrder.setDeadlineStartTime(message.getStartTime());
        presentOrder.setDeadlineEndTime(message.getEndTime());
        Date currentDate = new Date();
        presentOrder.setReceiveTime(currentDate);
        presentOrder.setPayTime(message.getPayTime());
        presentOrder.setUpdateTime(currentDate);
        presentOrder.setCreateTime(currentDate);
        presentOrder.setUid(message.getUid());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(presentOrderCode)) {
            presentOrder.setPresentOrderCode(presentOrderCode);
        }
        presentOrder.setOrderType(orderType);
        presentOrder.setFv(StringUtils.isNotBlank(message.getFv()) ? message.getFv():StringConstants.DEAULT_FV);
        presentOrder.setPresentConfigId(presentConfig.getId());
        return presentOrder;
    }

    /**
     * PresentOrder封装
     */
    public static PresentOrder packagePresentOrderRequest(OrderMessage message, PresentConfig presentConfig,
                                                          Integer status, String presentOrderCode,
                                                          PresentVipRequest request, Integer orderType) {
        PresentOrder presentOrder = new PresentOrder();
        presentOrder.setMsgId(message.getMsgId());
        presentOrder.setOrderCode(message.getOrderCode());
        presentOrder.setPresentTradeCode(request.getTradeCode());
        //买的会员类型
        String productSubtype = message.getProductSubtype();
        presentOrder.setBuyType(productSubtype == null ? "" : productSubtype + "");
        //赠的会员类型
        presentOrder.setPresentType(presentConfig.getPvipType());
        //赠的数量
        presentOrder.setProductAmount(request.getAmount());
        //赠送状态；0：已赠送；1：未赠送；2：赠送中；3：退款成功；
        presentOrder.setStatus(status);
        presentOrder.setDeadlineStartTime(message.getStartTime());
        presentOrder.setDeadlineEndTime(message.getEndTime());
        Date currentDate = new Date();
        presentOrder.setReceiveTime(currentDate);
        presentOrder.setPayTime(message.getPayTime());
        presentOrder.setUpdateTime(currentDate);
        presentOrder.setCreateTime(currentDate);
        presentOrder.setUid(message.getUid());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(presentOrderCode)) {
            presentOrder.setPresentOrderCode(presentOrderCode);
        }
        // 购买的产品code、数量、类型等信息
        String buyPid = "";
        JSONObject jsonObject = JSON.parseObject(message.getRefer());
        if(null!=jsonObject){
            buyPid = null==jsonObject.get("buyPid")?"":(String) jsonObject.get("buyPid");
        }
        if(StringUtils.isNotBlank(buyPid)){//增单
            request.setBuyPid(buyPid);
        }else{
            request.setBuyPid(message.getPid());
        }
        presentOrder.setOrderType(orderType);
        presentOrder.setFv(StringUtils.isNotBlank(message.getFv()) ? message.getFv() : StringConstants.DEAULT_FV);
        presentOrder.setPresentConfigId(presentConfig.getId());
        presentOrder.setIsPressureTest(message.getIsPressureTest());
        return presentOrder;
    }

    private static String presentVipActCode(String buyCode, String presentCode) {
        return "vip_present:" + buyCode + "->" + presentCode;
    }

    private static String presentVipTradeCode(String orderCode, String presentCode) {
        return orderCode + "_" + presentCode;
    }

    /**
     * BI统计逻辑，跟buyPid保持一致，使用在有buPid的场景
     * @param message
     * @param presentCode
     * @return
     */
    private static String presentNewVipTradeCode(OrderMessage message, String presentCode) {
        String tradeCode = message.getTradeCode();
        String[] tradeCodeArr = tradeCode.split("_");
        if(null==tradeCodeArr || tradeCodeArr.length==0){
            log.info("presentNewVipTradeCode tradeCode 格式错误:{}",message);
            return presentVipTradeCode(message.getOrderCode(), presentCode);
        }
        return tradeCodeArr[0] + "_" + presentCode;
    }

    /**
     * bi需要 获取原单code 跟buyPid保持一致，使用在有buPid的场景
     * @param message
     * @return
     */
    private static String presentOriginalOrderCode(OrderMessage message) {
        String tradeCode = message.getTradeCode();
        String[] tradeCodeArr = tradeCode.split("_");
        if(null==tradeCodeArr || tradeCodeArr.length==0){
            log.info("presentNewVipTradeCode OriginalOrderCode 格式错误:{}",message);
            return message.getOrderCode();
        }
        return tradeCodeArr[0];
    }

}