package com.iqiyi.vip.present.entity;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.iqiyi.vip.present.utils.GsonUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;

/**
 * check_record
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckRecord implements Serializable {

    public static final String BUNDLE_CODES = "bundleCodes";

    private Long id;

    private Integer ruleId;

    private String orderCode;

    private String extendParam;

    private Date createTime;

    private Date updateTime;

    /**
     * {@link com.iqiyi.vip.present.enums.CheckRecordStatusEnum}
     */
    private Integer status;

    private Long userId;

    private static final long serialVersionUID = 1L;

    private Map<String, String> extendMap;

    public void buildCertificateInfoFromOrder(Order order) {
        if (order == null || StringUtils.isEmpty(order.getRefer())) {
            return;
        }
        JsonObject referResult = GsonUtils.fromJson(order.getRefer(), JsonObject.class);

        // 创建一个 Map 来存储 extendParam 的内容
        Map<String, String> extendMap = new HashMap<>();
        // 1. 如果 refer 有 saleScene，获取 points
        if (referResult.has(OrderMessage.SALE_SCENE) && referResult.has(OrderMessage.POINTS)) {
            extendMap.put(OrderMessage.POINTS, referResult.get(OrderMessage.POINTS).getAsString());
        }

        JsonObject businessProperty = referResult.getAsJsonObject(OrderMessage.BUSINESS_PROPERTY);
        if (businessProperty == null) {
            return;
        }

        // 2. 从 businessProperty 获取 couponCode
        if (businessProperty.has(OrderMessage.COUPON_CODE)) {
            extendMap.put(OrderMessage.COUPON_CODE, businessProperty.get(OrderMessage.COUPON_CODE).getAsString());
        }

        // 3. 从 businessProperty 获取 pointsDiscount
        if (businessProperty.has(OrderMessage.POINTS_DISCOUNT)) {
            extendMap.put(OrderMessage.POINTS_DISCOUNT, "合作方积分");
        }


        // 4. 从 businessProperty 获取 redPacketBatchCode
        if (businessProperty.has(OrderMessage.RED_BATCH_CODE_NAME)) {
            extendMap.put(OrderMessage.RED_BATCH_CODE_NAME, businessProperty.get(OrderMessage.RED_BATCH_CODE_NAME).getAsString());
        }
        // 5. 从 businessProperty 获取 orderExt
        if (businessProperty.has(OrderMessage.ORDER_EXT)) {
            String orderExt = businessProperty.get(OrderMessage.ORDER_EXT).getAsString();
            JsonObject orderExtObj = GsonUtils.fromJson(orderExt, JsonObject.class);
            if (orderExtObj.has(OrderMessage.ORDER_EXT_TOP_GIFT_LIST)) {
                JsonArray topGiftListArray = orderExtObj.getAsJsonArray(OrderMessage.ORDER_EXT_TOP_GIFT_LIST);
                if (topGiftListArray != null) {
                    List<String> bundleCodeList = new ArrayList<>();
                    for (JsonElement topGift : topGiftListArray) {
                        JsonObject topGiftObject = topGift.getAsJsonObject();
                        if (topGiftObject.has("type") && topGiftObject.get("type").getAsInt() == 1) {
                            bundleCodeList.add(topGiftObject.get("code").getAsString());
                        }
                    }
                    if (CollectionUtils.isNotEmpty(bundleCodeList)) {
                        extendMap.put(BUNDLE_CODES,StringUtils.join(bundleCodeList,","));
                    }
                }
            }
        }

        // 将 Map 转换为 JSON 字符串并设置到 extendParam
        if (MapUtils.isNotEmpty(extendMap)) {
            this.extendParam = GsonUtils.toJson(extendMap); 
            this.extendMap =  extendMap;
        }
    }
}
