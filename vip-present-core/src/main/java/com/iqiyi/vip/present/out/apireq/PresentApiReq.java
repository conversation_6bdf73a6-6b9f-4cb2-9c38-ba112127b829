package com.iqiyi.vip.present.out.apireq;

import com.iqiyi.vip.present.model.PresentOrder;
import org.springframework.core.env.Environment;

import java.util.Map;

import com.iqiyi.vip.present.out.PresentVipRequest;

/**
 * 第三方赠送接口请求信息
 *
 * <AUTHOR> (<EMAIL>)
 * @date 2020/3/11 11:53
 */
public interface PresentApiReq {
    String DEADLINE_START_TIME = "startTime";
    String DEADLINE_END_TIME = "endTime";
    /**
     * 赠送接口地址
     * @param environment
     * @return
     */
    String presentUrl(Environment environment);

    /**
     * 赠送接口参数
     * @param request
     * @param environment
     * @param presentOrder
     * @return
     */
    Map presentParams(PresentVipRequest request, Environment environment, PresentOrder presentOrder);

    /**
     * 回收接口地址
     * @param environment
     * @return
     */
    String cancelUrl(Environment environment);

    /**
     * 回收接口参数
     * @param request
     * @param environment
     * @return
     */
    Map cancelParams(Map request, Environment environment);


    Boolean isCancelLb(Environment environment);


    Boolean ispPresentLb(Environment environment);

}
