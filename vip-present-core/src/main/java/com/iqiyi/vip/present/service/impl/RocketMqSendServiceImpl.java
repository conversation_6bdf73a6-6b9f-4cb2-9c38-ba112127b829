package com.iqiyi.vip.present.service.impl;

import com.alibaba.fastjson.JSON;
import com.iqiyi.lego.rocketmq.core.StringRocketMQTemplate;
import com.iqiyi.vip.present.data.VipRightsGrantMessage;
import com.iqiyi.vip.present.service.RocketMqSendService;
import com.iqiyi.vip.present.task.PresentVipAsyncTask;
import com.iqiyi.vip.present.utils.CloudConfigUtil;
import com.iqiyi.vip.repository.ClusterAsyncTaskManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
@Slf4j
public class RocketMqSendServiceImpl implements RocketMqSendService {

    @Resource
    private DefaultMQProducer vipRightsGrantProducer;
    @Resource
    private ClusterAsyncTaskManager clusterAsyncTaskManager;
    @Value("${present.vip.rights.grant.producer.topic}")
    private String rmqTopicName;

    @Override
    public void sendVipRightsGrantMessage(VipRightsGrantMessage vipRightsGrantMessage) {
        try {
            byte[] msgBytes = JSON.toJSONBytes(vipRightsGrantMessage);
            Message message = new Message(rmqTopicName, msgBytes);
            SendResult sendResult = vipRightsGrantProducer.send(message);
            if(SendStatus.SEND_OK != sendResult.getSendStatus()){
                clusterAsyncTaskManager.insertTask(new PresentVipAsyncTask(vipRightsGrantMessage.getRequest(), vipRightsGrantMessage.getPresentOrder()));
                log.info("vipRightsGrantRmqTemplate send fail,vipRightsGrantMessage:{}", vipRightsGrantMessage);
            }else {
                log.info("vipRightsGrantRmqTemplate send result success:{}", vipRightsGrantMessage);
            }
        }catch (Exception e){
            clusterAsyncTaskManager.insertTask(new PresentVipAsyncTask(vipRightsGrantMessage.getRequest(), vipRightsGrantMessage.getPresentOrder()));
            log.error("rmqVipRightsGrant send message error,vipRightsGrantMessage:{}", vipRightsGrantMessage,e);
        }
    }
}
