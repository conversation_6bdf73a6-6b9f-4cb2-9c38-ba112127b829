package com.iqiyi.vip.present.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;

/**
 * 自定义反序列化器，用于处理字符串"null"到Integer的转换
 * 当遇到字符串"null"时，返回null值而不是抛出异常
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class NullStringToIntegerDeserializer extends JsonDeserializer<Integer> {
    
    @Override
    public Integer deserialize(JsonParser p, DeserializationContext ctxt) 
            throws IOException, JsonProcessingException {
        String value = p.getValueAsString();
        
        // 如果是空字符串、null字符串或者字面量"null"，返回null
        if (StringUtils.isBlank(value) || "null".equals(value)) {
            return null;
        }
        
        try {
            return Integer.valueOf(value);
        } catch (NumberFormatException e) {
            // 如果无法转换为Integer，返回null而不是抛出异常
            return null;
        }
    }
}
