package com.iqiyi.vip.present.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.iqiyi.kiwi.utils.HttpClientConnection;
import com.iqiyi.kiwi.utils.JsonResult;
import com.iqiyi.vip.present.consts.TaskPoolTypeEnum;
import com.iqiyi.vip.present.utils.ApplicationContextUtil;
import com.iqiyi.vip.present.utils.EncodeUtils;
import com.iqiyi.vip.present.utils.JacksonUtils;
import com.iqiyi.vip.threadpool.AbstractTask;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.core.env.Environment;

import java.util.Map;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SendVipTask extends AbstractTask {
    @Setter
    @Getter
    private Map param;

    public SendVipTask(Map param) {
        this.param = param;
    }

    @Override
    protected boolean execute() {
        logger.info("[action:SendVipTask] [step:start] [param:{}]", param);
        try {
            Environment environment = (Environment) ApplicationContextUtil.getBean(Environment.class);
            String refundUrl = environment.getProperty("dopay.url");
            param.put("sign", EncodeUtils.signMessage(param, environment.getProperty("internal.pay.key")));
            HttpClientConnection hcc = new HttpClientConnection(refundUrl, HttpClientConnection.POST_METHOD);
            hcc.setReqParams(param);
            hcc.setSoTimeout(5000);
            boolean status = hcc.connect();
            logger.info("[action:SendVipTask] [step:return] [res:{}]", new Object[]{hcc.getBody()});
            if (status) {
                JsonResult result = JacksonUtils.parseObject(hcc.getBody(), JsonResult.class);
                logger.info("[action:SendVipTask]异步调用会员系统赠送会员接口返回结果：{}", (result != null ? result.getCode() : null));
                if (result == null || (!"A00000".equals(result.getCode()) && !"Q00348".equals(result.getCode()))) {
                    return false;
                }
            } else {
                logger.error("[action:SendVipTask] [param:{}] [hcc.status:{}]", param, hcc.getStatus());
                return false;
            }

        } catch (Exception e) {
            logger.error("[action:SendVipTask]异步调用会员系统赠送会员接口ERROR：" + param, e);
            return false;
        }
        return true;
    }


    @Override
    public void deserialize(String data) throws IllegalArgumentException {
        JSONObject obj = JSON.parseObject(data);
        this.param = obj.getObject("param", Map.class);

    }

    @Override
    public String serialize() {
        JSONObject obj = new JSONObject();
        obj.put("param", param);
        return obj.toJSONString();
    }

    @Override
    public int getDefaultPoolType() {
        return TaskPoolTypeEnum.PARTNER_SEND_VIP.getType();
    }
}
