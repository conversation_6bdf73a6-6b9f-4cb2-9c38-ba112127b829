package com.iqiyi.vip.present.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.MessageDigestAlgorithms;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 **/
@Slf4j
public class SignatureInterceptor implements ClientHttpRequestInterceptor {

    private static final String SIGNATURE_HEADER = "X-Signature";
    private static final String SOURCE_HEADER = "X-Partner";

    private final String source;
    private final String signatureKey;

    private final String signAlgorithm;

    public SignatureInterceptor(String source, String signatureKey, String signAlgorithm) {
        this.source = source;
        this.signatureKey = signatureKey;
        this.signAlgorithm = signAlgorithm;
    }

    public SignatureInterceptor(String source, String signatureKey) {
        this.source = source;
        this.signatureKey = signatureKey;
        this.signAlgorithm = MessageDigestAlgorithms.MD5;
    }

    @Override
    public ClientHttpResponse intercept(final HttpRequest request, final byte[] body, final ClientHttpRequestExecution execution) throws IOException {

        // 获取请求体
        String requestBody = new String(body, StandardCharsets.UTF_8);

        // 生成签名
        String signature;
        try {
            signature = generateSignature(requestBody, signatureKey, signAlgorithm);
            log.info("for qingle : generate signature is {}", signature);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }

        // 添加签名到请求头
        request.getHeaders().add(SIGNATURE_HEADER, signature);
        request.getHeaders().add(SOURCE_HEADER, source);

        return execution.execute(request, body);
    }

    private String generateSignature(String requestBody, String signatureKey, String algorithm)
        throws NoSuchAlgorithmException {
        String toSignStr = requestBody + signatureKey;
        try {
            return md5(toSignStr);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Failed to generate signature", e);
        }
    }


    private String md5(String toSignStr) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] digest = md.digest(toSignStr.getBytes(StandardCharsets.UTF_8));
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

}
