package com.iqiyi.vip.present.api;

import com.alibaba.fastjson.JSONObject;
import com.iqiyi.vip.present.apiresponse.BaseResponse;
import com.iqiyi.vip.present.service.RestTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2024/3/4
 * @apiNote
 */
@Component
@Slf4j
public class FreeDoPayApi {

    @Value("${free.order.url:}")
    private String freeOrderUrl;
    @Value("${free.order.signKey:}")
    private String signKey;

    @Resource
    private RestTemplateService restTemplateService;

    public BaseResponse<String> pay(Long uid, Integer payType, Integer amount, String skuId, Integer source, String tradeCode, String actCode) {
        log.info("FreeDoPayApi");
        Map<String, String> map = new HashMap<>();
        map.put("tradeCode", tradeCode); //固定规则
        map.put("payType", String.valueOf(payType));
        map.put("actCode", actCode);
        map.put("skuId", skuId);
        map.put("skuAmount", String.valueOf(amount));
        map.put("uid", String.valueOf(uid));
        map.put("sign", getSign(map));
        try {
            log.info("FreeDoPayApi pay params:{}", map);
            JSONObject bodyJson = restTemplateService.postForEntity(freeOrderUrl, map, new ParameterizedTypeReference<JSONObject>() {
            }, true);
            log.info("FreeDoPayApi pay params:{}, response:{}", map, bodyJson);

            return parseResponse(bodyJson);
        } catch (Exception e) {
            log.error("FreeDoPayApi pay params:{}, exception:", map, e);
            return BaseResponse.createParamError("请求交易下单失败");
        }
    }

    private BaseResponse<String> parseResponse(JSONObject bodyJson) {
        if (bodyJson == null || bodyJson.isEmpty()) {
            return BaseResponse.createParamError("请求交易下单失败");
        }
        String code = bodyJson.getString("code");
        if (Objects.equals(code, "A00000") || Objects.equals(code, "Q00348")) {
            JSONObject dataJson = bodyJson.getJSONObject("data");
            if (dataJson != null) {
                return BaseResponse.createSuccess(dataJson.getString("orderCode"));
            }
        }
        return BaseResponse.createParamError("请求交易下单失败");
    }

    private String getSign(Map<String, String> map) {
        TreeMap<String, String> treeMap = new TreeMap<>(map);
        StringBuilder sb = new StringBuilder();

        for (Map.Entry<String, String> entry : treeMap.entrySet()) {
            sb.append("&").append(entry.getKey()).append("=").append(entry.getValue());
        }

        sb.append(signKey);
        return DigestUtils.md5Hex(sb.subSequence(1, sb.length()).toString());
    }
}
