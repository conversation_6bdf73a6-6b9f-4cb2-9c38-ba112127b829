package com.iqiyi.vip.present.service.impl;

import com.iqiyi.vip.present.consts.EnumOrderStatusCode;
import com.iqiyi.vip.present.entity.CheckRecord;
import com.iqiyi.vip.present.entity.Order;
import com.iqiyi.vip.present.enums.CheckRecordStatusEnum;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.service.CompensationStatusService;
import com.iqiyi.vip.present.service.PresentOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 补偿状态检查服务实现
 * 
 * <AUTHOR>
 * @date 2024/12/20
 */
@Service
@Slf4j
public class CompensationStatusServiceImpl implements CompensationStatusService {

    @Resource
    private PresentOrderService presentOrderService;

    @Override
    public boolean checkCompensationStatus(Order order, CheckRecord checkRecord, PresentOrder queryPresentOrderParam) {
        if (order == null || queryPresentOrderParam == null) {
            return false;
        }

        try {
            //1. 判断checkRecord中的状态
            if (checkRecord != null && CheckRecordStatusEnum.isNoNeedOrCompletedCompensation(checkRecord.getStatus())) {
                log.info("CheckRecord状态为不匹配处理条件或无需补偿, orderCode: {}", order.getOrderCode());
                return true;
            }

            // 2. 根据传入的查询参数查询presentOrders（只需要取一条）
            List<PresentOrder> presentOrders = presentOrderService.queryOrderByParams(queryPresentOrderParam);
            if (!CollectionUtils.isEmpty(presentOrders)) {
                PresentOrder presentOrder = presentOrders.get(0);
                // 统一判断：只要不是"未赠送"状态，就认为已经补偿过了
                if (!EnumOrderStatusCode.NO_PRESENT.getCode().equals(presentOrder.getStatus())) {
                    log.info("订单已补偿，无需重复处理, orderCode: {}, presentOrderStatus: {}",
                            order.getOrderCode(), presentOrder.getStatus());
                    return true;
                }
            }

            log.info("检查订单补偿状态, orderCode: {}, status: {}", order.getOrderCode(), order.getStatus());
            return false;
        } catch (Exception e) {
            log.error("检查订单补偿状态异常, orderCode: {}", order.getOrderCode(), e);
            return false;
        }
    }

}
