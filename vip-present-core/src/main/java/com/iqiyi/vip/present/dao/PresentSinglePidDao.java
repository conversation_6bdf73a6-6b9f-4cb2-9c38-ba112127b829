package com.iqiyi.vip.present.dao;

import com.iqiyi.vip.present.mapper.PresentSinglePidMapper;
import com.iqiyi.vip.present.model.PresentSinglePid;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class PresentSinglePidDao {
    @Resource
    private PresentSinglePidMapper presentSinglePidMapper;

    public PresentSinglePidMapper getPresentSinglePidMapper() {
        return presentSinglePidMapper;
    }

    public int insertOrUpdate(PresentSinglePid singlePid) {
        PresentSinglePid dbSinglePid = presentSinglePidMapper.selectByPid(singlePid.getPid());
        if (null == dbSinglePid) {
            return presentSinglePidMapper.insertSelective(singlePid);
        } else {
            singlePid.setId(dbSinglePid.getId());
            return presentSinglePidMapper.updateByPrimaryKeySelective(singlePid);
        }
    }
}
