package com.iqiyi.vip.present.config;

import com.google.common.cache.CacheBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * Created at: 2022-04-13
 *
 * <AUTHOR>
 */
@Configuration
@EnableCaching
public class GuavaCacheConfig {

    /**
     * guavaCacheManager Bean的名字
     * */
    public static final String GUAVA_CACHE_MANAGER = "guavaCacheManager";

    @Value("${guava.expire.second:120}")
    private int expireSecond;

    @Value("${guava.max.num.size:2000}")
    private int maxNumSize;

    @Bean(GuavaCacheConfig.GUAVA_CACHE_MANAGER)
    public CacheManager cacheManager() {
        GuavaCacheManager cacheManager = new GuavaCacheManager();
        cacheManager.setCacheBuilder(
            CacheBuilder.newBuilder()
                .expireAfterWrite(expireSecond, TimeUnit.SECONDS)
                .maximumSize(maxNumSize));
        return cacheManager;
    }

    @Bean(name = "customKeyGenerator")
    public CustomKeyGenerator keyGenerator() {
        return new CustomKeyGenerator();
    }

}