package com.iqiyi.vip.present.service;

import com.iqiyi.vip.present.model.PresentPerform;

import java.util.Map;

/**
 * present_perform表服务类 User: zhangdaoguang Date: 2019/3/20 Time: 14:11
 */
public interface PresentPerformService {

    /**
     * 通过ID查PresentPerform
     */
    PresentPerform queryPresentPerformById(Long performId);

    /**
     * 通过ID查询receiveType
     *
     * @param performId id
     * @return receiveType, 查不到返回默认0(实时)
     * @see com.iqiyi.vip.present.consts.EnumReceiveTypeCode
     */
    Integer queryReceiveTypeByIdWithDefault(Long performId);

    Map<Long, PresentPerform> queryPerforms();
}
