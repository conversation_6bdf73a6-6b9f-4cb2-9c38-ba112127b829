package com.iqiyi.vip.present.service;

import com.iqiyi.vip.present.apiresponse.Page;
import com.iqiyi.vip.present.apiresponse.PresentOrderVO;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.task.PresentVipAsyncTask;

import java.util.List;
import java.util.Map;

public interface PresentOrderService {
    List<PresentOrder> queryOrderByParams(PresentOrder presentOrderRequest);

    int insertVipOrderPresent(PresentOrder presentOrder);

    int updateOrderStatus(PresentOrder presentOrder, Integer oldStatus);

    void saveOrderPresentAndTask(List<PresentVipAsyncTask.TaskData> taskDatas);

    void updateOrderPresenRefundtAndTask(List<PresentOrder> orders, List<Map<String, Object>> taskParams);

    void updateOrderPresenRefundtAndTask(PresentOrder order, Map<String, String> taskParam);

    List<PresentOrder> queryByParamsAndDate(PresentOrder presentOrderRequest, String startTime, String endTime);

    /**
     * 查询买奇异果送黄金待领取订单
     *
     * @return
     */
    List<PresentOrder> queryKiwiPresentGoldNotReceiveOrder();

    List<PresentOrder> queryOrderByPageParams(Map<String, Object> params);

    Integer findOrderTotalCounts(Map<String, Object> params);

     Page<PresentOrderVO> findCardBatchPage(Map<String, Object> params,String presentCode);

    PresentOrder queryByPresentTradeCode(Long uid,String presentTradeCode);
}
