package com.iqiyi.vip.present.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.util.TypeUtils;
import com.google.common.collect.Maps;
import com.iqiyi.vip.present.apiresponse.MultilingualResponse;
import com.iqiyi.vip.present.apiresponse.QiyueResponse;
import com.iqiyi.vip.present.data.MultilingualData;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.model.PresentSinglePid;
import com.iqiyi.vip.present.service.RestTemplateService;
import com.iqiyi.vip.present.utils.EncodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class QiyueApi {
    private static final String NULL = "null";
    @Value("${qiyue.singlePid.url:http://admin-test.vip.qiyi.domain/vip-operation-api/basisdata/singleProduct/list}")
    private String singlePidUrl;
    @Value("${qiyue.singlePid.sys:buyPresent}")
    private String singlePidSys;
    @Value("${qiyue.singlePid.secretKey:123456}")
    private String sinlgePidSecretKey;
    @Value("${qiyue.multilingual.url:http://t.vip.qiyi.domain:8080/vcq/multilingual/contents/query}")
    private String multilingualUrl;
    @Value("${qiyue.multilingual.bizSource:buy-present}")
    private String multilingualBizSource;
    @Value("${qiyue.multilingual.sourceKey:111111}")
    private String multilingualSourceKey;

    @Autowired
    private RestTemplateService restTemplateService;

    /**
     * 获取单点pid,条件为：http://wiki.qiyi.domain/pages/viewpage.action?pageId=464917614
     */
    public List<PresentSinglePid> getSinglePid(Integer pageNo, Integer pageSize) {
        log.info("[get single pid start][pageNo:][{}][pageSize:][{}]", pageNo, pageSize);
        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("pageSize", String.valueOf(pageSize));
        paramMap.put("pageNo", String.valueOf(pageNo));
        paramMap.put("sys", singlePidSys);
        paramMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
        paramMap.put("sign", qiyueSign(paramMap, sinlgePidSecretKey));
        final int RETRY_TIMES = 2;
        QiyueResponse<List<Map<String, Object>>> responseDTO = null;
        for (int i = 0; i < RETRY_TIMES; i++) {
            responseDTO = restTemplateService.getForResult(singlePidUrl, paramMap, QiyueResponse.class,false);
            if (responseDTO.getSuccess() && null != responseDTO.getDataList()) {
                List<Map<String, Object>> data = responseDTO.getDataList();
                List<PresentSinglePid> pids = new ArrayList<>();
                for (Map<String, Object> d : data) {
                    PresentSinglePid pid = new PresentSinglePid();
                    if (!d.containsKey("code")) {
                        continue;
                    }
                    pid.setPid(TypeUtils.castToString(d.get("code")));
                    if (d.containsKey("name")) {
                        pid.setName(TypeUtils.castToString(d.get("name")));
                    }
                    if (d.containsKey("description")) {
                        pid.setDescription(TypeUtils.castToString(d.get("description")));
                    }
                    pids.add(pid);
                }
                log.info("[get single pid success][size:][{}]", pids.size());
                return pids;
            }
        }
        log.warn("[get single pid fail][response:][{}]", JSON.toJSONString(responseDTO));
        return null;
    }

    /**
     * 获取多语言关联列表
     *
     * @param message
     * @return
     */
    public List<MultilingualData> getMultilingualData(OrderMessage message) {
        log.info("[get multilingual data start][messageId:][{}][aid:][{}]", message.getMsgId(), message.getAid());
        if (null == message.getAid() || NULL.equals(message.getAid())) {
            log.warn("[aid is null.][messageId:][{}]", message.getMsgId());
            return null;
        }
        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("bizSource", multilingualBizSource);
        paramMap.put("messageId", message.getMsgId());
        paramMap.put("aid", message.getAid());
        paramMap.put("sign", multilingualSign(paramMap, multilingualSourceKey));
        final int RETRY_TIMES = 2;
        MultilingualResponse<Map> responseDTO = null;
        for (int i = 0; i < RETRY_TIMES; i++) {
            responseDTO = restTemplateService.getForResult(multilingualUrl, paramMap, MultilingualResponse.class,true);
            if (responseDTO.isSuccess() && null != responseDTO.getData()) {
                List<Map> data = responseDTO.getData();
                List<MultilingualData> result = new ArrayList<>();
                for (Map d : data) {
                    MultilingualData md = new MultilingualData();
                    md.setNestedQpid(TypeUtils.castToLong(d.get("nestedQpid")));
                    md.setVodProductCode(TypeUtils.castToString(d.get("vodProductCode")));
                    result.add(md);
                }
                log.info("[get multilingual data success][size:][{}]", result.size());
                return result;
            }
        }
        log.warn("[get single pid fail][response:][{}]", JSON.toJSONString(responseDTO));
        return null;
    }

    /**
     * 获取pid接口签名
     */
    private String qiyueSign(Map<String, String> params, String secretKey) {
        StringBuilder sb = new StringBuilder();
        sb.append("sys").append("=").append(params.get("sys")).append("&").append("timestamp").append("=")
                .append(params.get("timestamp"));
        return EncodeUtils.MD5(sb.append(secretKey).toString(), "UTF-8");
    }

    /**
     * 获取单点买赠关系签名
     *
     * @param reqMap
     * @param signKey
     * @return
     */
    public static String multilingualSign(Map<String, String> reqMap, String signKey) {
        if (CollectionUtils.isEmpty(reqMap)) {
            throw new RuntimeException("reqMap is null or empty");
        }
        if (StringUtils.isEmpty(signKey)) {
            throw new RuntimeException("signKey is null or empty");
        }
        SortedMap<String, String> sortedParams = new TreeMap<>(reqMap);
        StringBuilder sb = new StringBuilder();
        Iterator<Map.Entry<String, String>> iterator = sortedParams.entrySet().iterator();
        while (iterator.hasNext()) {
            sb.append(iterator.next().getValue());
        }
        sb.append(signKey);

        log.info("Md5SignCalculator md5 before: {}", sb);
        String sign = DigestUtils.md5Hex(sb.toString());
        log.info("Md5SignCalculator md5 after: {}", sign);
        return sign;
    }

}
