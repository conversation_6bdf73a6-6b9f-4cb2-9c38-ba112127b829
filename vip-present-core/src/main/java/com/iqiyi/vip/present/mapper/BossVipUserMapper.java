package com.iqiyi.vip.present.mapper;

import com.iqiyi.vip.present.model.BossVipUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BossVipUserMapper {

    List<BossVipUser> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询数据表中最大时长未过期的用户id（限主站会员）
     * @param startUid
     * @return
     */
    List<BossVipUser> selectUserIdByGroupMax(@Param("userId") Long userId);
}