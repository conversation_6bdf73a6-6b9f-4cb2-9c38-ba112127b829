package com.iqiyi.vip.present.dao;

import com.iqiyi.vip.present.mapper.PresentOrderMapper;
import com.iqiyi.vip.present.model.PresentOrder;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Repository
public  class PresentOrderDao extends BaseDao<PresentOrder> {

    @Resource
    PresentOrderMapper presentOrderMapper;

    public List<PresentOrder> queryOrderByParams(PresentOrder presentOrderRequest) {
        return presentOrderMapper.queryOrderByParams(presentOrderRequest);
    }

    public int updateOrderStatus(PresentOrder presentOrder, Integer oldStatus) {
        return presentOrderMapper.updateOrderStatus(presentOrder, oldStatus);
    }

    public int updateStatusFromOld(Long uid, Long id, Integer newStatus, Integer oldStatus) {
        return presentOrderMapper.updateStatusFromOld(uid, id, newStatus, oldStatus);
    }

    public List<PresentOrder> queryByParamsAndDate(PresentOrder presentOrderRequest, String startTime, String endTime,String tableNo) {
        return presentOrderMapper.queryByParamsAndDate(presentOrderRequest, startTime, endTime, tableNo);
    }

    public Integer findOrderTotalCounts(Map<String, Object> params) {
        return presentOrderMapper.findOrderTotalCounts(params);
    }

    public List<PresentOrder> queryOrderByPageParams(Map<String, Object> params) {
        return presentOrderMapper.queryOrderByPageParams(params);
    }

    /**
     * 查询买奇异果送黄金，待领取订单列表 用于精准触达
     *
     * @return
     */
    public List<PresentOrder> queryKiwiPresentGoldNotReceiveOrder(Integer diffDay, String tableNo) {
        return presentOrderMapper.queryKiwiPresentGoldNotReceiveOrder(diffDay, tableNo);
    }

    public List<PresentOrder> queryKiwiPresentGoldNotReceiveOrderByRange(String startTime, String endTime, String tableNo, Integer configId, Integer limitNum) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime) || StringUtils.isBlank(tableNo)) {
            return Collections.emptyList();
        }
        return presentOrderMapper.queryKiwiPresentGoldNotReceiveOrderByRange(startTime, endTime, tableNo, configId, limitNum);
    }

    public List<PresentOrder> queryManualPresentOrders(String startTime, String endTime, String tableNo, List<Integer> configIdList, Integer limitNum) {
        return presentOrderMapper.queryManualPresentOrders(startTime, endTime, tableNo, configIdList, limitNum);
    }

    public PresentOrder queryByPresentTradeCode(Long uid,String presentTradeCode){
        return presentOrderMapper.queryByPresentTradeCode(uid,presentTradeCode);
    }
}
