package com.iqiyi.vip.present.service;

import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.model.PresentCondition;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.utils.ConvertUtils;
import com.iqiyi.vip.present.utils.ExecuteExpression;
import com.iqiyi.vip.present.utils.JacksonUtils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Created at: 2021-01-19
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PresentRuleComponent {

    @Resource
    PresentConditionService conditionService;
    @Resource
    PresentOrderService presentOrderService;

    /**
     * 条件匹配
     */
    public PresentCondition getTargetCondition(OrderMessage message, PresentConfig presentConfig) {
        long start = System.currentTimeMillis();
        log.info("[message:{}] [ids:{}]", message, presentConfig.getConditionIds());
        List<PresentCondition> presentConditionList = conditionService.queryConditionFromCacheByIds(presentConfig.getConditionIds());
        if (CollectionUtils.isEmpty(presentConditionList)) {
            return null;
        }
        //开始把数据库查询出来的条件和消息体的匹配
        Map<String, String> map = (Map<String, String>) ConvertUtils.objectToMap(message);
        log.info("[condition-map:{}]", JacksonUtils.toJsonString(map));
        PresentCondition targetCondition = null;
        for (PresentCondition condition : presentConditionList) {
            boolean isPresent = ExecuteExpression.executeCodeExpression(map, condition.getCondition());
            if (isPresent || StringUtils.isEmpty(condition.getCondition())) {
                targetCondition = condition;
                log.info("[targetCondition:{}]", JacksonUtils.toJsonString(targetCondition));
                break;
            }
        }
        log.info("getTargetCondition() [cost:{}ms]", System.currentTimeMillis() - start);
        return targetCondition;
    }

    /**
     * 订单是否匹配赠送配置
     * @param orderMessage
     * @param presentConfig
     */
    public boolean needNotPresent(OrderMessage orderMessage, PresentConfig presentConfig) {
        if (!presentConfig.getBvipType().equals(orderMessage.getProductSubtype())) {
            return true;
        }
        if (presentConfig.getStatus() != 1 || presentConfig.getHandlerStatus() != 1) {
            return true;
        }
        if (StringUtils.isNotBlank(presentConfig.getBuyCode()) && !presentConfig.getBuyCode().equals(orderMessage.getPid())) {
            return true;
        }
        if (alreadyProcessed(orderMessage, presentConfig)) {
            return true;
        }
        return false;
    }

    /**
     * 此订单是否已经处理
     * @param orderMessage
     * @param presentConfig
     */
    public boolean alreadyProcessed(OrderMessage orderMessage, PresentConfig presentConfig) {
        PresentOrder presentOrderRequest = new PresentOrder();
        presentOrderRequest.setOrderCode(orderMessage.getOrderCode());
        presentOrderRequest.setUid(orderMessage.getUid());
        presentOrderRequest.setPresentConfigId(presentConfig.getId());
        List<PresentOrder> list = presentOrderService.queryOrderByParams(presentOrderRequest);
        if (CollectionUtils.isNotEmpty(list)) {
            log.warn("此订单的赠送已经处理过了，orderCode:{}, presentConfigId:{}, presentConfigName:{}", orderMessage.getOrderCode(), presentConfig.getId(), presentConfig.getRemark());
            return true;
        }
        return false;
    }

}
