package com.iqiyi.vip.present.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/5/27 15:58
 */
@Data
public class OrderMessage {

    public static final String BUSINESS_PROPERTY = "businessProperty";

    public static final String COUPON_BATCH = "couponBatch";

    public static final String COUPON_CODE = "couponCode";

    public static final String SALE_SCENE = "saleScene";

    public static final String POINTS = "points";

    public static final String POINTS_DISCOUNT = "pointsDiscount";

    public static final String PRICE_ACT_CODE_ACTUAL = "priceActCodeActual";

    public static final String RED_BATCH_CODE_NAME = "redPacketBatchCode";

    public static final String ORDER_EXT = "orderExt";

    public static final String ORDER_EXT_TOP_GIFT_LIST = "topGiftList";

    public static final String RED_CODE_NAME = "redPacketCode";

    public static final String MAIN = "main_";

    public static final String SUB = "sub_";

    public static final String PACKAGE_ORDER_TYPE = "15";
    /**
     * 加价购订单
     */
    public static final String VIP_PLUS_ORDER_TYPE = "16";

    private String orderType;
    private String type;

    private String tradeCode;
    private String parentOrderCode;
    private String orderCode;

    //paid、prePaid是userId，finish是uid
    private Long userId;
    private Long uid;

    private Integer status;

    private String skuId;

    //paid、prePaid是frVersion，finish是fr_version
    private String frVersion;
    private String fr_version;

    /**
     * 金额相关
     */
    //paid、prePaid是realFee，finish是orderRealFeerealFee
    private Long realFee;

    private Long couponFee;

    private Long couponSettlementFee;


    private Long orderRealFee;


    private String platformCode;


    private String refer;

    private String skuAmount;

    private String payType;

    private String payChannel;

    private String autoRenew;

    private String accountId;

    private Integer amount;

    private Integer productType;
}
