package com.iqiyi.vip.present.apiresponse;

import com.iqiyi.vip.present.consts.EnumResultCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseResponse<T> implements Serializable {

    private static final long serialVersionUID = 0L;
    private String code;
    private String msg;
    private T data;

    public BaseResponse(EnumResultCode resultCode) {
        this.code = resultCode.getCode();
        this.msg = resultCode.getMsg();
    }
    
    public static BaseResponse createSuccess() {
        return new BaseResponse(EnumResultCode.SUCCESS);
    }

    public static <T> BaseResponse<T> createSuccess(T data) {
        BaseResponse<T> baseResponse = createSuccess();
        baseResponse.setData(data);
        return baseResponse;
    }

    public static BaseResponse createParamError() {
        return new BaseResponse(EnumResultCode.PARAM);
    }

    public static BaseResponse createParamError(String errorMsg) {
        return new BaseResponse(EnumResultCode.PARAM.getCode(), errorMsg, null);
    }

    public static BaseResponse createSystemError() {
        return new BaseResponse(EnumResultCode.SYSTEM_ERROR);
    }
    
    public static boolean isSuccess(String code) {
        return EnumResultCode.SUCCESS.getCode().equalsIgnoreCase(code);
    }

    public void setResultCode(EnumResultCode resultCode) {
        this.code = resultCode.getCode();
        this.msg = resultCode.getMsg();
    }
}
