package com.iqiyi.vip.present.consts;

import lombok.Getter;

/**
 * 异步任务枚举
 *
 * <AUTHOR>
 * @date 2019/06/20 17:13
 */
@Getter
public enum TaskPoolTypeEnum {
    /**
     * 线程池类型
     */
    PRESENT_VIP(1, "赠送会员任务"),
    REFUND_VIP(2, "退款任务"),
    PARTNER_SEND_VIP(3,"对外合作退款"),
    PRESENT_ASYNC_ORDER_MESSAGE(4,"异步处理订单消息任务"),
    ;


    private Integer type;

    private String desc;

    TaskPoolTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

}
