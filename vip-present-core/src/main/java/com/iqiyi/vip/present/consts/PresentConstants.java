package com.iqiyi.vip.present.consts;

import com.google.common.collect.Sets;

import java.util.Set;

/**
 * 赠送常量 User: zhang<PERSON>oguang Date: 2019/3/15 Time: 15:10
 */
public class PresentConstants {

    /**
     * present_config默认占位符,代表不存在
     */
    public static Long PRESENT_CONFIG_ID_DEFAULT = 0L;

    /**
     * 订单完成消息status: 1已支付
     */
    public static final Integer PAID_STATUS = 1;

    /**
     * 订单完成消息status: 6,12已退款
     */
    public static final Integer REFUND_STATUS_6 = 6;
    public static final Integer REFUND_STATUS_12 = 12;

    // 免费订单Pay Type
    public static final int PAY_TYPE_FREE_PRESENT_GOLD = 307; // 买奇异果送黄金
    public static final int PAY_TYPE_FREE_PRESENT_TVKIWI = 308; // 买钻石送奇异果
    public static final int PAY_TYPE_FREE_MARKETING = 305;// 会员营销系统调用赠送会员接口的固定支付方式
    public static final int PAY_TYPE_FREE_PRESENT_TENNIS_TVKIWI = 316; // 买网球赠网球奇异果
    public static final int PAY_TYPE_FREE_PRESENT_TVKIWI_TENNIS = 317; // 奇异果网球会员送爱奇艺网球会员
    public static final Integer PAY_TYPE_TV_GUO_PRESENT = 137; // 电视果赠送支付方式
    public static final Integer PAY_TYPE_DIAMOND_CHILD_PRESENT = 329; // 钻石送儿童支付方式
    public static final int PAY_TYPE_ADMIN_FREE = 24;//后台免费赠送
    public static final int HISTORY_USER_PRESENT = 454;//历史用户补增订单支付方式
    public static final Set<Integer> FREE_PAY_TYPE_SET = Sets.newHashSet(
        PAY_TYPE_FREE_PRESENT_GOLD,
        PAY_TYPE_FREE_PRESENT_TVKIWI,
        PAY_TYPE_FREE_MARKETING,
        PAY_TYPE_ADMIN_FREE,
        PAY_TYPE_FREE_PRESENT_TENNIS_TVKIWI,
        PAY_TYPE_FREE_PRESENT_TVKIWI_TENNIS,
        PAY_TYPE_DIAMOND_CHILD_PRESENT,
        PAY_TYPE_TV_GUO_PRESENT
    );
    public static final String DIAMOND_VIP_PID_OF_DAY = "8be4680dd4caa309";
    public static final String DIAMOND_VIP_PID_OF_MONTH = "adb3376b039b970b";
    public static final String DIAMOND_VIP_PID_OF_UPGRADE = "94f865839c851009";
    public static final Set<String> DIAMOND_VIP_PIDS = Sets.newHashSet(
        DIAMOND_VIP_PID_OF_DAY,
        DIAMOND_VIP_PID_OF_MONTH,
        DIAMOND_VIP_PID_OF_UPGRADE,
        "a6594ebf952c8618",
        "9bdc7c34ae4b29e8",
        "9412e5d3d239c5f7",
        "bc20cba0229e52a3",
        "ba99c928798c4d5f",
        "afbb7a1e6f823221",
        "87c9990a75913c1f",
        "a7cf12c10baf95eb",
        "a5750a0edc02bd19",
        "880bcceb948593c6",
        "ae3cb16226eb8174",
        "8edc52f3584268d8",
        "86a89acc3134de1f",
        "a6526db0ae6ac087",
        "ae57ec2a7298150c",
        "bcb56c8dd24c1952",
        "afc436d28d375a70",
        "a1ccb878339f59bd",
        "88ce51c0d6b5bd0b",
        "bcd2a69174475132",
        "bac9d8754da3ffad",
        "8f6ce3322b294089",
        "901d02ed7c446d58",
        "8f909f7988f68837",
        "a7b8075dcb50de94",
        "bacc64f357d034cb",
        "ac1532a56a18180b",
        "8ce35b570c7cd461",
        "993964302ebd890d",
        "88d3fa4c01359d22",
        "bf53f1d4525c85a3",
        "b0c6395129fe8db7",
        "a821351b1db0b761",
        "aab309858db5536f",
        "84bd6925e6afdbcf",
        "96b7319813d15483",
        "b713f59939d1ff9a",
        "a6dfa1d73814e1c0",
        "b0c7f9c9d2e4e89f",
            //TODO 提价后首单黄金升级星钻的pid，活动结束必须下线产品id
            "ae4cb99bbf2bd316",
            "9df48cee97703397"
    );

    public static final String ACTCODE= "vip_present";
    public static final String TYPE = "-1"; // 表示压测订单 或 苹果沙河订单

    public static final String DIAMOND_VIP_TYPE_CODE = "a89acfbe25ab47a6";

    // 奇异果钻石vip_type值
    public static final String TV_DIAMOND_VIP_TYPE = "54";

    //基础会员
    public static final Integer BASIC_VIP_TYPE = 56;

    public static final Integer BASIC_TV_VIP_TYPE = 60;

    //public static final Set<String> basic_vip_types = Sets.newHashSet("56","60");

    // 奇异果星钻、奇异果白金、单屏
    public static final Set<String> tv_vip_types = Sets.newHashSet("54","5","57");
    // 星钻、白金、黄金、学生
    public static final Set<String> main_vip_types = Sets.newHashSet("4","58","1","16");

    public static final Set<Integer> main_and_tv_vip_types = Sets.newHashSet(4,58,1,16,54,5,57,56,60);
}
