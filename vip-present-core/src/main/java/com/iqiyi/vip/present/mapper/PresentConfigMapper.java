package com.iqiyi.vip.present.mapper;

import com.iqiyi.vip.present.model.PresentConfig;

import java.util.List;

public interface PresentConfigMapper  extends BaseMapper{

    int insertSelective(PresentConfig record);

    @Override
    PresentConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PresentConfig record);

    List<PresentConfig> queryPresentConfig();

    List<PresentConfig> queryConfigListByGroup(String grouping);
    
    List<PresentConfig> queryConfigListByVipTypeList(List<String> bvipTypeList);

    List<PresentConfig> queryAllPresentConfig();
}