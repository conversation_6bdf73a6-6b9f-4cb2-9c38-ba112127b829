package com.iqiyi.vip.present.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.redisson.config.ReadMode;
import org.redisson.config.SubscriptionMode;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/7/26
 * @apiNote
 */
@Configuration
public class RedissonConfig {

    @Value("${spring.redisson.clusterServers}")
    private String clusterServers;

    @Value("${spring.redisson.password}")
    private String password;

    @Value("${spring.redisson.masterConnectionPoolSize:64}")
    private int masterConnectionPoolSize;

    @Value("${spring.redisson.slaveConnectionPoolSize:64}")
    private int slaveConnectionPoolSize;

    @Value("${spring.redisson.masterConnectionMinimumIdleSize:10}")
    private int masterConnectionMinimumIdleSize;

    @Value("${spring.redisson.slaveConnectionMinimumIdleSize:10}")
    private int slaveConnectionMinimumIdleSize;

    @Value("${spring.redisson.connectTimeout:10000}")
    private int connectTimeout;

    @Value("${spring.redisson.timeout:3000}")
    private int timeout;

    @Value("${spring.redisson.retryAttempts:3}")
    private int retryAttempts;

    @Value("${spring.redisson.retryInterval:1500}")
    private int retryInterval;

    @Value("${spring.redisson.pingConnectionInterval:30000}")
    private int pingConnectionInterval;

    @Value("${spring.redisson.idleConnectionTimeout:10000}")
    private int idleConnectionTimeout;

    @Bean(destroyMethod = "shutdown")
    public RedissonClient redissonClient() {
        Config config = new Config();

        try {
            ClusterServersConfig clusterServersConfig = config.useClusterServers()
                    .setScanInterval(2000) // 集群状态扫描间隔时间，单位是毫秒
                    // 连接池配置
                    .setMasterConnectionPoolSize(masterConnectionPoolSize) // 主节点连接池大小
                    .setSlaveConnectionPoolSize(slaveConnectionPoolSize)  // 从节点连接池大小
                    .setMasterConnectionMinimumIdleSize(masterConnectionMinimumIdleSize) // 主节点连接池最小空闲连接数
                    .setSlaveConnectionMinimumIdleSize(slaveConnectionMinimumIdleSize)  // 从节点连接池最小空闲连接数
                    // 超时配置
                    .setConnectTimeout(connectTimeout)    // 连接超时，单位：毫秒
                    .setTimeout(timeout)            // 命令等待超时，单位：毫秒
                    .setRetryAttempts(retryAttempts)         // 命令失败重试次数
                    .setRetryInterval(retryInterval)      // 命令重试发送时间间隔，单位：毫秒
                    // 心跳检测
                    .setPingConnectionInterval(pingConnectionInterval) // ping连接间隔
                    .setKeepAlive(true)          // 保持连接活跃
                    // 空闲连接超时
                    .setIdleConnectionTimeout(idleConnectionTimeout) // 空闲连接超时时间
                    // 修复Spring Boot 2.x升级后的READONLY命令兼容性问题
                    .setReadMode(ReadMode.MASTER) // 强制从主节点读取，避免READONLY命令
                    .setSubscriptionMode(SubscriptionMode.MASTER) // 订阅模式也使用主节点
                    // 禁用从节点检查，避免READONLY命令问题
                    .setCheckSlotsCoverage(false); // 禁用槽位覆盖检查，提高兼容性

            clusterServersConfig.addNodeAddress(clusterServers.split(","));
            clusterServersConfig.setPassword(password);

            return Redisson.create(config);
        } catch (Exception e) {
            // 如果集群模式失败，尝试使用单节点模式作为备用方案
            System.err.println("集群模式连接失败，尝试单节点模式: " + e.getMessage());

            // 使用第一个节点作为单节点连接
            String firstNode = clusterServers.split(",")[0];
            config = new Config();
            config.useSingleServer()
                    .setAddress(firstNode)
                    .setPassword(password)
                    .setConnectionPoolSize(masterConnectionPoolSize)
                    .setConnectionMinimumIdleSize(masterConnectionMinimumIdleSize)
                    .setConnectTimeout(connectTimeout)
                    .setTimeout(timeout)
                    .setRetryAttempts(retryAttempts)
                    .setRetryInterval(retryInterval)
                    .setPingConnectionInterval(pingConnectionInterval)
                    .setIdleConnectionTimeout(idleConnectionTimeout);

            return Redisson.create(config);
        }
    }
}
