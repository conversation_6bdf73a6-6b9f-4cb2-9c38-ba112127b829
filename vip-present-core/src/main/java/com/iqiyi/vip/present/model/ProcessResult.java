package com.iqiyi.vip.present.model;

import com.iqiyi.vip.present.consts.EnumResultCode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 处理结果封装类
 * 用于封装消息处理的结果状态
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProcessResult {
    
    /**
     * 结果状态码
     */
    private EnumResultCode resultCode;
    
    /**
     * 结果消息
     */
    private String message;
    
    /**
     * 创建成功结果
     */
    public static ProcessResult success(String message) {
        return new ProcessResult(EnumResultCode.SUCCESS, message);
    }
    
    /**
     * 创建无需赠送结果
     */
    public static ProcessResult noNeedPresent(String message) {
        return new ProcessResult(EnumResultCode.NO_NEED_PRESENT, message);
    }

    /**
     * 创建接受数据成功，赠送中结果
     */
    public static ProcessResult acceptDataPresenting(String message) {
        return new ProcessResult(EnumResultCode.SUCCESS, message);
    }
    
    /**
     * 判断是否为成功状态
     */
    public boolean isSuccess() {
        return EnumResultCode.SUCCESS.equals(resultCode);
    }

}
