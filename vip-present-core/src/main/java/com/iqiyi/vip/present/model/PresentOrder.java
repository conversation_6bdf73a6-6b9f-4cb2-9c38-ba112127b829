package com.iqiyi.vip.present.model;

import lombok.Data;

import java.util.Date;

@Data
public class PresentOrder {

    private Long id;

    private Long uid;

    private String msgId;

    private String orderCode;

    private String presentOrderCode;

    private String presentTradeCode;

    private String refundOrderCode;

    private String buyType;

    private String presentType;

    private Integer productAmount;

    private Integer status;

    private Long presentConfigId;

    private Long presentConditionId;

    private Long presentPerformId;

    private Date deadlineStartTime;

    private Date deadlineEndTime;

    private Date payTime;

    private Date receiveTime;

    private Date receiveDeadlineTime;

    private Date updateTime;

    private Date createTime;

    private Integer orderType;

    private String fv;

    private String isPressureTest;

}