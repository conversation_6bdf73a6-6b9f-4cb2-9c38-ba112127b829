package com.iqiyi.vip.present.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iqiyi.vip.present.exception.CustomJsonException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Map;

/**
 * Created at: 2021-01-19
 *
 * <AUTHOR>
 */
@Slf4j
public class JacksonUtils {

    // 默认的ObjectMapper，配置为忽略未知属性和支持日期格式
    private static final ObjectMapper DEFAULT_MAPPER = new ObjectMapper();

    static {
        // 初始化默认ObjectMapper
        DEFAULT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 配置日期格式，支持 "yyyy-MM-dd HH:mm:ss" 格式
        DEFAULT_MAPPER.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        // 配置当遇到空字符串或"null"字符串时，将其转换为null值
        DEFAULT_MAPPER.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
        DEFAULT_MAPPER.configure(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL, true);
    }

    public static <T> T parseObject(String jsonString, Class<T> clazz) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        try {
            return DEFAULT_MAPPER.readValue(jsonString, clazz);
        } catch (IOException e) {
            log.error("occurred exception, jsonString: {}", jsonString, e);
            throw new CustomJsonException("解析json出现异常", e);
        }
    }

    public static <T> T parseObject(byte[] jsonBytes, Class<T> clazz) {
        if (jsonBytes == null) {
            return null;
        }
        try {
            return DEFAULT_MAPPER.readValue(jsonBytes, clazz);
        } catch (IOException e) {
            log.error("occurred exception, jsonString: {}", StringUtils.toEncodedString(jsonBytes, StandardCharsets.UTF_8), e);
            throw new CustomJsonException("解析json出现异常", e);
        }
    }

    public static <T> T parseObject(String jsonString, TypeReference<T> typeReference) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        try {
            return DEFAULT_MAPPER.readValue(jsonString, typeReference);
        } catch (IOException e) {
            log.error("occurred exception, jsonString: {}", jsonString, e);
            throw new CustomJsonException("解析json出现异常", e);
        }
    }

    public static <T> String toJsonString(T object) {
        if (object == null) {
            return null;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("writeValueAsString occurred exception, objectValue: {}", object, e);
            return null;
        }
    }

    /**
     * 将Map转换为指定类型的对象
     *
     * @param map Map数据
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 转换后的对象
     */
    public static <T> T mapToBean(Map<String, ?> map, Class<T> clazz) {
        if (map == null || map.isEmpty()) {
            return null;
        }
        return DEFAULT_MAPPER.convertValue(map, clazz);
    }

}
