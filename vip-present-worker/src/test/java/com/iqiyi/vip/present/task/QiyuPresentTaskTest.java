package com.iqiyi.vip.present.task;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.iqiyi.vip.present.consts.EnumOrderStatusCode;
import com.iqiyi.vip.present.consts.EnumOrderTypeCode;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.mapper.PresentConditionMapper;
import com.iqiyi.vip.present.model.PresentCondition;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.model.PresentPerform;
import com.iqiyi.vip.present.out.PresentVipRequest;
import com.iqiyi.vip.present.service.PresentConfigService;
import com.iqiyi.vip.present.service.PresentOrderService;
import com.iqiyi.vip.present.service.PresentPerformService;
import com.iqiyi.vip.present.utils.CalAmountUtils;
import com.iqiyi.vip.present.utils.PackagePresentVip;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * Created at: 2020-10-15
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class QiyuPresentTaskTest {

    static {
        System.setProperty("spring.profiles.active", "dev");
    }

    @Resource
    PresentConfigService presentConfigService;
    @Resource
    PresentConditionMapper presentConditionMapper;
    @Resource
    PresentPerformService presentPerformService;
    @Resource
    PresentOrderService presentOrderService;

    @Test

    public void doTest() {
        PresentConfig presentConfig = presentConfigService.queryConfigById(107L);
        String conditionIds = presentConfig.getConditionIds();
        PresentCondition presentCondition = presentConditionMapper.selectByPrimaryKey(Long.valueOf(conditionIds));
        PresentPerform presentPerform = presentPerformService.queryPresentPerformById(presentCondition.getPerformId());

        String orderMsg = "{\"payTime\":\"2020-10-15 16:38:27\",\"channel\":\"afbe8fd3d73448c9\",\"chargeType\":\"1\",\"pid\":\"adb3376b039b970b\",\"phoneNum\":\"***********\",\"type\":\"1\",\"deviceId\":\"330169ca312855766ce2d6cc2f115c81662e66f4\",\"businessCode\":\"lyksc7aq36aedndk\",\"payType\":\"99\",\"msgtype\":\"7\",\"productId\":\"870\",\"tradeNo\":\"202010151638250477104\",\"fr_version\":\"d=330169ca312855766ce2d6cc2f115c81662e66f4&k=8e48946f144759d86a50075555fd5862&v=11.9.6&aid=&test=&login=&dfp=874bbcc6bf0dac4942a9dcf15ecdcde6afd71fb40e58664686949aa0185566a6b9\",\"tradeCode\":\"350000738173022\",\"settlementFee\":\"4000\",\"orderRealFee\":\"4000\",\"centerPayType\":\"APPLEIAPDUT\",\"actCode\":\"iqiyi_diamond_vip_iphone_video_autorenew_1m_40\",\"expireTime\":\"2020-11-12 00:19:46.0\",\"sourceType\":\"\",\"fc\":\"a9e4fec171bb4c74\",\"aid\":\"null\",\"platformCode\":\"bb35a104d95490f6\",\"productSubtype\":\"4\",\"status\":\"1\",\"serviceCode\":\"lyksc7aq36aedndk\",\"orderFee\":\"4000\",\"couponSettlementFee\":\"0\",\"platform\":\"12\",\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"uid\":\"1480407671\",\"centerCode\":\"2020101559906001489\",\"autoRenew\":\"1\",\"startTime\":\"2020-11-11 23:17:41\",\"thirdUid\":\"\",\"renewFlag\":\"1\",\"productType\":\"1\",\"currencyUnit\":\"CNY\",\"amount\":\"1\",\"beforePaidSign\":\"1\",\"beforeDeadline\":\"2020-11-11 23:17:41\",\"createTime\":\"2020-10-15 16:38:26\",\"orderCode\":\"202010151638250477104\",\"payChannel\":\"9\",\"endTime\":\"2020-12-11 23:17:41\",\"centerPayService\":\"446\",\"gateway\":\"1\"}";
        OrderMessage message = buildOrderMsg(orderMsg);
        PresentVipRequest presentVipRequest = PackagePresentVip.packagePresentVipRequest(message, presentConfig);
        PresentOrder presentOrder = PackagePresentVip.packagePresentOrderRequest(message, presentConfig,
                EnumOrderStatusCode.NO_PRESENT.getCode(), null, presentVipRequest, EnumOrderTypeCode.YES.getCode());
        presentOrder.setPresentConditionId(presentCondition.getId());
        presentOrder.setPresentPerformId(presentPerform.getId());
        if (presentPerform.getAmount() != null) {
            presentOrder.setProductAmount(presentPerform.getAmount());
        }
        int inserted = presentOrderService.insertVipOrderPresent(presentOrder);
        assertTrue(inserted >= 0);
        PresentVipAsyncTask qiyuPresentTask = new PresentVipAsyncTask(presentVipRequest, presentOrder);
        boolean success = qiyuPresentTask.execute();
        assertTrue(success);


        String cancelOrderMsg = "{\"payTime\":\"2020-10-16 14:58:49\",\"channel\":\"afbe8fd3d73448c9\",\"chargeType\":\"1\",\"pid\":\"adb3376b039b970b\",\"phoneNum\":\"***********\",\"type\":\"1\",\"deviceId\":\"330169ca312855766ce2d6cc2f115c81662e66f4\",\"businessCode\":\"lyksc7aq36aedndk\",\"payType\":\"99\",\"msgtype\":\"7\",\"productId\":\"870\",\"tradeNo\":\"202010151638250477104\",\"fr_version\":\"d=330169ca312855766ce2d6cc2f115c81662e66f4&k=8e48946f144759d86a50075555fd5862&v=11.9.6&aid=&test=&login=&dfp=874bbcc6bf0dac4942a9dcf15ecdcde6afd71fb40e58664686949aa0185566a6b9\",\"tradeCode\":\"202010151638250477104\",\"settlementFee\":\"-4000\",\"orderRealFee\":\"-4000\",\"centerPayType\":\"APPLEIAPDUT\",\"actCode\":\"iqiyi_diamond_vip_iphone_video_autorenew_1m_40\",\"expireTime\":\"2020-11-12 00:19:46.0\",\"sourceType\":\"\",\"fc\":\"a9e4fec171bb4c74\",\"aid\":\"null\",\"platformCode\":\"bb35a104d95490f6\",\"productSubtype\":\"4\",\"status\":\"6\",\"serviceCode\":\"lyksc7aq36aedndk\",\"orderFee\":\"-4000\",\"couponSettlementFee\":\"0\",\"platform\":\"12\",\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"uid\":\"1480407671\",\"centerCode\":\"2020101614584967541\",\"autoRenew\":\"1\",\"startTime\":\"2020-12-11 23:17:41\",\"thirdUid\":\"\",\"renewFlag\":\"1\",\"productType\":\"1\",\"currencyUnit\":\"CNY\",\"amount\":\"-1\",\"beforePaidSign\":\"1\",\"beforeDeadline\":\"2020-12-11 23:17:41\",\"createTime\":\"2020-10-16 14:58:49\",\"orderCode\":\"202010161458498283298\",\"payChannel\":\"9\",\"endTime\":\"2020-11-11 23:17:41\",\"centerPayService\":\"446\",\"gateway\":\"1\"}";
        OrderMessage refundOrderMsg = buildOrderMsg(cancelOrderMsg);
        PresentOrder presentOrderRequest = new PresentOrder();
        presentOrderRequest.setOrderCode(refundOrderMsg.getTradeCode());
        presentOrderRequest.setUid(refundOrderMsg.getUid());
        presentOrderRequest.setOrderType(EnumOrderTypeCode.YES.getCode());

        List<PresentOrder> presentOrderList = presentOrderService.queryOrderByParams(presentOrderRequest);
        for (PresentOrder refundPresentOrder : presentOrderList) {
            Map<String, Object> refundParams = buildRefundParams(refundOrderMsg, refundPresentOrder);
            RefundVipAsyncTask refundVipAsyncTask = new RefundVipAsyncTask(refundParams);
            boolean refunded = refundVipAsyncTask.execute();
            assertTrue(refunded);
        }

    }

    private OrderMessage buildOrderMsg(String orderMsg) {
        return JSON.parseObject(orderMsg, OrderMessage.class);
    }

    private Map<String, Object> buildRefundParams(OrderMessage message, PresentOrder presentOrder) {
        PresentConfig presentConfig = presentConfigService.queryConfigById(presentOrder.getPresentConfigId());
        Integer refundAmount = CalAmountUtils.calRefundAmount(message, presentOrder, presentConfig);
        Map<String, Object> params = Maps.newHashMap();
        params.put("orderCode", presentOrder.getPresentOrderCode());
        params.put("fee", String.valueOf(0));
        params.put("timeLength", String.valueOf(refundAmount));
        params.put("reason", "present refund order");
        params.put("cancelVip", String.valueOf(1));

        params.put("msgId", message.getMsgId());
        params.put("presentOrder", presentOrder);
        params.put("refundOrderCode", message.getOrderCode());

        params.put("tradeCode", presentOrder.getPresentTradeCode());
        params.put("uid", String.valueOf(presentOrder.getUid()));
        return params;
    }

}
