package com.iqiyi.vip.present;

import java.util.HashSet;

/**
 * <AUTHOR>
 * @version v 1.0 2018/8/17 14:48
 */
public class TestMain {

//    public static void main(String[] args) {
//
//    }


}
class Solution {
    public int lengthOfLongestSubstring(String s) {
        HashSet set = new HashSet();
        int n = s.length();
        int i=0, j = 0;
        int ans = 0;
        while (i < n && j <n) {
            if (!set.contains(s.charAt(j))) {
                set.add(s.charAt(j++));
                ans = Math.max(ans, j - i);
            } else {
                set.remove(s.charAt(i++));
            }

        }
        return ans;
    }
}