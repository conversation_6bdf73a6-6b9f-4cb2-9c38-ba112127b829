package com.iqiyi.vip.present;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.iqiyi.kiwi.utils.HttpClientConnection;
import com.iqiyi.kiwi.utils.JsonBinder;
import com.iqiyi.kiwi.utils.JsonResult;
import com.iqiyi.vip.present.utils.DateUtils;
import com.iqiyi.vip.present.utils.EncodeUtils;
import org.apache.commons.io.FileUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest
public class ServiceTest {

    @Value("${dopay.url:http://bj.i.vip.qiyi.domain/api/internal/free-pay/dopay.action}")
    private String doPayUrl;

    @Test
    public void testGetUids() throws Exception {
        Connection conn = DriverManager.getConnection("*******************************************************************************************************************************************************************************");
        int total = 0;
        for (int j = 0; j <= 99; j++) {
            String table = "present_order_" + String.format("%02d", j);
            ;
            PreparedStatement ps = conn.prepareStatement("SELECT count(1) FROM " + table
                + " WHERE  present_config_id = 50  and status = 4 and create_time > '2019-10-15 00:00:00' and create_time < '2019-10-16 00:00:00'");
            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                total += rs.getInt(1);
            }
            rs.close();
            ps.close();
        }
        System.out.println(total);

    }

    @Test
    public void testGetDays() throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Date begin = sdf.parse("3300-12-19 00:00:00");
        Date end = sdf.parse("2019-05-09 00:00:00");

        System.out.println(DateUtils.getDayInterval(end, begin));

    }

    /**
     * 网球会员退权益
     */
    @Test
    public void testRefundTenis() throws Exception {
        List<String> list = FileUtils.readLines(new File("d:/tennis_7.txt"), "UTF-8");
        for (String str : list) {
            Map<String, String> param = Maps.newHashMap();
            param.put("uid", str.split("\t")[0]);
            param.put("tradeCode", "201905131500_tenis_" + param.get("uid"));
            param.put("amount", String.valueOf("-" + str.split("\t")[1]));
            param.put("pid", "a618504709bda074");
            param.put("payType", String.valueOf(317));
            param.put("actCode", "return:b9b7fa043da7f825->a618504709bda074");
            param.put("sign", EncodeUtils.signMessage(param, "1df3ec6fa2"));
            this.doRefund(param);
        }
    }

    /**
     * tv网球会员退权益
     */
    @Test
    public void testRefundTvTenis() throws Exception {
        List<String> list = FileUtils.readLines(new File("d:/tennis_8.txt"), "UTF-8");
        for (String str : list) {
            Map<String, String> param = Maps.newHashMap();
            param.put("uid", str.split("\t")[0]);
            param.put("tradeCode", "201905131500_tv_tenis_" + param.get("uid"));
            param.put("amount", String.valueOf("-" + str.split("\t")[1]));
            param.put("pid", "b9b7fa043da7f825");
            param.put("payType", String.valueOf(316));
            param.put("actCode", "return:a618504709bda074->b9b7fa043da7f825");
            param.put("sign", EncodeUtils.signMessage(param, "1df3ec6fa2"));
            this.doRefund(param);
        }
    }

    private void doRefund(Map<String, String> param) {
        System.out.println(JSON.toJSONString(param));
        HttpClientConnection hcc = new HttpClientConnection(doPayUrl, HttpClientConnection.POST_METHOD);
        hcc.setReqParams(param);
        hcc.setSoTimeout(5000);
        boolean status = hcc.connect();
        if (status) {
            JsonResult result = JsonBinder.buildNonDefaultBinder().fromJson(hcc.getBody(), JsonResult.class);
            if (result == null || (!"A00000".equals(result.getCode()) && !"Q00348".equals(result.getCode()))) {
                System.out.println(JSON.toJSONString(result));
            }
            System.out.println((result != null ? result.getCode() : null));

        }
    }
}
