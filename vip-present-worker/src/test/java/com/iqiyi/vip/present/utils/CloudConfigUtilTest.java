package com.iqiyi.vip.present.utils;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Created at: 2020-11-27
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CloudConfigUtilTest {

    static {
        System.setProperty("spring.profiles.active", "dev");
    }

    @Test
    public void doTest() {
        boolean small = CloudConfigUtil.justPresentSportsSmall("9e408f2ec2c806d3");
        Assert.assertTrue(small);
    }
}
