package com.iqiyi.vip.present.controller;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import com.iqiyi.vip.present.apiresponse.BaseResponse;

/**
 * Created at: 2022-05-05
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class FixControllerTest {

    static {
        System.setProperty("spring.profiles.active", "dev");
    }

    @Resource
    FixController fixController;

    @Test
    public void reSendFromLog() throws Exception {
        String line = "[INFO ] [2022-05-05 18:00:02.431] [TID:6273a02245a57450fa2a72feb5ee25d6] [ConsumeMessageThread_9] [c.i.v.p.c.OrderFinishedConsumer:consumeMessage$original$pytun2O7:47] [rmq deal start] [topic:vip_trade_msg_order_finished] [msgId:AC11000400105BDA80BF18763E7C6C60] [message:{\"actCode\":\"JLSP_UNLOCKVIP\",\"aid\":\"null\",\"amount\":1,\"autoRenew\":\"null\",\"autoRenewProduct\":false,\"centerPayService\":\"\",\"centerPayType\":\"\",\"channel\":\"98b1d640429887af\",\"chargeType\":2,\"createTime\":1651744802000,\"endTime\":1651745702000,\"fr_version\":\"\",\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"gateway\":\"4639\",\"giftBatchNo\":\"202204132128140677603\",\"msgId\":\"AC11000400105BDA80BF18763E7C6C60\",\"msgtype\":7,\"orderCode\":\"20220505180002116913000995\",\"orderFee\":0,\"orderRealFee\":0,\"payTime\":1651744802000,\"payType\":305,\"pid\":\"950dd666bac348d3\",\"platform\":1301,\"platformCode\":\"b9670363558ecaa3\",\"productSubtype\":\"\",\"productType\":2,\"renewFlag\":\"0\",\"serviceCode\":\"lyksc7aq36aedndk\",\"settlementFee\":0,\"sourceType\":\"\",\"startTime\":1651744802000,\"status\":1,\"tradeCode\":\"nervi_8a2794278ed74e2fb63a9de8cf91f442\",\"type\":\"1\",\"uid\":1367802013}] [cost:0]";
        BaseResponse baseResponse = fixController.reSendFromLog(line);
        System.out.println(baseResponse);
    }
}
