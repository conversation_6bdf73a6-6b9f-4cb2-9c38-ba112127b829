package com.iqiyi.vip.present.task;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import com.iqiyi.vip.present.dao.PresentAsyncTaskDao;
import com.iqiyi.vip.present.model.PresentAsyncTask;
import com.iqiyi.vip.present.utils.DateUtils;
import com.iqiyi.vip.present.utils.UUidUtils;

import static org.junit.Assert.*;

/**
 * Created at: 2022-04-11
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class PresentTaskTest {

    static {
        System.setProperty("spring.profiles.active", "dev");
    }

    @Resource
    PresentAsyncTaskDao presentAsyncTaskDao;

    @Rollback
    @Test
    public void doTest() {
        PresentAsyncTask asyncTask = PresentAsyncTask.builder()
            .className(VipPresentTask.class.getSimpleName())
            .data("{}")
            .taskId(UUidUtils.getUUid())
            .build();
        boolean inserted = presentAsyncTaskDao.addAsyncTask(asyncTask);
        assertTrue(inserted);
        PresentAsyncTask storedAsyncTask = presentAsyncTaskDao.getAsyncTask(asyncTask.getId());
        assertNotNull(storedAsyncTask);
        boolean processing = presentAsyncTaskDao.makeTaskProcessing(asyncTask.getId());
        assertTrue(processing);
        boolean restore = presentAsyncTaskDao.restoreTaskForRetry(asyncTask.getId(), DateUtils.calculateTime(DateUtils.getCurrentTimestamp(), "5s"));
        assertTrue(restore);
        presentAsyncTaskDao.removeAsyncTask(asyncTask.getId());
    }
}
