package com.iqiyi.vip.present.consumer;

import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.nio.charset.StandardCharsets;
import java.util.Collections;

import static org.junit.Assert.*;

/**
 * Created at: 2021-01-19
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class OrderFinishedConsumerTest {

    static {
        System.setProperty("spring.profiles.active", "dev");
    }

    @Resource
    OrderFinishedConsumer orderFinishedConsumer;

    @Test
    public void goldPresentBasicVip() {
        //免费黄金送极速版
//        String orderMsgStr = "{\"payTime\":\"2021-01-19 16:02:20\",\"channel\":\"afbe8fd3d73448c9\",\"chargeType\":\"1\",\"pid\":\"8ea0996f0444a283\",\"type\":\"1\",\"businessCode\":\"lyksc7aq36aedndk\",\"payType\":\"407\",\"msgtype\":\"7\",\"productId\":\"11847\",\"tradeNo\":\"202101191602198666726\",\"fr_version\":\"\",\"tradeCode\":\"202101191602092622177_8ea0996f0444a283\",\"settlementFee\":\"0\",\"orderRealFee\":\"0\",\"centerPayType\":\"\",\"actCode\":\"vip_present\",\"sourceType\":\"\",\"fc\":\"a9e4fec171bb4c74\",\"aid\":\"null\",\"platformCode\":\"bb136ff4276771f3\",\"productSubtype\":\"1\",\"status\":\"1\",\"serviceCode\":\"lyksc7aq36aedndk\",\"orderFee\":\"0\",\"couponSettlementFee\":\"\",\"platform\":\"10\",\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"uid\":\"**********\",\"businessValues\":\"null\",\"autoRenew\":\"null\",\"startTime\":\"2021-01-19 16:02:20\",\"thirdUid\":\"\",\"renewFlag\":\"1\",\"productType\":\"1\",\"currencyUnit\":\"CNY\",\"amount\":\"31\",\"beforePaidSign\":\"1\",\"beforeDeadline\":\"2021-01-18 13:27:22\",\"createTime\":\"2021-01-19 16:02:20\",\"payTypeCategory\":\"1\",\"userIp\":\"*************\",\"orderCode\":\"202101191602198666726\",\"payChannel\":\"23\",\"endTime\":\"2021-02-19 16:02:20\",\"centerPayService\":\"\",\"gateway\":\"1\"}";
        //付费黄金送极速版
        String orderMsgStr = "{\"payTime\":\"2021-01-19 17:00:33\",\"channel\":\"afbe8fd3d73448c9\",\"chargeType\":\"1\",\"pid\":\"a0226bd958843452\",\"phoneNum\":\"***********\",\"type\":\"1\",\"deviceId\":\"3E6089B4-9E27-433A-BE71-C43016212B1D\",\"businessCode\":\"lyksc7aq36aedndk\",\"payType\":\"304\",\"msgtype\":\"7\",\"productId\":\"4\",\"tradeNo\":\"202012191631123118877\",\"fr_version\":\"d=3E6089B4-9E27-433A-BE71-C43016212B1D&k=8e48946f144759d86a50075555fd5862&v=11.11.5&aid=&test=&login=&dfp=87a6a39ced8206450fb25d6367b0135300e1de65eaa4a0c0f031036a1439d72641\",\"tradeCode\":\"170000935410698\",\"settlementFee\":\"1900\",\"orderRealFee\":\"1900\",\"centerPayType\":\"APPLEIAPDUT\",\"actCode\":\"iqiyi_vip_iphone_video_autorenew\",\"expireTime\":\"2021-02-19 18:00:24.0\",\"sourceType\":\"\",\"fc\":\"a9e4fec171bb4c74\",\"aid\":\"null\",\"platformCode\":\"bb35a104d95490f6\",\"productSubtype\":\"1\",\"status\":\"1\",\"serviceCode\":\"lyksc7aq36aedndk\",\"orderFee\":\"1900\",\"couponSettlementFee\":\"0\",\"platform\":\"12\",\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"uid\":\"**********\",\"centerCode\":\"2021011961232001019\",\"autoRenew\":\"2\",\"startTime\":\"2021-01-19 18:00:31\",\"thirdUid\":\"\",\"renewFlag\":\"1\",\"productType\":\"1\",\"currencyUnit\":\"CNY\",\"amount\":\"1\",\"beforePaidSign\":\"1\",\"beforeDeadline\":\"2021-01-19 18:00:31\",\"createTime\":\"2021-01-19 17:00:33\",\"payTypeCategory\":\"1\",\"userIp\":\"***************\",\"orderCode\":\"202101191700326415659\",\"payChannel\":\"9\",\"endTime\":\"2021-02-19 18:00:31\",\"centerPayService\":\"446\",\"gateway\":\"1\"}";
        MessageExt messageExt = new MessageExt();
        messageExt.setMsgId("0A0D2EAA7FAA61A4A8B36058D04B8C71");
        messageExt.setBody(orderMsgStr.getBytes(StandardCharsets.UTF_8));
        ConsumeConcurrentlyStatus consumeStatus = orderFinishedConsumer.consumeMessage(Collections.singletonList(messageExt), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, consumeStatus);
    }

    @Test
    public void youthPresentBasicVip() {
        //付费学生送极速版会员
//        String orderMsgStr = "{\"payTime\":\"2021-01-19 17:05:23\",\"channel\":\"afbe8fd3d73448c9\",\"chargeType\":\"1\",\"pid\":\"a90e5323a93c1554\",\"phoneNum\":\"***********\",\"type\":\"1\",\"businessCode\":\"lyksc7aq36aedndk\",\"payType\":\"379\",\"msgtype\":\"7\",\"productId\":\"11399\",\"tradeNo\":\"202101191705100227088\",\"fr_version\":\"\",\"tradeCode\":\"4200000847202101194541898114\",\"settlementFee\":\"1200\",\"orderRealFee\":\"1200\",\"centerPayType\":\"WECHATH5DUTV4\",\"actCode\":\"main_9600201106202727880050,sub_9600201106202727880742\",\"sourceType\":\"\",\"fc\":\"a03eda98e3d5aab6\",\"aid\":\"null\",\"platformCode\":\"97ae2982356f69d8\",\"productSubtype\":\"16\",\"status\":\"1\",\"serviceCode\":\"lyksc7aq36aedndk\",\"orderFee\":\"1200\",\"couponSettlementFee\":\"0\",\"platform\":\"19\",\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"uid\":\"**********\",\"businessValues\":\"0\",\"centerCode\":\"2021011961510001091\",\"autoRenew\":\"1\",\"startTime\":\"2021-01-19 17:05:23\",\"thirdUid\":\"oveuTjjTsxLKt_oHBZXZPkS-ietQ\",\"renewFlag\":\"0\",\"productType\":\"1\",\"currencyUnit\":\"CNY\",\"amount\":\"1\",\"beforePaidSign\":\"0\",\"payAccount\":\"oveuTjjTsxLKt_oHBZXZPkS-ietQ\",\"createTime\":\"2021-01-19 17:05:11\",\"payTypeCategory\":\"1\",\"userIp\":\"*************\",\"orderCode\":\"202101191705100227088\",\"payChannel\":\"2\",\"endTime\":\"2021-02-19 17:05:23\",\"centerPayService\":\"469\",\"gateway\":\"8311\"}";
        //免费学生不送极速版
        String orderMsgStr = "{\"payTime\":\"2021-01-19 16:20:09\",\"channel\":\"afbe8fd3d73448c9\",\"chargeType\":\"2\",\"pid\":\"919a2ea53ffa9779\",\"type\":\"1\",\"businessCode\":\"lyksc7aq36aedndk\",\"payType\":\"381\",\"msgtype\":\"7\",\"productId\":\"11496\",\"tradeNo\":\"202101191620094294252\",\"fr_version\":\"\",\"tradeCode\":\"202101191620078038923_919a2ea53ffa9779\",\"settlementFee\":\"0\",\"orderRealFee\":\"0\",\"centerPayType\":\"\",\"actCode\":\"vip_present\",\"sourceType\":\"\",\"fc\":\"a9e4fec171bb4c74\",\"aid\":\"null\",\"platformCode\":\"b6c13e26323c537d\",\"productSubtype\":\"16\",\"status\":\"1\",\"serviceCode\":\"lyksc7aq36aedndk\",\"orderFee\":\"0\",\"couponSettlementFee\":\"\",\"platform\":\"1\",\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"uid\":\"**********\",\"businessValues\":\"null\",\"autoRenew\":\"null\",\"startTime\":\"2021-02-08 10:20:22\",\"thirdUid\":\"\",\"renewFlag\":\"1\",\"productType\":\"1\",\"currencyUnit\":\"CNY\",\"amount\":\"3\",\"beforePaidSign\":\"1\",\"beforeDeadline\":\"2021-02-08 10:20:22\",\"createTime\":\"2021-01-19 16:20:09\",\"payTypeCategory\":\"1\",\"userIp\":\"*************\",\"orderCode\":\"202101191620094294252\",\"payChannel\":\"23\",\"endTime\":\"2021-02-11 10:20:22\",\"centerPayService\":\"\",\"gateway\":\"1\"}";
        MessageExt messageExt = new MessageExt();
        messageExt.setMsgId("0A6E5C0D6DBE44CF2E7B6033D34F5928");
        messageExt.setBody(orderMsgStr.getBytes(StandardCharsets.UTF_8));
        ConsumeConcurrentlyStatus consumeStatus = orderFinishedConsumer.consumeMessage(Collections.singletonList(messageExt), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, consumeStatus);
    }
}