package com.iqiyi.vip.present.service;

import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.utils.PresentConfigCacheComponent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import javax.annotation.Resource;

import java.util.Date;

import static org.junit.Assert.*;

/**
 * Created at: 2021-01-19
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class PresentRuleComponentTest {

    static {
        System.setProperty("spring.profiles.active", "dev");
    }

    @Resource
    PresentConfigService presentConfigService;
    @Resource
    PresentConfigCacheComponent presentConfigCacheComponent;

    @Test
    public void presentConfigTest() {
        assertNotNull(PresentConfigCacheComponent.configMap);
        assertFalse(PresentConfigCacheComponent.configMap.isEmpty());

        assertNotNull(PresentConfigCacheComponent.conditionMap);
        assertFalse(PresentConfigCacheComponent.conditionMap.isEmpty());

        assertNotNull(PresentConfigCacheComponent.performMap);
        assertFalse(PresentConfigCacheComponent.performMap.isEmpty());

        assertNotNull(PresentConfigCacheComponent.diamondPids);
        assertFalse(PresentConfigCacheComponent.diamondPids.isEmpty());

        int goldPresentConfigSize = PresentConfigCacheComponent.configMap.get("1").size();
        PresentConfig presentConfig = buildPresentConfig();
        int saved = presentConfigService.save(presentConfig);
        assertTrue(saved > 0);
        presentConfigCacheComponent.getPresentConfig();
        assertEquals(goldPresentConfigSize + 1, PresentConfigCacheComponent.configMap.get("1").size());
    }

    private PresentConfig buildPresentConfig() {
        PresentConfig presentConfig = new PresentConfig();
        presentConfig.setBvipType("1");
        presentConfig.setPvipType("56");
        presentConfig.setPresentCode("aaaaaa");
        presentConfig.setRemark("买黄金送极速版会员");
        presentConfig.setStatus(1);
        presentConfig.setHandlerStatus(1);
        presentConfig.setGrouping("a");
        presentConfig.setUpdateTime(new Date());
        presentConfig.setCreateTime(new Date());
        presentConfig.setPayType(424);
        presentConfig.setConditionIds("1");
        presentConfig.setCalAmountType(0);
        presentConfig.setStartTime(new Date());
        return presentConfig;
    }
}
