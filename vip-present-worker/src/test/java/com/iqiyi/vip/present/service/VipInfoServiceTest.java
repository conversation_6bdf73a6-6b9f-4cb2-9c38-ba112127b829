package com.iqiyi.vip.present.service;

import com.iqiyi.vip.present.consts.VipTypeEnum;
import com.iqiyi.vip.present.out.VipUser;
import org.apache.commons.lang.RandomStringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.Assert.*;

/**
 * Created at: 2021-01-19
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class VipInfoServiceTest {

    static {
        System.setProperty("spring.profiles.active", "dev");
    }

    @Resource
    VipInfoService vipInfoService;

    @Test
    public void getVipInfo() throws Exception{
        long uid = 1480407671L;
        int basicVip = VipTypeEnum.basic.getVipType();
        VipUser vipInfo = vipInfoService.getVipInfo(uid, basicVip + "", RandomStringUtils.randomAlphanumeric(15));
        assertNotNull(vipInfo);
        assertEquals("1", vipInfo.getStatus());
    }
}
