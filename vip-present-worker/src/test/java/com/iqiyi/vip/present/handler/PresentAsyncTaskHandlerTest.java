package com.iqiyi.vip.present.handler;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Created at: 2022-04-15
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class PresentAsyncTaskHandlerTest {

    static {
        System.setProperty("spring.profiles.active", "dev");
    }

    @Resource
    CanalEventHandlerRegistry canalEventHandlerRegistry;

    @Test
    public void doTest() {
        CanalEventHandler canalEventHandler = canalEventHandlerRegistry.getHandler("present_async_task");
        String event = "{\"rowBefore\":{\"timerrun_at\":1650006150000,\"exec_count\":0,\"update_time\":1650006150000,\"classname\":\"VipPresentTask\",\"data\":\"{}\",\"create_time\":1650006150000,\"task_id\":\"b0a36896e2b745f688fbf7ccb373a41b\",\"id\":4,\"priority\":1,\"inqueue\":1},\"eventType\":\"UPDATE\",\"schemaName\":\"vip-present\",\"rowAfter\":{\"timerrun_at\":1650006155000,\"exec_count\":1,\"update_time\":1650006150000,\"classname\":\"VipPresentTask\",\"data\":\"{}\",\"create_time\":1650006150000,\"task_id\":\"b0a36896e2b745f688fbf7ccb373a41b\",\"id\":4,\"priority\":1,\"inqueue\":0},\"tableName\":\"present_async_task\",\"timestamp\":1650006150000}";
        canalEventHandler.handCanalEvent(event);
    }
}
