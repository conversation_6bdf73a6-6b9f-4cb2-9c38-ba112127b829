package com.iqiyi.vip.present.consumer;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.iqiyi.vip.present.consts.EnumOrderStatusCode;
import com.iqiyi.vip.present.data.VipRightsGrantMessage;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.out.PresentVipRequest;
import com.iqiyi.vip.present.out.PresentVipResponse;
import com.iqiyi.vip.present.out.apireq.PresentApiReq;
import com.iqiyi.vip.present.out.apireq.PresentApiReqEngine;
import com.iqiyi.vip.present.service.PresentOrderService;
import com.iqiyi.vip.present.service.RestTemplateService;
import com.iqiyi.vip.present.task.PresentVipAsyncTask;
import com.iqiyi.vip.present.utils.ApplicationContextUtil;
import com.iqiyi.vip.repository.ClusterAsyncTaskManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class VipRightsGrantConsumer implements MessageListenerConcurrently {

    @Resource
    private RestTemplateService restTemplateService;
    @Resource
    private PresentOrderService presentOrderService;
    @Resource
    private ClusterAsyncTaskManager clusterAsyncTaskManager;
    public static final String VIPTYPE_VOD_SINGLE = "single";
    private static final int MAX_RECONSUME_TIMES_BEFORE_FALLBACK = 14;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt messageExt : msgs) {
            String msgBody = new String(messageExt.getBody(), StandardCharsets.UTF_8);
            VipRightsGrantMessage vipRightsGrantMessage = JSON.parseObject(msgBody, VipRightsGrantMessage.class);
            log.info("[VipRightsGrantConsumer deal start] [topic:{}] [msgId:{}] [message:{}]", messageExt.getTopic(), messageExt.getMsgId(), vipRightsGrantMessage);
            try {
                processVipRightsGrantMessage(vipRightsGrantMessage);
            }catch(Exception e){
                if(shouldFallbackToAsyncTask(messageExt, vipRightsGrantMessage)){
                    clusterAsyncTaskManager.insertTask(new PresentVipAsyncTask(vipRightsGrantMessage.getRequest(), vipRightsGrantMessage.getPresentOrder()));
                }else{
                    log.error("[VipRightsGrantConsumer deal error] [topic:{}] [msgId:{}] [message:{}] [times:{}]", messageExt.getTopic(), messageExt.getMsgId(), vipRightsGrantMessage,messageExt.getReconsumeTimes(),e);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                }
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private void processVipRightsGrantMessage(VipRightsGrantMessage vipRightsGrantMessage){
        PresentVipRequest presentVipRequest = vipRightsGrantMessage.getRequest();
        PresentOrder presentOrder = vipRightsGrantMessage.getPresentOrder();
        if (presentVipRequest.getAmount() == 0) {
            log.info("VipRightsGrantConsumer ignored, amount为0，presentVipRequest: {}, presentOrder: {}", JSONObject.toJSONString(presentVipRequest), JSONObject.toJSONString(presentOrder));
            return;
        }

        int oldStatus = presentOrder.getStatus();
        Environment environment = (Environment) ApplicationContextUtil.getBean(Environment.class);
        int presentType = NumberUtils.toInt(presentOrder.getPresentType());
        PresentApiReq presentApiReq = PresentApiReqEngine.build(presentType);
        String url = presentApiReq.presentUrl(environment);
        Boolean lb = presentApiReq.ispPresentLb(environment);
        Map reqMap = presentApiReq.presentParams(presentVipRequest, environment, presentOrder);

        PresentVipResponse presentVipResponse;
        log.info("VipRightsGrantConsumer execute [msgId:{}][http-presentVipRequest:{}]", presentOrder.getMsgId(), JSONObject.toJSONString(reqMap));
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            presentVipResponse = restTemplateService.postForObject(url, reqMap, PresentVipResponse.class,lb,presentOrder.getIsPressureTest());
        } catch (Exception e) {
            log.error("VipRightsGrantConsumer [msgId:{}][订单下单服务异常，重试][url:{}][reqMap:{}]", presentOrder.getMsgId(), url,JSONObject.toJSONString(reqMap), e);
            throw new RuntimeException("订单下单服务异常,重试 msgId:"+presentOrder.getMsgId()+"suerId:"+presentOrder.getUid());
        }finally {
            stopWatch.stop();
        }

        if (presentVipResponse != null && presentVipResponse.isUserNotFound()) {
            log.info("VipRightsGrantConsumer [msgId:{}][用户不存在][reqMap:{}] [response:{}]", presentOrder.getMsgId(),
                    JSONObject.toJSONString(reqMap), JSON.toJSONString(presentVipResponse));
            return;
        }

        if (presentVipResponse == null || !presentVipResponse.isSuccess()) {
            log.error("VipRightsGrantConsumer [msgId:{}][订单下单服务异常，重试][reqMap:{}] [response:{}]", presentOrder.getMsgId(), JSONObject
                    .toJSONString(reqMap), presentVipResponse == null ? "" : JSON.toJSONString(presentVipResponse));
            throw new RuntimeException("订单下单服务异常,重试 msgId:"+presentOrder.getMsgId()+"suerId:"+presentOrder.getUid());
        }
        log.info("VipRightsGrantConsumer execute [msgId:{}][http-presentVipRequest:{}] [response:{}][costTime:{}ms]", presentOrder.getMsgId(), JSONObject
                .toJSONString(reqMap), JSONObject.toJSONString(presentVipResponse), stopWatch.getTime());

        presentOrder.setStatus(EnumOrderStatusCode.ALREDY_PRESENT.getCode());
        if (presentVipResponse.getData() != null) {
            String presentOrderCode = presentVipResponse.getData().getOrderCode();
            presentOrder.setPresentOrderCode(presentOrderCode);
        }
        log.info("VipRightsGrantConsumer execute [msgId:{}][修改数据库presentOrder:{}]", presentOrder.getMsgId(), JSONObject.toJSONString(presentOrder));

        presentOrderService.updateOrderStatus(presentOrder, oldStatus);
        log.info("VipRightsGrantConsumer execute [msgId:{}][修改数据库成功presentOrder:{}]", presentOrder.getMsgId(), JSONObject.toJSONString(presentOrder));
    }

    private boolean shouldFallbackToAsyncTask(MessageExt messageExt, VipRightsGrantMessage message) {
        return messageExt.getReconsumeTimes() > MAX_RECONSUME_TIMES_BEFORE_FALLBACK
                && VIPTYPE_VOD_SINGLE.equalsIgnoreCase(message.getPresentOrder().getBuyType());
    }
}
