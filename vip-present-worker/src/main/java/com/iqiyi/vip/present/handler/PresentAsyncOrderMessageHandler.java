package com.iqiyi.vip.present.handler;

import com.alibaba.fastjson.JSON;
import com.iqiyi.vip.present.consumer.impl.OrderPresentConsumer;
import com.iqiyi.vip.present.dao.PresentAsyncOrderMessageDao;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.model.PresentAsyncOrderMessage;
import com.iqiyi.vip.present.task.PresentAsyncOrderMessageTask;
import com.iqiyi.vip.repository.ClusterAsyncTaskManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @date 2024/7/25
 * @apiNote
 */
@Service
@Slf4j
public class PresentAsyncOrderMessageHandler {

    @Resource
    private PresentAsyncOrderMessageDao presentAsyncOrderMessageDao;
    @Resource
    private ClusterAsyncTaskManager clusterAsyncTaskManager;
    @Resource
    private RedissonClient redissonClient;

    @Resource
    private OrderPresentConsumer orderPresentConsumer;

    @Value("${partner.order.delay.seconds}")
    private Integer PARTNER_ORDER_DELAY;

    @Transactional(rollbackFor = Exception.class)
    public void saveAsyncOrderMessage(OrderMessage message) {
        presentAsyncOrderMessageDao.insert(PresentAsyncOrderMessage.build(message));
        clusterAsyncTaskManager.insertTask(new PresentAsyncOrderMessageTask(message.getUid(), Integer.parseInt(message.getProductSubtype())), DateUtils.addSeconds(new Date(), PARTNER_ORDER_DELAY));
    }

    public boolean processAsyncOrderMessages(Long uid, Integer productSubType) {
        //加分布式锁
        RLock rLock = redissonClient.getLock("process_async_order:" + uid);
        try {
            if (!rLock.tryLock(200, 3000, TimeUnit.MILLISECONDS)) {
                return false;
            }
            log.info("processAsyncOrderMessages start uid:{}, productSubType:{}", uid, productSubType);
            List<PresentAsyncOrderMessage> messages = presentAsyncOrderMessageDao.getByUidAndProductSubType(uid, productSubType);
            log.info("processAsyncOrderMessages start uid:{}, productSubType:{}, messages:{}", uid, productSubType, JSON.toJSONString(messages));
            if (CollectionUtils.isEmpty(messages)) {
                return true;
            }
            messages.sort(Comparator.comparing(PresentAsyncOrderMessage::getStartTime));
            for (PresentAsyncOrderMessage message : messages) {
                //process
                orderPresentConsumer.doProcess(JSON.parseObject(message.getOrderMessage(), OrderMessage.class));

                //删除数据
                presentAsyncOrderMessageDao.delete(message.getOrderCode());
                // sleep
                TimeUnit.SECONDS.sleep(PARTNER_ORDER_DELAY);
                log.info("processAsyncOrderMessages processing uid:{}, productSubType:{}, message:{} 处理完成", uid, productSubType, JSON.toJSONString(message));
            }
            log.info("processAsyncOrderMessages processing uid:{}, productSubType:{}, finish", uid, productSubType);
            return true;
        } catch (Exception e) {
            log.error("processAsyncOrderMessages uid:{}, productSubType:{} exception:", uid, productSubType, e);
            return false;
        } finally {
            if (rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }
}
