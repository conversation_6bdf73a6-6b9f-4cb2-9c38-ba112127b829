package com.iqiyi.vip.present.consumer.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.iqiyi.kiwi.utils.DateHelper;
import com.iqiyi.vip.present.consts.*;
import com.iqiyi.vip.present.consumer.BaseMessageConsumer;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.model.ProcessResult;
import com.iqiyi.vip.present.model.SupplyPresentRecord;
import com.iqiyi.vip.present.service.PresentConfigService;
import com.iqiyi.vip.present.service.PresentOrderService;
import com.iqiyi.vip.present.service.PresentRulePerformComponent;
import com.iqiyi.vip.present.service.SupplyPresentRecordService;
import com.iqiyi.vip.present.task.RefundVipAsyncTask;
import com.iqiyi.vip.present.utils.*;
import com.iqiyi.vip.repository.ClusterAsyncTaskManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OrderCancelConsumer extends BaseMessageConsumer {

    @Autowired
    private PresentConfigService presentConfigService;

    @Autowired
    private PresentOrderService presentOrderService;
    @Resource
    private SupplyPresentRecordService supplyPresentRecordService;
    @Resource
    private ClusterAsyncTaskManager clusterAsyncTaskManager;
    @Resource
    private PresentRulePerformComponent presentRulePerformComponent;

    @Override
    public boolean doMatch(OrderMessage message) {
        Integer status = message.getStatus();
        return PresentConstants.REFUND_STATUS_6.equals(status) || PresentConstants.REFUND_STATUS_12.equals(status);
    }

    @Override
    public ProcessResult doDeal(OrderMessage message) throws Exception {
        if (StringUtils.isBlank(message.getTradeCode())) {
            return ProcessResult.noNeedPresent("订单交易码为空，无需赠送");
        }
        PresentOrder presentOrderRequest = new PresentOrder();
        presentOrderRequest.setOrderCode(message.getTradeCode());
        presentOrderRequest.setUid(message.getUid());
        //presentOrderRequest.setOrderType(EnumOrderTypeCode.YES.getCode());
        log.info("[message:{}] [presentConfigList:{}]", message, JSONObject.toJSONString(presentOrderRequest));
        List<PresentOrder> presentOrderList = presentOrderService.queryOrderByParams(presentOrderRequest);
        // TODO 无对应关系，查询是否有对应的基础会员单，有则对基础会员进行退单，判断是主站还是tv的会员退单，并且该退单不是增单
        //基础会员补增单判断
        // 判断是否已补增，如果已补增则走赠送逻辑，否则暂时不赠送，判断是tv还是主站赠送,TODO 买赠增单过滤掉该逻辑

        // 处理赠送订单取消逻辑
        ProcessResult presentResult = processPresentCancel(message, presentOrderRequest, presentOrderList);

        //判断当前买赠里面有没有基础会员，和规则里面有没有送基础
        //TODO
        ProcessResult supplyResult = processSupplyCancel(message, presentOrderList);

        if ((presentResult != null && presentResult.isSuccess()) ||
            (supplyResult != null && supplyResult.isSuccess())) {
            return ProcessResult.acceptDataPresenting("接收数据成功，处理中");
        }

        return ProcessResult.noNeedPresent("没有需要处理的订单");
    }

    private ProcessResult processSupplyCancel(OrderMessage message, List<PresentOrder> presentOrderList) throws Exception {
        if (PresentConstants.ACTCODE.equals(message.getActCode())) {
            return ProcessResult.noNeedPresent("无需处理买赠单");
        }

        log.info("processSupplyCancel start,presentOrderList:{},message:{}", presentOrderList, message);
        if (!PresentConstants.tv_vip_types.contains(message.getProductSubtype()) && !PresentConstants.main_vip_types.contains(message.getProductSubtype())) {
            log.info("退单 非主站或tv会员，无需走基础会员赠送:{}", JacksonUtils.toJsonString(message));
            return ProcessResult.noNeedPresent("非主站或tv会员，无需走基础会员赠送");
        }

        List<Integer> sources = Lists.newArrayList();
        List<PresentConfig> configList = PresentConfigCacheComponent.configMap.get(message.getProductSubtype());
        if (CollectionUtils.isEmpty(configList)) {
            return ProcessResult.noNeedPresent("配置列表为空，无需赠送");
        }
        configList = configList.stream().filter(p -> p.checkValid(new Date())).collect(Collectors.toList());
        log.info("PresentConfig result,configList:{}", configList);

        //处理主站基础会员
        if (presentOrderList.stream().noneMatch(t -> String.valueOf(PresentConstants.BASIC_VIP_TYPE).equals(t.getPresentType()))
                && configList.stream().anyMatch(t -> t.getPvipType().equals(String.valueOf(PresentConstants.BASIC_VIP_TYPE)) && presentRulePerformComponent.checkRulePerform(message, t) != null)) {
            //赠送主站基础
            sources.add(SupplySourceEnum.SUPPLY_MAIN_BASIC_VIP.getType());
        }
        //处理tv基础会员
        if (presentOrderList.stream().noneMatch(t -> String.valueOf(PresentConstants.BASIC_TV_VIP_TYPE).equals(t.getPresentType())) &&
                configList.stream().anyMatch(t -> t.getPvipType().equals(String.valueOf(PresentConstants.BASIC_TV_VIP_TYPE)) && presentRulePerformComponent.checkRulePerform(message, t) != null)) {
            //赠送tv基础
            sources.add(SupplySourceEnum.SUPPLY_TV_BASIC_VIP.getType());
        }
        log.info("sources result,sources:{},uid:{}", sources, message.getUid());

//        Integer source = PresentConstants.main_vip_types.contains(message.getProductSubtype()) ?
//                SupplySourceEnum.SUPPLY_MAIN_BASIC_VIP.getType() : PresentConstants.tv_vip_types.contains(message.getProductSubtype())
//                ? SupplySourceEnum.SUPPLY_TV_BASIC_VIP.getType() : SupplySourceEnum.OTHER.getType();
        List<SupplyPresentRecord> supplyPresentRecords = supplyPresentRecordService.selectByUid(SupplyPresentRecord.builder().uid(message.getUid()).build());
        log.info("supplyPresentRecords return result:{},message:{}", JacksonUtils.toJsonString(supplyPresentRecords), JacksonUtils.toJsonString(message));
        if (CollectionUtils.isEmpty(supplyPresentRecords)) {
            return ProcessResult.noNeedPresent("补增记录为空，无需赠送");
        }

        for (Integer source : sources) {
            SupplyPresentRecord supplyPresentRecord = supplyPresentRecords.stream().filter(t -> t.getSource() == source && StringUtils.isNotBlank(t.getOrderCode())).findFirst().orElse(null);
            if (null == supplyPresentRecord) {//该情况不正常，需要报警
                log.info("该单未补增基础会员，不退单基础会员:{}", JacksonUtils.toJsonString(message));
                continue;
            }
            PresentOrder presentOrder = createBasicPresentOrder(supplyPresentRecord, message);
            //进行退单
            Map<String, Object> refundBasicTaskParam = createBasicParam(supplyPresentRecord, presentOrder, message);
            //更新订单记录及插入退款任务
            //退单量较少，暂不考虑MQ
            presentOrderService.insertVipOrderPresent(presentOrder);
            clusterAsyncTaskManager.insertTask(new RefundVipAsyncTask(refundBasicTaskParam));
        }

        // 正常处理完成，返回成功
        return ProcessResult.acceptDataPresenting("接收数据成功，处理中");
    }

    private ProcessResult processPresentCancel(OrderMessage message, PresentOrder presentOrderRequest, List<PresentOrder> presentOrderList) {
        //转增特殊处理
        List<PresentOrder> presentOrderListFilter = TypeOfOrderEnum.isVipRightDonation(message.getType()) ? presentOrderList : presentOrderList.stream().filter(t -> t.getOrderType().equals(EnumOrderTypeCode.YES.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(presentOrderListFilter)) {
            log.info("没有需要退的订单[presentOrderRequest:{}][presentOrderList:{}]", JSONObject.toJSONString(presentOrderRequest),
                    JSONObject.toJSONString(presentOrderListFilter));
            return ProcessResult.noNeedPresent("没有需要退的订单，无需赠送");
        }
        log.info("[presentOrderList:{}][presentOrderListFilter:{}]", JSONObject.toJSONString(presentOrderListFilter),
                JSONObject.toJSONString(presentOrderListFilter));

        List<PresentOrder> refundSuccessOrders = Lists.newArrayList();
        List<Map<String, Object>> refundTaskParams = Lists.newArrayList();
        for (PresentOrder presentOrder : presentOrderListFilter) {
            // 未领取的直接更新为退款成功
            if (EnumOrderStatusCode.NOT_RECEIVE.getCode().equals(presentOrder.getStatus())) {
                presentOrder.setStatus(EnumOrderStatusCode.REFUND_SUCCESS.getCode());
                presentOrder.setOrderType(EnumOrderTypeCode.NOT.getCode());
                refundSuccessOrders.add(presentOrder);
                continue;
            }

            //对外合作退款订单
            if (StringUtils.isBlank(presentOrder.getPresentOrderCode())) {
                // continue;
                log.info("[extCancel][partnerOrderCancel:{}] ", message);
                //历史原因，合作方该类单退单会导致合作方客诉，合作方默认不退
                //extCancel(presentOrder, message);
                continue;
            }

            // 其他情况走退款任务
            Map<String, Object> refundTaskParam = createParam(presentOrder, message);
            refundTaskParams.add(refundTaskParam);
        }
        //写入订单记录及插入退款任务
        presentOrderService.updateOrderPresenRefundtAndTask(refundSuccessOrders, refundTaskParams);

        return ProcessResult.acceptDataPresenting("接收数据成功，处理中");
    }

    private void extCancel(PresentOrder presentOrder, OrderMessage message) {
        presentOrderService.updateOrderPresenRefundtAndTask(presentOrder, buidPartnerParam(presentOrder, message));

    }

    private Map<String, String> buidPartnerParam(PresentOrder presentOrder, OrderMessage message) {
        //异步保证，必须退成功，之后保存订单状态为退款
        Date startTime = message.getStartTime();
        Date endTime = message.getEndTime();
        Integer amount = DateHelper.getDayInterval(startTime, endTime); // end-start<0,负值
        PresentConfig presentConfig = presentConfigService.queryConfigById(presentOrder.getPresentConfigId());
        Map<String, String> param = Maps.newHashMap();
        param.put("uid", String.valueOf(message.getUid()));
        param.put("tradeCode", EncodeUtils.MD5(message.getTradeCode() + "_" + presentConfig.getPresentCode(), "UTF-8"));
        param.put("amount", String.valueOf(amount));
        param.put("pid", presentConfig.getPresentCode());
        param.put("payType", String.valueOf(presentConfig.getPayType()));
        param.put("originalOrderCode", message.getOrderCode());
        param.put("actCode", PresentConstants.ACTCODE);
        return param;
    }

    private Map<String, Object> createParam(PresentOrder presentOrder, OrderMessage message) {
        // 计算退款时长
        PresentConfig presentConfig = presentConfigService.queryConfigById(presentOrder.getPresentConfigId());
        Integer refundAmount = CalAmountUtils.calRefundAmount(message, presentOrder, presentConfig);
        Map<String, Object> params = Maps.newHashMap();
        if (presentConfig.getPresentCode().equals("ssports")) {
            params.put("orderCode", presentOrder.getPresentOrderCode());
            params.put("fee", String.valueOf(0));
            params.put("timeLength", String.valueOf(refundAmount));
            params.put("reason", "present refund order");
            params.put("cancelVip", String.valueOf(1));
            params.put("msgId", message.getMsgId());
            params.put("presentOrder", presentOrder);
            params.put("refundOrderCode", message.getOrderCode());
            params.put("tradeCode", presentOrder.getPresentTradeCode());
            params.put("uid", String.valueOf(presentOrder.getUid()));
        } else {
            params.put("orderCode", presentOrder.getPresentOrderCode());
            params.put("fee", String.valueOf(0));
            params.put("amount", String.valueOf(refundAmount));
            params.put("refundReason", "present refund order");
            params.put("applicant", "vip-present");
            params.put("skipAudit", "1");
            params.put("idempotentCode", message.getOrderCode());
            params.put("msgId", message.getMsgId());
            params.put("presentOrder", presentOrder);
        }
        return params;
    }

    private Map<String, Object> createBasicParam(SupplyPresentRecord supplyPresentRecord, PresentOrder presentOrder, OrderMessage message) {
        // 计算退款时长
        Integer refundAmount = CalAmountUtils.calRefundBasicAmount(message);
        Map<String, Object> params = Maps.newHashMap();
        params.put("orderCode", supplyPresentRecord.getOrderCode());
        params.put("fee", String.valueOf(0));
        params.put("amount", String.valueOf(refundAmount));
        params.put("refundReason", "present refund order");
        params.put("applicant", "vip-present");
        params.put("skipAudit", "1");
        params.put("idempotentCode", message.getOrderCode());
        params.put("msgId", message.getMsgId());
        params.put("presentOrder", presentOrder);

        return params;
    }

    private PresentOrder createBasicPresentOrder(SupplyPresentRecord supplyPresentRecord, OrderMessage message) {
        PresentOrder presentOrder = new PresentOrder();
        //presentOrder.setDeadlineEndTime(DateUtils.getDateBefore(message.(), Integer.valueOf(supplyPresentRecord.getAmount())));
        Integer refundAmount = CalAmountUtils.calRefundBasicAmount(message);
        presentOrder.setUid(supplyPresentRecord.getUid());
        presentOrder.setMsgId(message.getMsgId());
        presentOrder.setOrderCode(UUidUtils.getUUid());//补增单 无原单号
        presentOrder.setPresentOrderCode(supplyPresentRecord.getOrderCode());//补增单生成的订单号
        presentOrder.setPresentTradeCode(supplyPresentRecord.getTradeCode() + "_" + message.getOrderCode() + "_" + supplyPresentRecord.getVipType());//补增单的交易号
        presentOrder.setRefundOrderCode(message.getOrderCode());//订单消息过来的订单号
        presentOrder.setBuyType(message.getProductSubtype());
        presentOrder.setPresentType(String.valueOf(supplyPresentRecord.getVipType()));
        presentOrder.setProductAmount(refundAmount);
        presentOrder.setStatus(EnumOrderStatusCode.REFUND_SUCCESS.getCode());
        presentOrder.setPresentConditionId(supplyPresentRecord.getPresentConditionId());
        presentOrder.setPresentConfigId(supplyPresentRecord.getPresentConfigId());
        presentOrder.setPresentPerformId(1l);
        presentOrder.setDeadlineStartTime(supplyPresentRecord.getDeadlineStartTime());
        presentOrder.setDeadlineEndTime(DateUtils.getDateBefore(supplyPresentRecord.getDeadlineEndTime(), supplyPresentRecord.getAmount()));
        presentOrder.setPayTime(supplyPresentRecord.getCreateTime());
        presentOrder.setReceiveTime(supplyPresentRecord.getCreateTime());
        presentOrder.setUpdateTime(new Date());
        presentOrder.setCreateTime(new Date());
        presentOrder.setOrderType(EnumOrderTypeCode.NOT.getCode());
        presentOrder.setFv(StringUtils.isNotBlank(message.getFv()) ? message.getFv() : StringConstants.DEAULT_FV);
        return presentOrder;
    }

}
