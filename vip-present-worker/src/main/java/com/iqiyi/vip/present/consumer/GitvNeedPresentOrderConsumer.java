package com.iqiyi.vip.present.consumer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.iqiyi.vip.present.data.PresentRulePerform;
import com.iqiyi.vip.present.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Set;

import com.iqiyi.vip.present.consts.EnumPerformStatusCode;
import com.iqiyi.vip.present.consts.PresentConstants;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.handler.GitvNeedPresentOrderHandler;
import com.iqiyi.vip.present.model.PresentCondition;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.model.PresentPerform;
import com.iqiyi.vip.present.task.PresentVipAsyncTask;
import com.iqiyi.vip.present.utils.CloudConfigUtil;

/**
 * Created at: 2022-04-08
 *
 * <AUTHOR>
 */
@Slf4j
@Profile({"!i18n"})
@Component
public class GitvNeedPresentOrderConsumer implements MessageListenerOrderly {

    @Resource
    PresentConfigService presentConfigService;
    @Resource
    PresentPerformService presentPerformService;
    @Resource
    PresentRuleComponent presentRuleComponent;
    @Resource
    PresentDataComponent presentDataComponent;
    @Resource
    GitvNeedPresentOrderHandler gitvNeedPresentOrderHandler;
    @Resource
    private PresentRulePerformComponent presentRulePerformComponent;

    @Override
    public ConsumeOrderlyStatus consumeMessage(List<MessageExt> msgs, ConsumeOrderlyContext context) {
        for (MessageExt messageExt : msgs) {
            StopWatch stopWatch = StopWatch.createStarted();
            String msgBody = new String(messageExt.getBody(), StandardCharsets.UTF_8);
            OrderMessage orderMessage = JSON.parseObject(msgBody, OrderMessage.class);
            orderMessage.setMsgId(messageExt.getMsgId());
            try {
                if (PresentConstants.HISTORY_USER_PRESENT == orderMessage.getPayType()) {
                    continue;
                }
                handleOneMsg(orderMessage);
            } catch (Exception e) {
                log.error("[GitvNeedPresentOrderConsumer deal error] [topic:{}] [msgId:{}] [message:{}] [cost:{}]", messageExt.getTopic(), orderMessage.getMsgId(), JSON.toJSONString(orderMessage), stopWatch.getTime(), e);
                return ConsumeOrderlyStatus.SUSPEND_CURRENT_QUEUE_A_MOMENT;
            }
            log.info("[GitvNeedPresentOrderConsumer deal end] [topic:{}] [msgId:{}] [message:{}] [cost:{}]", messageExt.getTopic(), orderMessage.getMsgId(), JSON.toJSONString(orderMessage), stopWatch.getTime());
        }
        return ConsumeOrderlyStatus.SUCCESS;
    }

    private void handleOneMsg(OrderMessage orderMessage) {
        List<PresentVipAsyncTask.TaskData> taskDatas = Lists.newArrayList();
        Set<Integer> notNeedPresentVipTypeSet = orderMessage.getNotNeedPresentVipTypeSet();
        for (Long presentConfigId : CloudConfigUtil.historyPresentConfigIds()) {
            PresentConfig presentConfig = presentConfigService.queryConfigById(presentConfigId);
            if (presentConfig == null) {
                continue;
            }
            if (CollectionUtils.isNotEmpty(notNeedPresentVipTypeSet)
                && notNeedPresentVipTypeSet.contains(Integer.valueOf(presentConfig.getPvipType()))) {
                continue;
            }
            if (presentRuleComponent.needNotPresent(orderMessage, presentConfig)) {
                continue;
            }
//            PresentCondition targetCondition = presentRuleComponent.getTargetCondition(orderMessage, presentConfig);
//            if (targetCondition == null) {
//                log.warn("条件无效，什么也不做, orderCode: {}, presentConfigId:{}, presentConfigName:{}", orderMessage.getOrderCode(), presentConfig.getId(), presentConfig.getRemark());
//                continue;
//            }
//            PresentPerform presentPerform = presentPerformService.queryPresentPerformById(targetCondition.getPerformId());
//            if (presentPerform == null || EnumPerformStatusCode.NONE.getCode().equals(presentPerform.getStatus())) {
//                log.warn("执行规则无效，什么也不做, orderCode: {}, presentConfigId:{}, presentConfigName:{}", orderMessage.getOrderCode(), presentConfig.getId(), presentConfig.getRemark());
//                continue;
//            }
            PresentRulePerform rulePerform = presentRulePerformComponent.checkRulePerform(orderMessage, presentConfig);
            if (rulePerform == null) {
                continue;
            }
            taskDatas.add(presentDataComponent.getPresentOrderTask(orderMessage, presentConfig, rulePerform.getPerform(), rulePerform.getCondition()));
        }
        gitvNeedPresentOrderHandler.historyOrderSaveAndRunTask(taskDatas);
    }

}
