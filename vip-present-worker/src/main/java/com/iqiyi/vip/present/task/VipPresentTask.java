package com.iqiyi.vip.present.task;

import com.google.common.base.Charsets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.core.env.Environment;

import java.util.Map;

import com.iqiyi.vip.present.consts.EnumOrderStatusCode;
import com.iqiyi.vip.present.model.PresentAsyncTask;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.out.PresentVipRequest;
import com.iqiyi.vip.present.out.PresentVipResponse;
import com.iqiyi.vip.present.out.apireq.PresentApiReq;
import com.iqiyi.vip.present.out.apireq.PresentApiReqEngine;
import com.iqiyi.vip.present.service.PresentOrderService;
import com.iqiyi.vip.present.service.RestTemplateService;
import com.iqiyi.vip.present.task.async.AbstractTask;
import com.iqiyi.vip.present.utils.ApplicationContextUtil;
import com.iqiyi.vip.present.utils.EncodeUtils;
import com.iqiyi.vip.present.utils.JacksonUtils;

/**
 * Created at: 2022-04-12
 *
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class VipPresentTask extends AbstractTask {

    private PresentVipRequest presentVipRequest;
    private PresentOrder presentOrder;

    @Override
    protected boolean execute() {
        StopWatch stopWatch = StopWatch.createStarted();
        if (presentVipRequest.getAmount() == 0) {
            log.info("HistoryPresentTask ignored, amount为0，presentVipRequest: {}, presentOrder: {}",
                JacksonUtils.toJsonString(presentVipRequest), JacksonUtils.toJsonString(presentOrder));
            return true;
        }
        Environment environment = (Environment) ApplicationContextUtil.getBean(Environment.class);
        RestTemplateService restTemplateService = (RestTemplateService) ApplicationContextUtil.getBean(RestTemplateService.class);
        PresentOrderService presentOrderService = (PresentOrderService) ApplicationContextUtil.getBean(PresentOrderService.class);

        int presentType = NumberUtils.toInt(presentOrder.getPresentType());
        PresentApiReq presentApiReq = PresentApiReqEngine.build(presentType);
        String url = presentApiReq.presentUrl(environment);
        Boolean lb = presentApiReq.ispPresentLb(environment);
        Map reqMap = presentApiReq.presentParams(presentVipRequest, environment, presentOrder);
        PresentVipResponse presentVipResponse;
        try {
            presentVipResponse = restTemplateService.postForObject(url, reqMap, PresentVipResponse.class,lb);
        } catch (Exception e) {
            log.error("HistoryPresentTask 请求免费下单接口异常，msgId:{}, uid:{}, orderCode:{}, presentVipType:{}",
                presentOrder.getMsgId(), presentOrder.getUid(), presentOrder.getOrderCode(), presentType, e);
            return false;
        }
        if (presentVipResponse != null && presentVipResponse.isUserNotFound()) {
            log.warn("HistoryPresentTask 用户不存在, cost:{}, msgId:{}, uid:{}, orderCode:{}, presentVipType:{}, response:{}", stopWatch.getTime(),
                presentOrder.getMsgId(), presentOrder.getUid(), presentOrder.getOrderCode(), presentType, JacksonUtils.toJsonString(presentVipResponse));
            return true;
        }
        if (presentVipResponse == null || !presentVipResponse.isSuccess()) {
            log.error("HistoryPresentTask 请求免费下单接口返回失败, cost:{}, msgId:{}, uid:{}, orderCode:{}, presentVipType:{}, response:{}", stopWatch.getTime(),
                presentOrder.getMsgId(), presentOrder.getUid(), presentOrder.getOrderCode(), presentType, JacksonUtils.toJsonString(presentVipResponse));
            return false;
        }

        int oldStatus = presentOrder.getStatus();
        presentOrder.setStatus(EnumOrderStatusCode.ALREDY_PRESENT.getCode());
        if (presentVipResponse.getData() != null) {
            String presentOrderCode = presentVipResponse.getData().getOrderCode();
            presentOrder.setPresentOrderCode(presentOrderCode);
        }
        presentOrderService.updateOrderStatus(presentOrder, oldStatus);
        log.info("HistoryPresentTask execute end, cost:{}, msgId:{}, uid:{}, orderCode:{}, presentOrderCode: {}, presentVipType:{}", stopWatch.getTime(),
            presentOrder.getMsgId(), presentOrder.getUid(), presentOrder.getOrderCode(), presentOrder.getPresentOrderCode(), presentType);
        return true;
    }

    @Override
    public String genTaskId() {
        String taskId = getClass().getName() + presentOrder.getOrderCode() + presentOrder.getUid() + presentOrder.getBuyType() + presentOrder.getPresentType();
        return EncodeUtils.MD5(taskId, Charsets.UTF_8.name());
    }

    @Override
    public void deserialize(String data) throws IllegalArgumentException {
        VipPresentTask task = JacksonUtils.parseObject(data, VipPresentTask.class);
        if (task == null) {
            throw new IllegalArgumentException("参数解析失败");
        }
        presentVipRequest = task.getPresentVipRequest();
        presentOrder = task.getPresentOrder();
    }

    @Override
    public String serialize() {
        return JacksonUtils.toJsonString(this);
    }

    public PresentAsyncTask buildAsyncTask() {
        return PresentAsyncTask.builder()
            .taskId(genTaskId())
            .data(this.serialize())
            .className(this.getClass().getName())
            .build();
    }

}
