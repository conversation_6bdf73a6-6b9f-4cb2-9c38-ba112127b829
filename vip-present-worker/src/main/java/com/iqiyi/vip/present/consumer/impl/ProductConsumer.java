package com.iqiyi.vip.present.consumer.impl;

import com.alibaba.fastjson.JSON;
import com.iqiyi.vip.present.listener.ConfigListener;
import com.iqiyi.vip.present.listener.Event;
import com.iqiyi.vip.present.listener.PresentProductEvent;
import com.iqiyi.vip.present.model.PresentProduct;
import com.iqiyi.vip.present.service.PresentProductService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 会员产品消息监听
 *
 * <AUTHOR>
 * @date 2019/12/15 10:01
 */
@Component
@Slf4j
public class ProductConsumer implements MessageListenerConcurrently {

    @Resource
    PresentProductService presentProductService;

    @Resource
    List<ConfigListener> configListener;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list,
        ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt messageExt : list) {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            String json = new String(messageExt.getBody(), StandardCharsets.UTF_8);
            try {
                log.info("[ConsumerMQ]dealMessage original message:{}", json);
                PresentProduct product = JSON.parseObject(json, PresentProduct.class);
                presentProductService.saveOrUpdate(product);
                for (ConfigListener listener : configListener) {
                    Event event = new PresentProductEvent(product);
                    listener.onMessage(event);
                }
            } catch (Exception e) {
                log.error("[ConsumerMQ]dealMessage fail, message:{}", json, e);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            } finally {
                long cost = stopWatch.getTime();
                log.info("[ConsumerMQ]dealMessage completely, message:{}, cost:{}", json, cost);
            }

        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}