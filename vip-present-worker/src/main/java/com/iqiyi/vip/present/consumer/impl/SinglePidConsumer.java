package com.iqiyi.vip.present.consumer.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.iqiyi.vip.present.dao.PresentSinglePidDao;
import com.iqiyi.vip.present.data.SingleProductMessage;
import com.iqiyi.vip.present.model.PresentSinglePid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Profile({"!i18n"})
public class SinglePidConsumer implements MessageListenerConcurrently {
    @Autowired
    private PresentSinglePidDao presentSinglePidDao;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt messageExt : msgs) {
            StopWatch stopWatch = StopWatch.createStarted();
            String msgBody = new String(messageExt.getBody(), StandardCharsets.UTF_8);
            SingleProductMessage singleMessage = JSON.parseObject(msgBody, SingleProductMessage.class);
            if (singleMessage == null) {
                continue;
            }
            singleMessage.setMsgId(messageExt.getMsgId());
            try {
                PresentSinglePid nSinglePid = new PresentSinglePid();
                nSinglePid.setPid(singleMessage.getCode());
                nSinglePid.setName(singleMessage.getName());
                nSinglePid.setDescription(singleMessage.getDescription());
                presentSinglePidDao.insertOrUpdate(nSinglePid);
            } catch (Exception e) {
                log.error("[save single pid occurred exception], topic: {}, msgBody: {}", messageExt.getTopic(), JSONObject.toJSONString(singleMessage), e);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
            log.info("[save single pid success], topic: {}, msgBody: {}, cost: {}", messageExt.getTopic(), JSONObject.toJSONString(singleMessage), stopWatch.getTime());
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
