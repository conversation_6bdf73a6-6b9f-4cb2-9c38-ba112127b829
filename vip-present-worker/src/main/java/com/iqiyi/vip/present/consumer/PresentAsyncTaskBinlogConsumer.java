package com.iqiyi.vip.present.consumer;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Charsets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.present.exception.CustomJsonException;
import com.iqiyi.vip.present.handler.CanalEventHandler;
import com.iqiyi.vip.present.handler.CanalEventHandlerRegistry;
import com.iqiyi.vip.present.mysqlio.CanalEvent;
import com.iqiyi.vip.present.utils.JacksonUtils;

/**
 * Created at: 2022-04-11
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Profile({"!i18n"})
public class PresentAsyncTaskBinlogConsumer implements MessageListenerConcurrently {

    @Resource
    CanalEventHandlerRegistry canalEventHandlerRegistry;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt messageExt : msgs) {
            StopWatch stopWatch = StopWatch.createStarted();
            String msgBody = new String(messageExt.getBody(), Charsets.UTF_8);
            try {
                handleOneMsg(msgBody);
            } catch (Exception e) {
                log.error("[{} deal error] [topic:{}] [msgId:{}] [msgBody:{}] [cost:{}]", getClass().getSimpleName(),
                    messageExt.getTopic(), messageExt.getMsgId(), msgBody, stopWatch.getTime(), e);
                if (e instanceof CustomJsonException) {
                    continue;
                }
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
            log.info("[{} deal end] [topic:{}] [msgId:{}] [msgBody:{}] [cost:{}]", getClass().getSimpleName(),
                messageExt.getTopic(), messageExt.getMsgId(), msgBody, stopWatch.getTime());
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private void handleOneMsg(String msgBody) {
        CanalEvent<Map<String, Object>> mapCanalEvent = JacksonUtils.parseObject(msgBody, new TypeReference<CanalEvent<Map<String, Object>>>() {});
        CanalEventHandler canalEventHandler = canalEventHandlerRegistry.getHandler(mapCanalEvent.getTableName());
        if (canalEventHandler == null) {
            log.warn("Not found suitable CanalEventHandler for this event:{}", msgBody);
            return;
        }
        if (!canalEventHandler.accept(mapCanalEvent)) {
            return;
        }
        canalEventHandler.handCanalEvent(msgBody);
    }

}
