package com.iqiyi.vip.present.consumer.special;

import com.alibaba.fastjson.JSONObject;
import com.iqiyi.vip.present.consts.EnumOrderStatusCode;
import com.iqiyi.vip.present.consts.EnumOrderTypeCode;
import com.iqiyi.vip.present.consts.VipTypeEnum;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.data.VipRightsGrantMessage;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.out.PresentVipRequest;
import com.iqiyi.vip.present.out.PresentVipResponse;
import com.iqiyi.vip.present.out.VipUser;
import com.iqiyi.vip.present.service.PresentOrderService;
import com.iqiyi.vip.present.service.RocketMqSendService;
import com.iqiyi.vip.present.service.VipInfoService;
import com.iqiyi.vip.present.task.PresentVipAsyncTask;
import com.iqiyi.vip.present.utils.PackagePresentVip;
import com.iqiyi.vip.repository.ClusterAsyncTaskManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class GoldPresentYouthConsumer {

    @Autowired
    private VipInfoService vipInfoService;
    @Autowired
    private PresentOrderService presentOrderService;
    @Autowired
    private ClusterAsyncTaskManager asyncTaskManager;
    @Autowired
    private RocketMqSendService rocketMqSendService;

    @Value("${gold.present.youth}")
    private String pid;

    public void goldPresentYouth(OrderMessage message) throws Exception{
        //todo 可以设置一个开关，而且特殊逻辑，没有必要从config里面取值，直接写死也可以
        //单独处理黄金送青春
        if (String.valueOf(VipTypeEnum.gold.getVipType()).equals(message.getProductSubtype())) {
            //查询是否有未过期的青春身份
            long start = System.currentTimeMillis();
            VipUser vipUser = vipInfoService.getVipInfo(message.getUid(), String.valueOf(VipTypeEnum.youth.getVipType()), message.getMsgId());
            log.info("GoldPresentYouthConsumer getVipInfo [msgId:{}] [message:{}] [vipUser:{}][cost:{}]", message.getMsgId(),
                JSONObject.toJSONString(message), JSONObject.toJSONString(vipUser), System.currentTimeMillis() - start);
            //未过期
            if (vipUser != null && "1".equals(vipUser.getType())) {
                PresentConfig presentConfig = new PresentConfig();
                //买黄金送青春会员
                presentConfig.setId(1L);
                presentConfig.setPayType(381);
                presentConfig.setPresentCode(pid);
                presentConfig.setPvipType(String.valueOf(VipTypeEnum.youth.getVipType()));
                PresentVipRequest request = PackagePresentVip.packagePresentVipRequest(message, presentConfig);
                PresentVipResponse.ResponseData responseData = null;
                try {
                    PresentOrder presentOrder = PackagePresentVip.packagePresentOrderRequest(message, presentConfig, EnumOrderStatusCode.NO_PRESENT
                        .getCode(), null, request, EnumOrderTypeCode.YES.getCode());
                    presentOrder.setPresentConditionId(1L);
                    presentOrder.setPresentPerformId(1L);

                    log.info("GoldPresentYouthConsumer [msgId:{}] [message:{}] [request:{}][presentOrder:{}]", message.getMsgId(), JSONObject
                        .toJSONString(message), JSONObject.toJSONString(request), JSONObject.toJSONString(presentOrder));
                    int row = presentOrderService.insertVipOrderPresent(presentOrder);
                    if (row == 0) {
                        log.info("GoldPresentYouthConsumer goldPresentYouth[msgId:{}] [message:{}] 不进行赠送，此单在数据库存在，已经赠送过 [presentOrder:{}][row:{}]", message
                            .getMsgId(), JSONObject.toJSONString(message), JSONObject.toJSONString(presentOrder), String
                            .valueOf(row));
                        return;
                    }
                    //asyncTaskManager.insertTask(new PresentVipAsyncTask(request, presentOrder));
                    rocketMqSendService.sendVipRightsGrantMessage(new VipRightsGrantMessage(request, presentOrder));

                } catch (Exception e) {
                    log.error("GoldPresentYouthConsumer errror [param:{}] [responseData:{}]", JSONObject.toJSONString(request),
                        JSONObject .toJSONString(responseData), e);
                }
            }
        }
    }
}
