package com.iqiyi.vip.present.consumer;

import com.alibaba.fastjson.JSON;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.model.ProcessResult;
import com.iqiyi.vip.present.utils.OrderRehearsal;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;

import static com.iqiyi.vip.present.utils.OrderRehearsal.PRESSURE_TEST_MODE;

/**
 * 监听交易订单完成消息
 *
 * <AUTHOR>
 * @date 2019年12月16日下午2:11:47
 */
@Slf4j
@Component
public class OrderFinishedConsumer implements MessageListenerConcurrently {

    @Resource
    private List<BaseMessageListener<OrderMessage>> messageHandlers;

    @Value("${open.pressure.test.filter:true}")
    private boolean isOpenPressureTestFilter;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt messageExt : msgs) {

            if (OrderRehearsal.isPressureOrderMsg(messageExt, isOpenPressureTestFilter)) { // 全链路压测，是否是测试环境
                log.info("[rmq deal skipped, 全链路压测测试消息跳过处理:{}]", messageExt.getMsgId());
                continue;
            }

            StopWatch stopWatch = StopWatch.createStarted();
            String msgBody = new String(messageExt.getBody(), StandardCharsets.UTF_8);
            OrderMessage orderMessage = JSON.parseObject(msgBody, OrderMessage.class);
            orderMessage.setMsgId(messageExt.getMsgId());
            orderMessage.setIsPressureTest(messageExt.getUserProperty(PRESSURE_TEST_MODE));
            try {
                for (BaseMessageListener<OrderMessage> messageListener : messageHandlers) {
                    if (messageListener.match(orderMessage)) {
                        ProcessResult result = messageListener.dealOrder(orderMessage);
                        if (result != null && !result.isSuccess()) {
                            log.info("[rmq deal result] [topic:{}] [msgId:{}] [message:{}] [resultCode:{}] [resultMessage:{}] [cost:{}]",
                                messageExt.getTopic(), orderMessage.getMsgId(), JSON.toJSONString(orderMessage),
                                result.getResultCode().getCode(), result.getMessage(), stopWatch.getTime());
                        }
                    }
                }
            } catch (Exception e) {
                log.error("[rmq deal error] [topic:{}] [msgId:{}] [message:{}] [cost:{}]", messageExt.getTopic(), orderMessage.getMsgId(), JSON.toJSONString(orderMessage), stopWatch.getTime(), e);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
            log.info("[rmq deal start] [topic:{}] [msgId:{}] [message:{}] [cost:{}]", messageExt.getTopic(), orderMessage.getMsgId(), JSON.toJSONString(orderMessage), stopWatch.getTime());
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

}
