package com.iqiyi.vip.present.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.present.consts.EnumOrderStatusCode;
import com.iqiyi.vip.present.dao.PresentAsyncTaskDao;
import com.iqiyi.vip.present.dao.PresentOrderDao;
import com.iqiyi.vip.present.model.PresentAsyncTask;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.task.VipPresentTask;
import com.iqiyi.vip.present.task.PresentVipAsyncTask;
import com.iqiyi.vip.present.task.PresentVipAsyncTask.TaskData;

/**
 * Created at: 2022-04-08
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class GitvNeedPresentOrderHandler {

    @Resource
    PresentOrderDao presentOrderDao;
    @Resource
    PresentAsyncTaskDao presentAsyncTaskDao;

    @Transactional(rollbackFor = Exception.class)
    public void historyOrderSaveAndRunTask(List<TaskData> taskDatas) {
        for (PresentVipAsyncTask.TaskData taskData : taskDatas) {
            PresentOrder presentOrder = taskData.getPresentOrder();
            try {
                int row = presentOrderDao.insert(presentOrder);
                if (row == 0) {
                    log.info("不进行赠送，此订单在数据库存在, 已经赠送过, uid:{} orderCode:{} presentVipType:{} presentTradeCode:{}",
                        presentOrder.getUid(), presentOrder.getOrderCode(), presentOrder.getPresentType(), presentOrder.getPresentTradeCode());
                    continue;
                }
                if (!EnumOrderStatusCode.NO_PRESENT.getCode().equals(presentOrder.getStatus())) {
                    continue;
                }
            } catch (DuplicateKeyException e) {
                log.error("赠送记录的presentTradeCode重复，uid:{} orderCode:{} presentTradeCode:{} presentVipType:{} presentConfigId:{}",
                    presentOrder.getUid(), presentOrder.getOrderCode(), presentOrder.getPresentTradeCode(), presentOrder.getPresentType(), presentOrder.getPresentConditionId(), e);
                continue;
            }

            VipPresentTask vipPresentTask = new VipPresentTask(taskData.getRequest(), taskData.getPresentOrder());
            PresentAsyncTask presentAsyncTask = vipPresentTask.buildAsyncTask();
            try {
                presentAsyncTaskDao.addAsyncTask(presentAsyncTask);
            } catch (DuplicateKeyException e) {
                log.error("赠送异步任务表记录重复, taskId:{} uid:{} orderCode:{} presentVipType:{}, presentConfigId:{}",
                    presentAsyncTask.getTaskId(), presentOrder.getUid(), presentOrder.getOrderCode(), presentOrder.getPresentType(), presentOrder.getPresentConfigId(), e);
            }
        }
    }

}
