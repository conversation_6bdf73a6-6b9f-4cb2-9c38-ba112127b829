package com.iqiyi.vip.present.controller;

import com.iqiyi.vip.present.service.PresentConfigService;
import com.iqiyi.vip.present.utils.PresentConfigCacheComponent;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@RestController
@Slf4j
public class CommonController {

    @Autowired
    private PresentConfigService presentConfigService;

    @RequestMapping("/")
    public String index() {
        return "vip-present-worker";
    }

    @RequestMapping("/worker/flushCache")
    public String flushCache() {
        PresentConfigCacheComponent.configMap = presentConfigService.queryPresentConfig();
        return "success";
    }
}
