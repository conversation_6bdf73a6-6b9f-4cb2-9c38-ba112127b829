package com.iqiyi.vip.present.config;

import com.iqiyi.vip.present.consumer.*;
import com.iqiyi.vip.present.consumer.impl.ProductConsumer;
import com.iqiyi.vip.present.consumer.impl.SinglePidConsumer;
import com.iqiyi.vip.present.utils.ApplicationContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Profile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2019/12/26 11:06
 * @Description: mq配置
 */
@Configuration
@Slf4j
public class RocketMQConfig {

    @Autowired
    ProductConsumer productConsumer;

    @Autowired
    OrderFinishedConsumer orderFinishedConsumer;

    @Autowired(required = false)
    PresentDelayRmqConsumer presentDelayRmqConsumer;
    @Autowired(required = false)
    GitvNeedPresentOrderConsumer gitvNeedPresentOrderConsumer;
    @Resource
    PresentAsyncTaskConsumer presentAsyncTaskConsumer;
    @Autowired(required = false)
    @Lazy
    PresentAsyncTaskBinlogConsumer presentAsyncTaskBinlogConsumer;

    @Autowired(required = false)
    SupplyBasicVipConsumer supplyBasicVipConsumer;
    @Resource
    @Lazy
    private VipRightsGrantConsumer vipRightsGrantConsumer;

    @Bean
    @ConfigurationProperties(prefix = "rocketmq.consumer.product")
    public RocketMQProperties productProperties() {
        return new RocketMQProperties();
    }

    @Bean
    @ConfigurationProperties(prefix = "rocketmq.consumer.orderfinish")
    public RocketMQProperties orderfinishProperties() {
        return new RocketMQProperties();
    }

    @Bean
    @ConfigurationProperties(prefix = "rocketmq.consumer.single-pid")
    public RocketMQProperties singlePidProperties() {
        return new RocketMQProperties();
    }

    @Bean
    @ConfigurationProperties(prefix = "rmq.present.delay.consumer")
    @Profile({"!i18n"})
    public RocketMQProperties presentDelayConsumerProperties() {
        return new RocketMQProperties();
    }

    @Bean
    @ConfigurationProperties(prefix = "rmq.present.delay.prod")
    @Profile({"!i18n"})
    public RocketMQProperties presentDelayProducerProperties() {
        return new RocketMQProperties();
    }

    @Bean
    @ConfigurationProperties(prefix = "rmq.right.transfer.order.finished.consumer")
    @Profile({"!i18n"})
    public RocketMQProperties rightTransferOrderFinishedConsumerProperties() {
        return new RocketMQProperties();
    }

    @Bean
    @Profile({"!i18n"})
    @ConfigurationProperties(prefix = "exist.gitv.need.present.order.consumer")
    public RocketMQProperties gitvNeedPresentOrderConsumerProperties() {
        return new RocketMQProperties();
    }

    @Bean
    @Profile({"!i18n"})
    @ConfigurationProperties(prefix = "present.async.task.binlog.consumer")
    public RocketMQProperties presentAsyncTaskBinlogConsumerProperties() {
        return new RocketMQProperties();
    }

    @Bean
    @Profile({"!i18n"})
    @ConfigurationProperties(prefix = "present.async.task.producer")
    public RocketMQProperties presentAsyncTaskProducerProperties() {
        return new RocketMQProperties();
    }

    @Bean
    @Profile({"!i18n"})
    @ConfigurationProperties(prefix = "present.async.task.consumer")
    public RocketMQProperties presentAsyncTaskConsumerProperties() {
        return new RocketMQProperties();
    }

    @Bean
    @Profile({"!i18n"})
    @ConfigurationProperties(prefix = "rmq.basic.vip.supply.consumer")
    public RocketMQProperties supplyBasicVipConsumerProperties() {
        return new RocketMQProperties();
    }


    @Bean
    @Profile({"!i18n"})
    @ConfigurationProperties(prefix = "present.vip.rights.grant.producer")
    public RocketMQProperties presentVipRightsGrantProducerProperties() {
        return new RocketMQProperties();
    }

    @Bean
    @Profile({"!i18n"})
    @ConfigurationProperties(prefix = "present.vip.rights.grant.consumer")
    public RocketMQProperties presentVipRightsGrantConsumerProperties() {
        return new RocketMQProperties();
    }

    @Bean
    @Profile({"!i18n"})
    public DefaultMQPushConsumer rightTransferOrderFinishedConsumer() throws MQClientException {
        RocketMQProperties rocketMQProperties = rightTransferOrderFinishedConsumerProperties();
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(rocketMQProperties.getGroupname());
        consumer.setNamesrvAddr(rocketMQProperties.getAddress());
        consumer.setToken(rocketMQProperties.getToken());
        consumer.registerMessageListener(orderFinishedConsumer);
        consumer.subscribe(rocketMQProperties.getTopics(), "*");
        consumer.start();
        return consumer;
    }


    @Bean(name = "presentDelayProducer", initMethod = "start", destroyMethod = "shutdown")
    @Profile({"!i18n"})
    public DefaultMQProducer presentDelayProducer() {
        RocketMQProperties properties = presentDelayProducerProperties();
        DefaultMQProducer producer = new DefaultMQProducer(properties.getGroupname());
        producer.setNamesrvAddr(properties.getAddress());
        producer.setToken(properties.getToken());
        return producer;
    }

    @Bean
    @Profile({"!i18n"})
    public DefaultMQPushConsumer getPresentDelayRmqConsumer() throws MQClientException {
        RocketMQProperties rocketMQProperties = presentDelayConsumerProperties();
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(rocketMQProperties.getGroupname());
        consumer.setNamesrvAddr(rocketMQProperties.getAddress());
        consumer.setToken(rocketMQProperties.getToken());
        consumer.registerMessageListener(presentDelayRmqConsumer);
        consumer.subscribe(rocketMQProperties.getTopics(), "*");
        consumer.start();
        return consumer;
    }

    @Bean
    public DefaultMQPushConsumer getProductRocketMQConfig() {
        RocketMQProperties properties = productProperties();
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(properties.getGroupname());
        consumer.setNamesrvAddr(properties.getAddress());
        consumer.setToken(properties.getToken());
        consumer.setConsumeThreadMin(properties.getConsumethreadmin());
        consumer.setConsumeThreadMax(properties.getConsumethreadmax());
        consumer.registerMessageListener(productConsumer);
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
        consumer.setConsumeMessageBatchMaxSize(properties.getConsumemessagebatchmaxsize());
        startRocketMQ(properties, consumer);
        return consumer;
    }

    @Bean
    public DefaultMQPushConsumer getOrderFinishRocketMQConfig() {
        RocketMQProperties mqProperties = orderfinishProperties();
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(mqProperties.getGroupname());
        consumer.setNamesrvAddr(mqProperties.getAddress());
        consumer.setToken(mqProperties.getToken());
        consumer.setConsumeThreadMin(mqProperties.getConsumethreadmin());
        consumer.setConsumeThreadMax(mqProperties.getConsumethreadmax());
        consumer.registerMessageListener(orderFinishedConsumer);
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
        consumer.setConsumeMessageBatchMaxSize(mqProperties.getConsumemessagebatchmaxsize());
        startRocketMQ(mqProperties, consumer);
        return consumer;
    }

    @Bean
    @Profile({"!i18n"})
    public DefaultMQPushConsumer getSinglePidRocketMQConfig() {
        RocketMQProperties mqProperties = singlePidProperties();
        SinglePidConsumer singlePidConsumer = (SinglePidConsumer) ApplicationContextUtil.getBean(SinglePidConsumer.class);
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(mqProperties.getGroupname());
        consumer.setNamesrvAddr(mqProperties.getAddress());
        consumer.setToken(mqProperties.getToken());
        consumer.setConsumeThreadMin(mqProperties.getConsumethreadmin());
        consumer.setConsumeThreadMax(mqProperties.getConsumethreadmax());
        consumer.registerMessageListener(singlePidConsumer);
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
        consumer.setConsumeMessageBatchMaxSize(mqProperties.getConsumemessagebatchmaxsize());
        startRocketMQ(mqProperties, consumer);
        return consumer;
    }

    @Bean
    @Profile({"!i18n"})
    public DefaultMQPushConsumer gitvNeedPresentOrderConsumerConfig() {
        RocketMQProperties mqProperties = gitvNeedPresentOrderConsumerProperties();
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(mqProperties.getGroupname());
        consumer.setNamesrvAddr(mqProperties.getAddress());
        consumer.setToken(mqProperties.getToken());
        consumer.registerMessageListener(gitvNeedPresentOrderConsumer);
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
        startRocketMQ(mqProperties, consumer);
        return consumer;
    }

    @Bean
    @Profile({"!i18n"})
    public DefaultMQPushConsumer presentAsyncTaskBinlogConsumerConfig() {
        RocketMQProperties mqProperties = presentAsyncTaskBinlogConsumerProperties();
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(mqProperties.getGroupname());
        consumer.setNamesrvAddr(mqProperties.getAddress());
        consumer.setToken(mqProperties.getToken());
        consumer.registerMessageListener(presentAsyncTaskBinlogConsumer);
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
        startRocketMQ(mqProperties, consumer);
        return consumer;
    }

    @Profile({"!i18n"})
    @Bean(name = "presentAsyncTaskProducer", initMethod = "start", destroyMethod = "shutdown")
    public DefaultMQProducer presentAsyncTaskProducer() {
        RocketMQProperties properties = presentAsyncTaskProducerProperties();
        DefaultMQProducer producer = new DefaultMQProducer(properties.getGroupname());
        producer.setNamesrvAddr(properties.getAddress());
        producer.setToken(properties.getToken());
        return producer;
    }

    @Bean
    @Profile({"!i18n"})
    public DefaultMQPushConsumer presentAsyncTaskConsumerConfig() {
        RocketMQProperties mqProperties = presentAsyncTaskConsumerProperties();
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(mqProperties.getGroupname());
        consumer.setNamesrvAddr(mqProperties.getAddress());
        consumer.setToken(mqProperties.getToken());
        consumer.registerMessageListener(presentAsyncTaskConsumer);
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
        startRocketMQ(mqProperties, consumer);
        return consumer;
    }

    @Bean
    @Profile({"!i18n"})
    public DefaultMQPushConsumer presentVipRightsGrantConsumerConfig() {
        RocketMQProperties mqProperties = presentVipRightsGrantConsumerProperties();
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(mqProperties.getGroupname());
        consumer.setNamesrvAddr(mqProperties.getAddress());
        consumer.setToken(mqProperties.getToken());
        consumer.registerMessageListener(vipRightsGrantConsumer);
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
        startRocketMQ(mqProperties, consumer);
        return consumer;
    }

    @Profile({"!i18n"})
    @Bean(name = "vipRightsGrantProducer", initMethod = "start", destroyMethod = "shutdown")
    public DefaultMQProducer vipRightsGrantProducer() {
        RocketMQProperties properties = presentVipRightsGrantProducerProperties();
        DefaultMQProducer producer = new DefaultMQProducer(properties.getGroupname());
        producer.setNamesrvAddr(properties.getAddress());
        producer.setToken(properties.getToken());
        return producer;
    }

    @Bean
    @ConditionalOnBean(name = "supplyBasicVipConsumerProperties")
    public DefaultMQPushConsumer supplyBasicVipConsumerConfig() {
        RocketMQProperties mqProperties = supplyBasicVipConsumerProperties();
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(mqProperties.getGroupname());
        consumer.setNamesrvAddr(mqProperties.getAddress());
        consumer.setToken(mqProperties.getToken());
        consumer.registerMessageListener(supplyBasicVipConsumer);
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
        consumer.setPullInterval(900);
        consumer.setPullBatchSize(2);
//        consumer.setConsumeThreadMin(1);
//        consumer.setConsumeThreadMax(2);
        consumer.setConsumeMessageBatchMaxSize(1);
        startRocketMQ(mqProperties, consumer);
        return consumer;
    }

    private void startRocketMQ(RocketMQProperties properties, DefaultMQPushConsumer consumer) {
        try {
            consumer.subscribe(properties.getTopics(), properties.getTags());
            consumer.start();
            log.info("[rmq consumer start successfully] [group:{}] [topics:{}] [address:{}]", properties.getGroupname(), properties
                    .getTopics(), properties.getAddress());
        } catch (MQClientException e) {
            log.error("[rmq consumer start exception] [group:{}] [topics:{}] [address:{}]", properties.getGroupname(), properties
                    .getTopics(), properties.getAddress(), e);
        }
    }
}
