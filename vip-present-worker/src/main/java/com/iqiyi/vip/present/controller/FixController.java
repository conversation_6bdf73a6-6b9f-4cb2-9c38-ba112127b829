package com.iqiyi.vip.present.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Maps;
import com.iqiyi.kiwi.utils.DateHelper;
import com.iqiyi.vip.present.apiresponse.BaseResponse;
import com.iqiyi.vip.present.consumer.BaseMessageListener;
import com.iqiyi.vip.present.consumer.impl.OrderPresentConsumer;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.model.ProcessResult;
import com.iqiyi.vip.present.utils.PresentConfigCacheComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * 买赠修复
 *
 * <AUTHOR>
 * @date 2019/4/19 14:15
 */
@RestController
@RequestMapping("/fix")
@Slf4j
public class FixController {

    @Resource
    OrderPresentConsumer orderPresentConsumer;
    @Resource
    List<BaseMessageListener<OrderMessage>> messageListeners;
    @Resource
    PresentConfigCacheComponent presentConfigCacheComponent;

    /**
     * 修复已经处理过实际没处理漏赠的订单
     * <p>
     * grep "productSubtype=4" /data/logs/vip-present-worker/worker.log |grep "此订单已经处理过了"|grep "status=1">>new_data.txt
     * </p>
     */
    @RequestMapping("fixDiamond")
    @ResponseBody
    public BaseResponse fixDiamond(HttpServletRequest servletRequest, String fileAddress) throws Exception {
        if (StringUtils.isBlank(fileAddress)) {
            fileAddress = "/data/fix.txt";
        }
        BaseResponse response = BaseResponse.createSuccess();
        List<String> lines = FileUtils.readLines(new File(fileAddress));
        int successCount = 0;
        int failCount = 0;
        SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
        for (String line : lines) {
            try {
                String[] tmpArray = line.split("OrderMessage");
                String message = tmpArray[1].substring(1, tmpArray[1].indexOf(")"));
                String[] messages = message.split(",");
                Map<String, String> map = Maps.newHashMap();
                for (String msg : messages) {
                    String[] msgArray = msg.split("=");
                    if (msgArray.length > 1) {
                        String value = msgArray[1].trim();
                        try {
                            Date date = sdf.parse(value);
                            map.put(msgArray[0].trim(), DateHelper.getFormatDate(date, "yyyy-MM-dd HH:mm:ss"));
                        } catch (Exception e) {
                            map.put(msgArray[0].trim(), value);
                        }
                    }
                }

                String jsonStr = JSON.toJSONString(map);
                OrderMessage orderMessage = JSON.parseObject(jsonStr, OrderMessage.class);
                for (BaseMessageListener<OrderMessage> messageListener : messageListeners) {
                    if (!messageListener.match(orderMessage)) {
                        continue;
                    }

                    ProcessResult result = messageListener.dealOrder(orderMessage);
                    log.info("deal message:{} success, result: {}", orderMessage, result);
                    successCount++;
                }
            } catch (Exception e) {
                log.info("deal message:{} exception", e);
                failCount++;
            }
        }
        log.info("[fixDiamond] successCount:{},failCount:{}", successCount, failCount);
        return response;
    }

    @RequestMapping("/refreshPresentConfig")
    public BaseResponse refreshPresentConfig() {
        log.info("[refresh present config] [before] [configs:{}] [conditions:{}]", JSONArray.toJSONString(PresentConfigCacheComponent.configMap),
            JSONArray.toJSONString(PresentConfigCacheComponent.conditionMap));

        presentConfigCacheComponent.getPresentConfig();

        log.info("[refresh present config] [after] [configs:{}] [conditions:{}]", JSONArray.toJSONString(PresentConfigCacheComponent.configMap),
            JSONArray.toJSONString(PresentConfigCacheComponent.conditionMap));

        return BaseResponse.createSuccess();
    }

//    @RequestMapping("/singlePresent")
//    @ResponseBody
//    public BaseResponse<String> singlePresent(String oneLine) {
//        if (StringUtils.isBlank(oneLine)) {
//            return BaseResponse.createParamError();
//        }
//        String[] lineParts = oneLine.split("\\[message:");
//        if (lineParts.length != 2) {
//            return BaseResponse.createParamError();
//        }
//        try {
//            int msgEndIndex = lineParts[1].lastIndexOf("] [cost");
//            String msgStr = lineParts[1].substring(0, msgEndIndex);
//            OrderMessage orderMessage = JSON.parseObject(msgStr, OrderMessage.class);
//            log.info("[singlePresent][PresentRequest:{}]", JacksonUtils.toJsonString(orderMessage));
//            if (vodOrderPresentConsumer.doMatch(orderMessage)) {
//                vodOrderPresentConsumer.doDeal(orderMessage);
//            }
//            return BaseResponse.createSuccess(orderMessage.getOrderCode());
//        } catch (Exception e) {
//            log.error("[SinglePresentController singlePresent exception], msgContent: [{}]", oneLine, e);
//            return BaseResponse.createSystemError();
//        }
//    }

    @RequestMapping("reSendFromLog")
    @ResponseBody
    public BaseResponse reSendFromLog(String logLine) throws Exception {
        BaseResponse response = BaseResponse.createSuccess();
        try {
            String[] tmpArray = logLine.split("\\[message:");
            if (tmpArray.length != 2) {
                return BaseResponse.createParamError(logLine);
            }
            String[] messagePart = tmpArray[1].split("\\] \\[");
            if (messagePart.length != 2) {
                return BaseResponse.createParamError(logLine);
            }
            OrderMessage orderMessage = JSON.parseObject(messagePart[0], OrderMessage.class);
            for (BaseMessageListener<OrderMessage> messageListener : messageListeners) {
                if (!messageListener.match(orderMessage)) {
                    continue;
                }

                ProcessResult result = messageListener.dealOrder(orderMessage);
                log.info("deal success, orderCode:{}, result: {}", orderMessage.getOrderCode(), result);
            }
        } catch (Exception e) {
            log.error("dead failed message:{}", logLine, e);
        }
        return response;
    }
}
