package com.iqiyi.vip.present.handler;

import com.alibaba.csp.sentinel.Entry;
import com.alibaba.csp.sentinel.SphU;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.slots.block.RuleConstant;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager;
import com.iqiyi.vip.present.api.FreeDoPayApi;
import com.iqiyi.vip.present.apiresponse.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2024/3/20
 * @apiNote
 */
@Service
@Slf4j
public class SupplyDoPayHandler {

    static {
        FlowRule rule = new FlowRule();
        rule.setResource("supplyDoPay");
        rule.setGrade(RuleConstant.FLOW_GRADE_QPS); // 设置限流模式为 QPS
        rule.setCount(50); // 设置QPS的阈值

        // 加载规则
        FlowRuleManager.loadRules(Collections.singletonList(rule));
    }

    @Resource
    private FreeDoPayApi freeDoPayApi;

    public BaseResponse<String> doPay(Long uid, Integer payType, Integer amount, String skuId, Integer source, String tradeCode, String actCode) throws Exception {
        Entry entry = null;
        try {
            entry = SphU.entry("supplyDoPay");
            return freeDoPayApi.pay(uid, payType, amount, skuId, source, tradeCode, actCode);
        } catch (BlockException ex) {
            // 资源访问阻止，被限流或被降级
            // 在此处进行相应的处理操作
            log.error("uid:{}, Exception: ", uid, ex);
            throw ex;
        } finally {
            if (entry != null) {
                entry.exit();
            }
        }
    }
}
