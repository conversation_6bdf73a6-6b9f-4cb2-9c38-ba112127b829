package com.iqiyi.vip.present.producer;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;

import java.nio.charset.StandardCharsets;

@Slf4j
public abstract class RmqMsgProducer {

    protected boolean doSend(DefaultMQProducer producer, Message message) {
        String topic = message.getTopic();
        try {
            StopWatch stopWatch = StopWatch.createStarted();
            SendResult sendResult = producer.send(message);
            String msgContent = StringUtils.toEncodedString(message.getBody(), StandardCharsets.UTF_8);

            if (sendResult.getSendStatus() == SendStatus.SEND_OK) {
                log.info("Send msg to topic: {} success, msgId: {}, msgContent: {}, cost: {}ms",
                        topic, sendResult.getMsgId(), msgContent, stopWatch.getTime());
                return true;
            }

            log.info("Send msg to topic: {} failed, cost: {}ms, sendResult: {}, msgContent: {}",
                    topic, stopWatch.getTime(), sendResult.toString(), msgContent);
            return false;
        } catch (Exception e) {
            String msgContent = StringUtils.toEncodedString(message.getBody(), StandardCharsets.UTF_8);
            log.error("Send msg to topic: {} occur exception, msgContent: {}", topic, JSON.toJSONString(msgContent), e);
            throw (e instanceof RuntimeException) ? (RuntimeException) e : new RuntimeException(e.getMessage(), e);
        }
    }

}
