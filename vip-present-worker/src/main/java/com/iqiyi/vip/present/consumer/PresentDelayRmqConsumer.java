package com.iqiyi.vip.present.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.iqiyi.vip.present.consts.EnumOrderStatusCode;
import com.iqiyi.vip.present.consts.VipTypeEnum;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.out.PresentVipRequest;
import com.iqiyi.vip.present.service.PresentOrderService;
import com.iqiyi.vip.present.service.PresentPerformService;
import com.iqiyi.vip.present.task.PresentVipAsyncTask;
import com.iqiyi.vip.present.utils.CloudConfigUtil;
import com.iqiyi.vip.present.utils.JacksonUtils;
import com.iqiyi.vip.present.utils.PackagePresentVip;
import com.iqiyi.vip.present.utils.PresentConfigCacheComponent;
import com.iqiyi.vip.repository.ClusterAsyncTaskManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@Profile({"!i18n"})
public class PresentDelayRmqConsumer implements MessageListenerConcurrently {

    @Resource
    private PresentOrderService presentOrderService;
    @Resource
    private ClusterAsyncTaskManager clusterAsyncTaskManager;
    @Resource
    private PresentPerformService presentPerformService;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        PresentConfig presentConfig = getPresentConfig();
        for (MessageExt messageExt: msgs) {
            String msgBody = new String(messageExt.getBody(), StandardCharsets.UTF_8);
            OrderMessage orderMessage = JSON.parseObject(msgBody, OrderMessage.class);
            try {
                processDelayMessage(orderMessage, presentConfig);
            } catch (Exception e){
                log.error("[processDelayMessage error!]", e);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private void processDelayMessage(OrderMessage message, PresentConfig presentConfig) {
        log.info("[delay message][msgbody:{}]", message);
        if (!checkPresentConfig(message, presentConfig)) {
            return;
        }

        List<PresentOrder> presentOrders = getPresentOrder(message, presentConfig);
        if (CollectionUtils.isEmpty(presentOrders)) {
            log.error("[message don't match present order! msg:{}] ", message);
            return;
        }
        List<PresentOrder> needProcessOrder = presentOrders.stream().filter(this::notProcessed).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needProcessOrder)) {
            return;
        }
        for (PresentOrder order: needProcessOrder) {
            Long performId = order.getPresentPerformId();
            Integer receiveType = presentPerformService.queryReceiveTypeByIdWithDefault(performId);
            PresentVipRequest presentVipRequest = PackagePresentVip.packagePresentVipRequest(message, order, presentConfig, receiveType);
            log.info("[message:{}] [present-vip-request:{}]", message, JacksonUtils.toJsonString(presentVipRequest));
            clusterAsyncTaskManager.insertTask(new PresentVipAsyncTask(presentVipRequest, order));
        }
    }

    private boolean checkPresentConfig(OrderMessage orderMessage, PresentConfig presentConfig) {
        if (presentConfig == null) {
            return false;
        }
        if (presentConfig.getStatus() == 0 || presentConfig.getHandlerStatus() == 0) {
            return false;
        }
        if (!checkBuyCode(orderMessage, presentConfig)) {
            return false;
        }

        if (!presentConfig.checkValid(orderMessage.getPayTime())) {
            log.info("presentConfig is not valid,config:{}", presentConfig);
            return false;
        }
        return true;
    }

    private boolean checkBuyCode(OrderMessage orderMessage, PresentConfig presentConfig) {
        String buyCode = presentConfig.getBuyCode();
        // 兼容buycode为空的
        return StringUtils.isBlank(buyCode) || buyCode.equals(orderMessage.getPid());
    }


    private boolean notProcessed(PresentOrder order) {
        return String.valueOf(VipTypeEnum.kiwi.getVipType()).equals(order.getBuyType())
                && String.valueOf(VipTypeEnum.gold.getVipType()).equals(order.getPresentType())
                && StringUtils.isBlank(order.getPresentOrderCode())
                && EnumOrderStatusCode.NOT_RECEIVE.getCode().equals(order.getStatus());
    }

    private List<PresentOrder> getPresentOrder(OrderMessage message, PresentConfig presentConfig) {
        PresentOrder presentOrderRequest = new PresentOrder();
        presentOrderRequest.setOrderCode(message.getOrderCode());
        presentOrderRequest.setUid(message.getUid());
        presentOrderRequest.setPresentConfigId(presentConfig.getId());
        return presentOrderService.queryOrderByParams(presentOrderRequest);
    }

    private PresentConfig getPresentConfigById(String buyVipType, Long configId) {
        List<PresentConfig> presentConfigs = PresentConfigCacheComponent.configMap.get(buyVipType);
        if (CollectionUtils.isEmpty(presentConfigs)) {
            return null;
        }
        return presentConfigs.stream().filter(presentConfig -> presentConfig.getId().equals(configId)).findFirst().orElse(null);
    }

    private PresentConfig getPresentConfig() {
        String buyVipType = String.valueOf(VipTypeEnum.kiwi.getVipType());
        Long configId = CloudConfigUtil.sendKiwiPresentGoldRightConfigId();
        return getPresentConfigById(buyVipType, configId);
    }

}