package com.iqiyi.vip.present.consumer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.iqiyi.lego.rocketmq.core.StringRocketMQTemplate;
import com.iqiyi.vip.present.apiresponse.BaseResponse;
import com.iqiyi.vip.present.consts.PresentConstants;
import com.iqiyi.vip.present.consts.SupplySourceEnum;
import com.iqiyi.vip.present.consumer.dto.VipConfigCondition;
import com.iqiyi.vip.present.dao.SupplyPresentRecordDao;
import com.iqiyi.vip.present.handler.SupplyDoPayHandler;
import com.iqiyi.vip.present.mapper.BossVipUserMapper;
import com.iqiyi.vip.present.mapper.SupplyPresentRecordMapper;
import com.iqiyi.vip.present.model.BossVipUser;
import com.iqiyi.vip.present.model.SupplyPresentRecord;
import com.iqiyi.vip.present.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 监听基础会员补赠消息进行补赠
 *
 * <AUTHOR>
 * @date 2024/02/29
 */
@Slf4j
@Component
public class SupplyBasicVipConsumer implements MessageListenerConcurrently {

    @Resource
    private BossVipUserMapper bossVipUserMapper;

    @Resource
    private SupplyPresentRecordMapper supplyPresentRecordMapper;

    @Resource
    private SupplyDoPayHandler supplyDoPayHandler;

    @Resource
    private SupplyPresentRecordDao supplyPresentRecordDao;

    @Value("${basic.supply.pay.type:0}")
    private Integer payType;
    @Value("${basic.supply.config.condition.map:}")
    private String configCondition;
    @Value("${basic.supply.main.sku:}")
    private String mainSku;
    @Value("${basic.supply.tv.sku:}")
    private String tvSku;

    @Autowired(required = false)
    @Qualifier("supplyBasicVipRmqTemplate")
    private StringRocketMQTemplate supplyBasicVipRmqTemplate;

    private static final List<Integer> ALL_LIST = Lists.newArrayList(1, 4, 16, 58, 56, 57, 5, 54, 60);
    private static final List<Integer> MAIN_LIST = Lists.newArrayList(1, 4, 16, 58, 56);
    private static final List<Integer> TV_LIST = Lists.newArrayList(57, 5, 54, 60);
    private static final Integer MAIN_BASIC = 56;
    private static final Integer TV_BASIC = 60;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt messageExt : msgs) {
            String msgBody = new String(messageExt.getBody(), StandardCharsets.UTF_8);

            log.info("[SupplyBasicVipConsumer deal start] [topic:{}] [msgId:{}] [message:{}]", messageExt.getTopic(), messageExt.getMsgId(), msgBody);

            List<Long> supplyBasicUids = JSON.parseArray(msgBody, Long.class);

            try {
                for (Long uid : supplyBasicUids) {
                    Date now = new Date();
                    //查询会员
                    List<BossVipUser> list = bossVipUserMapper.selectByUserId(uid);
                    list = list.stream().filter(b -> b.getDeadline() != null).collect(Collectors.toList());

                    List<SupplyPresentRecord> supplyPresentRecords = supplyPresentRecordMapper.selectByUid(uid);
//                    if (CollectionUtils.isNotEmpty(supplyPresentRecords)) {
//                        //已经补过 走一遍校验逻辑
//                        checkSupplied(list, now, uid);
//                        continue;
//                    }

                    if (isAllExpired(list, now)) {
//                        //全部过期，补充一条other记录
//                        supplyPresentRecordMapper.insertSelective(SupplyPresentRecord.builder()
//                                .tradeCode(UUidUtils.getUUid()).uid(uid).source(SupplySourceEnum.OTHER.getType())
//                                .buyType(-1).vipType(-1).status(1).amount(0).payType(0).build());
                        continue;
                    }

                    try {
                        supplyConsumer(supplyPresentRecords, list, uid, now);
                    } catch (Exception e) {
                        log.error("[SupplyBasicVipConsumer single deal error] [topic:{}] [msgId:{}] [uid:{}]", messageExt.getTopic(), messageExt.getMsgId(), uid, e);
                        SendResult result = supplyBasicVipRmqTemplate.send(JSON.toJSONString(Lists.newArrayList(uid)));
                        log.info("[SupplyBasicVipConsumer single deal error uid:{}  重新发送消息:{}]", uid, result);
                    }
                }
            } catch (Exception e) {
                log.error("[SupplyBasicVipConsumer deal error] [topic:{}] [msgId:{}] [message:{}]", messageExt.getTopic(), messageExt.getMsgId(), msgBody, e);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
            log.info("[SupplyBasicVipConsumer deal finish] [topic:{}] [msgId:{}] [message:{}]", messageExt.getTopic(), messageExt.getMsgId(), msgBody);
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private void supplyConsumer(List<SupplyPresentRecord> supplyPresentRecords, List<BossVipUser> list, Long uid, Date now) throws Exception {
        List<SupplyPresentRecord> records = Lists.newArrayList();
        SupplyPresentRecord mainRecord = supply(supplyPresentRecords, list, uid, SupplySourceEnum.SUPPLY_MAIN_BASIC_VIP.getType(), now);
        if (mainRecord != null) {
            records.add(mainRecord);
        }
        SupplyPresentRecord tvRecord = supply(supplyPresentRecords, list, uid, SupplySourceEnum.SUPPLY_TV_BASIC_VIP.getType(), now);
        if (tvRecord != null) {
            records.add(tvRecord);
        }
        if (CollectionUtils.isNotEmpty(records)) {
            try {
                supplyPresentRecordDao.batchInsert(records);
            } catch (DuplicateKeyException e) {
                log.error("SupplyBasicVipConsumer supplyConsumer uid:{}, records:{}, 重复插入 忽略处理", uid, JSON.toJSONString(records));
            }
        }
    }

    private SupplyPresentRecord supply(List<SupplyPresentRecord> supplyPresentRecords, List<BossVipUser> list, Long uid, Integer source, Date now) throws Exception {
        //查询基础会员
        //分组，分主站和tv2组，取最长时长，取当前基础或者tv基础时长，相减获取需要补赠的天数，调用免费下单接口下单
        log.info("supplyConsumer userId:{}, source:{}, 开始进行补赠处理", uid, source);

        String tradeCode = PresentConstants.ACTCODE + "_" + source + "_" + uid;
        int diffDays = 0;

        //查看已经有当前会员的不赠记录
        Optional<SupplyPresentRecord> existSupplyRecordOptional = supplyPresentRecords.stream().filter(s -> s.getVipType().equals(getSourceBasicVip(source))).findFirst();

        Optional<BossVipUser> longestVipOptional = list.stream().filter(s -> getSourceVipList(source).contains(s.getTypeId())).max(Comparator.comparing(BossVipUser::getDeadline));
        if (longestVipOptional.isPresent() && longestVipOptional.get().getDeadline().after(now)) {
            //最长会员未过期 获取最长时长
            Date longestDeadline = longestVipOptional.get().getDeadline();

            //获取基础会员当前到期时间，否则取当前时间
            Optional<Date> basicDeadlineOptional = list.stream().filter(s -> Objects.equals(s.getTypeId(), getSourceBasicVip(source))).map(BossVipUser::getDeadline).max(Date::compareTo);
            Date basicDeadline = (!basicDeadlineOptional.isPresent() || basicDeadlineOptional.get().before(now))
                    ? now : basicDeadlineOptional.get();

            //基础会员到期时长在当前会员最长到期时长之前 计算天数差值
            if (basicDeadline.before(longestDeadline)) {

                //计算差值天数
                diffDays = getDiffDays(basicDeadline, longestDeadline);
                log.info("supplyConsumer userId:{}, source:{}, basicDeadline:{}, longestDeadline:{}, diffDays:{}", uid, source, basicDeadline, longestDeadline, diffDays);
                if (diffDays > 3) {

                    if (existSupplyRecordOptional.isPresent() && StringUtils.isNotBlank(existSupplyRecordOptional.get().getOrderCode())) {
                        //这种情况是已经补过一单了，所以不会做第二次补单
                        log.info("supplyConsumer userId:{}, source:{} 用户已经补过有效单了，不会再补了,但是时长还对不上", uid, source);
                        return null;
                    }

                    //给用户补赠基础会员
                    log.info("supplyConsumer userId:{}, source:{} 用户时长对不上,差3天以上，补赠基础会员", uid, source);
                    BaseResponse<String> payResponse = supplyDoPayHandler.doPay(uid, payType, diffDays, getSku(source), source, tradeCode, PresentConstants.ACTCODE);
                    if (BaseResponse.isSuccess(payResponse.getCode())) {
                        String orderCode = payResponse.getData();

                        // 记录本次补单权益开始结束时间
                        Date startTime = org.apache.commons.lang3.time.DateUtils.addSeconds(basicDeadline, 1);
                        Date endTime = longestDeadline;

                        //生成一条记录
                        VipConfigCondition condition = getByVipType(longestVipOptional.map(BossVipUser::getTypeId).orElse(null));
                        //获取已经存在的id
                        Long id = existSupplyRecordOptional.map(SupplyPresentRecord::getId).orElse(null);
                        return SupplyPresentRecord.builder()
                                .id(id)
                                .tradeCode(tradeCode)
                                .uid(uid)
                                .source(source)
                                .buyType(longestVipOptional.map(BossVipUser::getTypeId).orElse(-1))
                                .vipType(getSourceBasicVip(source))
                                .status(1)
                                .amount(diffDays)
                                .deadlineStartTime(startTime)
                                .deadlineEndTime(endTime)
                                .payType(payType)
                                .orderCode(orderCode)
                                .presentConfigId(condition != null ? condition.getConfigId() : null)
                                .presentConditionId(condition != null ? condition.getConditionId() : null)
                                .build();

                    } else {
                        throw new RuntimeException("supplyConsumer userId:" + uid + "补赠 source:" + source + " 基础会员失败，下单失败");
                    }
                } else {
                    log.info("supplyConsumer userId:{}, source:{}, 补赠基础会员因时长小于1天，忽略处理", uid, source);
                }
            }

        }
        return null;
    }

    private boolean isAllExpired(List<BossVipUser> bossVipUsers, Date now) {
        BossVipUser maxBossVipUser = bossVipUsers.stream().filter(v -> ALL_LIST.contains(v.getTypeId())).max(Comparator.comparing(BossVipUser::getDeadline)).orElse(null);
        if (maxBossVipUser == null) {
            return true;
        }
        return maxBossVipUser.getDeadline().before(now);
    }

    private List<Integer> getSourceVipList(Integer source) {
        return source.equals(SupplySourceEnum.SUPPLY_MAIN_BASIC_VIP.getType()) ? MAIN_LIST : TV_LIST;
    }

    private Integer getSourceBasicVip(Integer source) {
        return source.equals(SupplySourceEnum.SUPPLY_MAIN_BASIC_VIP.getType()) ? MAIN_BASIC : TV_BASIC;
    }

    private String getSku(Integer source) {
        return source.equals(SupplySourceEnum.SUPPLY_MAIN_BASIC_VIP.getType()) ? mainSku : tvSku;
    }

    /**
     * 计算差值天数，此处取endDate先补齐到当天的23：59：59，再进行向下取整计算天数
     *
     * @param startDate
     * @param endDate
     * @return
     */
    private Integer getDiffDays(Date startDate, Date endDate) {
        int diffDays = DateUtils.getDayInterval(startDate, DateUtils.getDayEnd(endDate));
        if (diffDays < 0) {
            throw new RuntimeException("Diff days cannot be negative");
        }
        return diffDays;
    }

    private VipConfigCondition getByVipType(Integer vipType) {
        if (vipType == null) {
            return null;
        }
        List<VipConfigCondition> configConditions = JSON.parseArray(configCondition, VipConfigCondition.class);
        return configConditions.stream().filter(c -> Objects.equals(c.getVipType(), vipType)).findFirst().orElse(null);
    }
}
