package com.iqiyi.vip.present.handler;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created at: 2022-04-14
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CanalEventHandlerRegistry implements ApplicationContextAware, InitializingBean {

    private static Map<String, CanalEventHandler> canalEventHandlerRepository = Maps.newHashMap();

    private ApplicationContext applicationContext;

    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, CanalEventHandler> eventHandlers = this.applicationContext.getBeansOfType(CanalEventHandler.class);
        for (CanalEventHandler eventHandler : eventHandlers.values()) {
            canalEventHandlerRepository.put(eventHandler.getTableName(), eventHandler);
        }
        log.info("Total Found {} CanalEventHandler", eventHandlers.size());
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public CanalEventHandler getHandler(String tableName) {
        return canalEventHandlerRepository.get(tableName);
    }

}
