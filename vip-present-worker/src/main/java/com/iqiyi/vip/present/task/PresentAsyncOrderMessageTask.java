package com.iqiyi.vip.present.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.iqiyi.vip.present.consts.TaskPoolTypeEnum;
import com.iqiyi.vip.present.handler.PresentAsyncOrderMessageHandler;
import com.iqiyi.vip.present.utils.ApplicationContextUtil;
import com.iqiyi.vip.threadpool.AbstractTask;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class PresentAsyncOrderMessageTask extends AbstractTask {

    private Long uid;
    private Integer productSubType;

    @Override
    protected boolean execute() {
        log.info("PresentAsyncOrderMessageTask execute [uid:{}] [productSubType:{}]", this.uid, this.productSubType);
        PresentAsyncOrderMessageHandler presentAsyncOrderMessageHandler = (PresentAsyncOrderMessageHandler) ApplicationContextUtil.getBean(PresentAsyncOrderMessageHandler.class);
        return presentAsyncOrderMessageHandler.processAsyncOrderMessages(uid, productSubType);
    }

    @Override
    public void deserialize(String data) throws IllegalArgumentException {
        JSONObject obj = JSON.parseObject(data);
        this.uid = obj.getLong("uid");
        this.productSubType = obj.getInteger("productSubType");
    }

    @Override
    public String serialize() {
        JSONObject obj = new JSONObject();
        obj.put("uid", this.getUid());
        obj.put("productSubType", this.getProductSubType());
        return obj.toJSONString();
    }

    @Override
    public int getDefaultPoolType() {
        return TaskPoolTypeEnum.PRESENT_ASYNC_ORDER_MESSAGE.getType();
    }
}
