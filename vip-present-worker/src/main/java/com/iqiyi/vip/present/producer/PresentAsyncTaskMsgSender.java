package com.iqiyi.vip.present.producer;

import com.alibaba.fastjson.JSON;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.Message;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.sql.Timestamp;
import java.util.Arrays;

import com.iqiyi.vip.present.model.PresentAsyncTask;
import com.iqiyi.vip.present.utils.DateUtils;
import com.iqiyi.vip.present.utils.RMQMsgSender;

/**
 * Created at: 2022-04-13
 *
 * <AUTHOR>
 */
@Profile({"!i18n"})
@Component
public class PresentAsyncTaskMsgSender extends RMQMsgSender {

    private static final String PRESENT_ASYNC_TASK_TOPIC = "present_async_task";

    @Resource
    DefaultMQProducer presentAsyncTaskProducer;

    public boolean doSend(PresentAsyncTask asyncTask) {
        Timestamp timerRunAt = asyncTask.getTimerRunAt();
        long delayTimeInMillis = DateUtils.getTimeInterval(DateUtils.getCurrentTimestamp(), timerRunAt);
        Message message = new Message(PRESENT_ASYNC_TASK_TOPIC, JSON.toJSONBytes(asyncTask));
        message.setKeys(Arrays.asList(asyncTask.getId().toString(), asyncTask.getTaskId()));
        if (delayTimeInMillis > 2000) {
            message.setDelayTimeInSeconds((int) (delayTimeInMillis / 1000));
        }
        return doSend(presentAsyncTaskProducer, message);
    }


}
