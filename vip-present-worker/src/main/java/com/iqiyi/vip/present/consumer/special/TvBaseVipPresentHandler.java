package com.iqiyi.vip.present.consumer.special;

import com.google.common.collect.Lists;
import com.iqiyi.vip.present.consts.EnumOrderStatusCode;
import com.iqiyi.vip.present.consts.EnumOrderTypeCode;
import com.iqiyi.vip.present.consts.EnumPerformStatusCode;
import com.iqiyi.vip.present.consts.VipTypeEnum;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.data.PresentRulePerform;
import com.iqiyi.vip.present.data.VipRightsGrantMessage;
import com.iqiyi.vip.present.model.PresentCondition;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.model.PresentPerform;
import com.iqiyi.vip.present.out.PresentVipRequest;
import com.iqiyi.vip.present.service.*;
import com.iqiyi.vip.present.task.PresentVipAsyncTask;
import com.iqiyi.vip.present.utils.JacksonUtils;
import com.iqiyi.vip.present.utils.PackagePresentVip;
import com.iqiyi.vip.present.utils.PresentConfigCacheComponent;
import com.iqiyi.vip.repository.ClusterAsyncTaskManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/7/31 15:03
 */
@Slf4j
@Component
public class TvBaseVipPresentHandler extends AbstractEffectiveVipPresentHandler {

    @Resource
    private VipInfoService vipInfoService;
    @Resource
    PresentRuleComponent presentRuleComponent;
    @Resource
    private PresentOrderService presentOrderService;
    @Resource
    private PresentRulePerformComponent presentRulePerformComponent;
    @Resource
    private ClusterAsyncTaskManager asyncTaskManager;
    @Resource
    private RocketMqSendService rocketMqSendService;

    @Override
    public void doPresent(OrderMessage message, PresentConfig presentConfig) {
        //条件匹配
//        PresentCondition targetCondition = presentRuleComponent.getTargetCondition(message, presentConfig);
//        if (targetCondition == null) {
//            log.info("[条件无效，什么也不做,条件:empty] [msgId:{}] [presentConfig:{}]", message.getMsgId(), JacksonUtils.toJsonString(presentConfig));
//            return;
//        }
//        //找到执行规则
//        PresentPerform presentPerform = PresentConfigCacheComponent.performMap.get(targetCondition.getPerformId());
//        if (presentPerform == null || EnumPerformStatusCode.NONE.getCode().equals(presentPerform.getStatus())) {
//            log.info("[执行规则无效，什么也不做,执行规则:{}]", targetCondition.getPerformId());
//            return;
//        }
        PresentRulePerform rulePerform = presentRulePerformComponent.checkRulePerform(message, presentConfig);
        if (rulePerform == null) {
            return;
        }

        PresentVipRequest request = PackagePresentVip.packagePresentVipRequest(message, presentConfig);
        PresentOrder presentOrder = PackagePresentVip.packagePresentOrderRequest(message, presentConfig, EnumOrderStatusCode.NO_PRESENT.getCode(), null, request, EnumOrderTypeCode.YES.getCode());
        presentOrder.setPresentConditionId(rulePerform.getCondition().getId());
        presentOrder.setPresentPerformId(rulePerform.getCondition().getPerformId());
        log.info("PresentTvBasicVipHandler [msgId:{}] [message:{}] [request:{}][presentOrder:{}]", message.getMsgId(), JacksonUtils.toJsonString(message), JacksonUtils.toJsonString(request), JacksonUtils.toJsonString(presentOrder));
        int row = presentOrderService.insertVipOrderPresent(presentOrder);
        if (row == 0) {
            log.info("PresentBasicVipHandler presentTvBasicVip[msgId:{}] [message:{}] 不进行赠送，此单在数据库存在，已经赠送过 [presentOrder:{}][row:{}]", message.getMsgId(), JacksonUtils.toJsonString(message), JacksonUtils.toJsonString(presentOrder), String.valueOf(row));
            return;
        }
        //asyncTaskManager.insertTask(new PresentVipAsyncTask(request, presentOrder));
        rocketMqSendService.sendVipRightsGrantMessage(new VipRightsGrantMessage(request, presentOrder));

    }

    @Override
    public boolean isPresentVip(OrderMessage message, PresentConfig presentConfig) {
        return String.valueOf(this.getVipType()).equals(presentConfig.getPvipType()) && // 赠送的是奇异果基础会员
                Lists.newArrayList(String.valueOf(VipTypeEnum.kiwi_diamonds.getVipType()), String.valueOf(VipTypeEnum.kiwi.getVipType()), String.valueOf(VipTypeEnum.kiwi_gold.getVipType()),
                        String.valueOf(VipTypeEnum.diamonds.getVipType()), String.valueOf(VipTypeEnum.IQIYI_PLATINUM.getVipType())).contains(message.getProductSubtype());
    }

    @Override
    public VipInfoService getVipInfoService() {
        return vipInfoService;
    }

    @Override
    public Integer getVipType() {
        return VipTypeEnum.kiwi_base.getVipType();
    }
}
