package com.iqiyi.vip.present.producer;

import com.alibaba.fastjson.JSON;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.utils.CloudConfigUtil;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@Component
@Profile({"!i18n"})
public class PresentDelayRmqProducer extends RmqMsgProducer {


	@Autowired(required = false)
	private DefaultMQProducer presentDelayProducer;

	@Value("${rmq.present.delay.consumer.topics}")
	private String rmqTopicName;

	public boolean doSend(OrderMessage orderMessage) {
        byte[] msgBytes = JSON.toJSONBytes(orderMessage);
        Message message = new Message(rmqTopicName, msgBytes);
		message.setDelayTimeInSeconds(CloudConfigUtil.orderMessageDelayTimeInSeconds());
		return doSend(presentDelayProducer, message);
	}
}
