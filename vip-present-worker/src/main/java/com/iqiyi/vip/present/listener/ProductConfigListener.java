package com.iqiyi.vip.present.listener;

import com.iqiyi.vip.present.consts.PresentConstants;
import com.iqiyi.vip.present.model.PresentProduct;
import com.iqiyi.vip.present.service.PresentProductService;
import com.iqiyi.vip.present.utils.PresentConfigCacheComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 产品配置监听器
 *
 * <AUTHOR>
 * @date 2019/12/16 17:03
 */
@Component
public class ProductConfigListener implements ConfigListener {

    @Autowired
    PresentProductService presentProductService;

    @Override
    public void onMessage(Event event) {
        if (EventEnum.PRODUCT.equals(event.getType())) {
            PresentProduct product = (PresentProduct) event.getObject();
            if (PresentConstants.DIAMOND_VIP_TYPE_CODE.equals(product.getVipTypeCode())) {
                PresentConfigCacheComponent.diamondPids = presentProductService.getCodesByVipType(PresentConstants.DIAMOND_VIP_TYPE_CODE);
            }
        }
    }
}