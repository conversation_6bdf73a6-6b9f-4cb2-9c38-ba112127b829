package com.iqiyi.vip.present.consumer.special;

import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.out.VipUser;
import com.iqiyi.vip.present.service.VipInfoService;
import com.iqiyi.vip.present.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.InitializingBean;

import java.util.Collection;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/7/31 14:52
 */
@Slf4j
public abstract class AbstractEffectiveVipPresentHandler implements InitializingBean {

    protected static ConcurrentMap<Integer, LinkedBlockingQueue<AbstractEffectiveVipPresentHandler>> presentVipMap = new ConcurrentHashMap<>();

    private static final String VIP_EXPIRED = "0";

    public static Collection<AbstractEffectiveVipPresentHandler> getInstance(Integer vipType) {
        return presentVipMap.get(vipType);
    }

    public void presentVip(OrderMessage message, PresentConfig presentConfig) throws Exception {
        StopWatch stopWatch = StopWatch.createStarted();
        VipUser vipUser = this.getVipInfoService().getVipInfo(message.getUid(), String.valueOf(this.getVipType()), message.getMsgId());
        log.info("PresentBasicVipHandler getVipInfo [msgId:{}] [message:{}] [vipUser:{}][cost:{}]", message.getMsgId(),
                JacksonUtils.toJsonString(message), JacksonUtils.toJsonString(vipUser), stopWatch.getTime());
        if (vipUser == null || VIP_EXPIRED.equals(vipUser.getType())) {
            return;
        }
        if (this.isPresentVip(message, presentConfig)) {
            this.doPresent(message, presentConfig);
        }
    }

    protected abstract boolean isPresentVip(OrderMessage message, PresentConfig presentConfig);

    protected abstract void doPresent(OrderMessage message, PresentConfig presentConfig);

    @Override
    public void afterPropertiesSet() throws Exception {
        LinkedBlockingQueue<AbstractEffectiveVipPresentHandler> abstractEffectiveVipPresentHandlers = presentVipMap.computeIfAbsent(this.getVipType(), v -> new LinkedBlockingQueue<>());
        if (abstractEffectiveVipPresentHandlers.offer(this)) {
            log.error("启动加载买赠规则 Handler 失败，Fail, {}", this);
        }
    }

    protected abstract VipInfoService getVipInfoService();

    protected abstract Integer getVipType();
}
