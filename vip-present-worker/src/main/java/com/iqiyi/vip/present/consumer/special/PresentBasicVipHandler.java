package com.iqiyi.vip.present.consumer.special;

import com.iqiyi.vip.present.consts.*;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.data.PresentRulePerform;
import com.iqiyi.vip.present.data.VipRightsGrantMessage;
import com.iqiyi.vip.present.mapper.BossVipUserMapper;
import com.iqiyi.vip.present.model.*;
import com.iqiyi.vip.present.out.PresentVipRequest;
import com.iqiyi.vip.present.out.VipUser;
import com.iqiyi.vip.present.service.*;
import com.iqiyi.vip.present.task.PresentVipAsyncTask;
import com.iqiyi.vip.present.utils.DateUtils;
import com.iqiyi.vip.present.utils.JacksonUtils;
import com.iqiyi.vip.present.utils.PackagePresentVip;
import com.iqiyi.vip.present.utils.PresentConfigCacheComponent;
import com.iqiyi.vip.repository.ClusterAsyncTaskManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import static com.iqiyi.vip.present.consts.PresentConstants.*;

/**
 * Created at: 2021-01-18
 *
 * 赠送极速版会员校验逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PresentBasicVipHandler {

    private static final String VIP_EXPIRED = "0";

    @Autowired
    private VipInfoService vipInfoService;
    @Autowired
    private PresentOrderService presentOrderService;
    @Autowired
    private ClusterAsyncTaskManager asyncTaskManager;
    @Resource
    PresentRuleComponent presentRuleComponent;
    @Resource
    private PresentRulePerformComponent presentRulePerformComponent;
    @Resource
    private SupplyPresentRecordService supplyPresentRecordService;
    @Resource
    private BossVipUserMapper bossVipUserMapper;
    @Resource
    private RocketMqSendService rocketMqSendService;

    @Value("${basic.is.closed}")
    private String basicIsClosed;
    @Value("${basic.supply.split.time}")
    private String supplySplitTime;

    public void presentBasicVip(OrderMessage message, PresentConfig presentConfig) throws Exception{
        StopWatch stopWatch = StopWatch.createStarted();
//        VipUser vipUser = vipInfoService.getVipInfo(message.getUid(), presentConfig.getPvipType(), message.getMsgId());
//        log.info("PresentBasicVipHandler getVipInfo [msgId:{}] [message:{}] [vipUser:{}][cost:{}]", message.getMsgId(),
//                JacksonUtils.toJsonString(message), JacksonUtils.toJsonString(vipUser), stopWatch.getTime());
//
//        //过期会员场景  加开关，补增完之后全部放开
//        if("0".equals(basicIsClosed)){
//            if (vipUser == null || VIP_EXPIRED.equals(vipUser.getType())) {
//                List<BossVipUser> list = bossVipUserMapper.selectByUserId(message.getUid());
//                log.info("selectByUserId return result:{}",JacksonUtils.toJsonString(list));
//                // 判断是否已补增，如果已补增则走赠送逻辑，否则暂时不赠送，判断是tv还是主站赠送
//                if(!PresentConstants.tv_vip_types.contains(message.getProductSubtype())&&!main_vip_types.contains(message.getProductSubtype())){
//                    log.info("非主站或tv会员，无需走基础会员赠送:{}",JacksonUtils.toJsonString(message));
//                    return;
//                }
////                Integer source = main_vip_types.contains(message.getProductSubtype())?
////                        SupplySourceEnum.SUPPLY_MAIN_BASIC_VIP.getType():PresentConstants.tv_vip_types.contains(message.getProductSubtype())
////                        ?SupplySourceEnum.SUPPLY_TV_BASIC_VIP.getType():SupplySourceEnum.OTHER.getType();
//                List<SupplyPresentRecord> supplyPresentRecords = supplyPresentRecordService.selectByUid(SupplyPresentRecord.builder().uid(message.getUid()).build());
//                if(CollectionUtils.isNotEmpty(list)){
//                    Optional<BossVipUser> mainCreateTimeVipOptional = list.stream().filter(s -> main_and_tv_vip_types.contains(s.getTypeId())).min(Comparator.comparing(BossVipUser::getCreateTime));
//                    log.info("mainCreateTimeVipOptional result:{}",mainCreateTimeVipOptional.isPresent()?JacksonUtils.toJsonString(mainCreateTimeVipOptional.get()):"");
//                    if(mainCreateTimeVipOptional.isPresent() && mainCreateTimeVipOptional.get().getCreateTime().after(DateUtils.parse(supplySplitTime))){
//                        log.info("该订单赠送message:{},mainCreateTimeVipOptional:{}",message,JacksonUtils.toJsonString(mainCreateTimeVipOptional.get()));
//                    }
//                    if(mainCreateTimeVipOptional.isPresent() && mainCreateTimeVipOptional.get().getCreateTime().before(DateUtils.parse(supplySplitTime)) && CollectionUtils.isEmpty(supplyPresentRecords)){
//                        log.info("订单过滤不赠送message:{},mainCreateTimeVipOptional:{}",message,JacksonUtils.toJsonString(mainCreateTimeVipOptional.get()));
//                        return;
//                    }
//                }
//            }
//        }

        //条件匹配
//        PresentCondition targetCondition = presentRuleComponent.getTargetCondition(message, presentConfig);
//        if (targetCondition == null) {
//            log.info("[条件无效，什么也不做,条件:empty] [msgId:{}] [presentConfig:{}]", message.getMsgId(), JacksonUtils.toJsonString(presentConfig));
//            return;
//        }
//        //找到执行规则
//        PresentPerform presentPerform = PresentConfigCacheComponent.performMap.get(targetCondition.getPerformId());
//        if (presentPerform == null || EnumPerformStatusCode.NONE.getCode().equals(presentPerform.getStatus())) {
//            log.info("[执行规则无效，什么也不做,执行规则:{}]", targetCondition.getPerformId());
//            return;
//        }
        PresentRulePerform rulePerform = presentRulePerformComponent.checkRulePerform(message, presentConfig);
        if (rulePerform == null) {
            return;
        }

        PresentVipRequest request = PackagePresentVip.packagePresentVipRequest(message, presentConfig);
        PresentOrder presentOrder = PackagePresentVip.packagePresentOrderRequest(message, presentConfig, EnumOrderStatusCode.NO_PRESENT.getCode(), null, request, EnumOrderTypeCode.YES.getCode());
        presentOrder.setPresentConditionId(rulePerform.getCondition().getId());
        presentOrder.setPresentPerformId(rulePerform.getCondition().getPerformId());
        presentOrder.setIsPressureTest(message.getIsPressureTest());

        log.info("PresentBasicVipHandler [msgId:{}] [message:{}] [request:{}][presentOrder:{}]", message.getMsgId(), JacksonUtils.toJsonString(message), JacksonUtils.toJsonString(request), JacksonUtils.toJsonString(presentOrder));
        int row = presentOrderService.insertVipOrderPresent(presentOrder);
        if (row == 0) {
            log.info("PresentBasicVipHandler goldPresentBasic[msgId:{}] [message:{}] 不进行赠送，此单在数据库存在，已经赠送过 [presentOrder:{}][row:{}]", message.getMsgId(), JacksonUtils.toJsonString(message), JacksonUtils.toJsonString(presentOrder), String.valueOf(row));
            return;
        }
        //使用地较多 不影响老逻辑 不修改updatesql
        if(null==presentOrder.getId()||0==presentOrder.getId()){
            PresentOrder presentOrderDto = presentOrderService.queryByPresentTradeCode(presentOrder.getUid(),presentOrder.getPresentTradeCode());
            if(null!=presentOrderDto){
                log.info("queryByPresentTradeCode presentOrderDto result:{}",presentOrderDto);
                presentOrder.setId(presentOrderDto.getId());
            }
        }
        //asyncTaskManager.insertTask(new PresentVipAsyncTask(request, presentOrder));
        rocketMqSendService.sendVipRightsGrantMessage(new VipRightsGrantMessage(request, presentOrder));
    }
}
