package com.iqiyi.vip.present;

import com.iqiyi.vip.present.config.datasource.DynamicDataSourceRegister;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;

@MapperScan("com.iqiyi.vip.present.mapper")
@ComponentScan(value={"com.iqiyi.vip"},
    excludeFilters = {
    @ComponentScan.Filter(type=FilterType.REGEX,pattern = "com\\.iqiyi\\.vip\\.uitls\\.task\\..*")
    ,@ComponentScan.Filter(type=FilterType.REGEX,pattern = "com\\.iqiyi\\.vip\\.uitls\\.utils\\..*")}
)
@Import({DynamicDataSourceRegister.class})
@SpringBootApplication
@EnableDiscoveryClient
public class WorkerApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(WorkerApplication.class, args);
    }

}
