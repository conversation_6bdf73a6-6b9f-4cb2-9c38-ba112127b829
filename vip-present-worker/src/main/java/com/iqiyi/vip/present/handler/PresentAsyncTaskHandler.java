package com.iqiyi.vip.present.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

import com.iqiyi.vip.present.model.PresentAsyncTask;
import com.iqiyi.vip.present.mysqlio.CanalEvent;
import com.iqiyi.vip.present.producer.PresentAsyncTaskMsgSender;
import com.iqiyi.vip.present.utils.CanalEventUtil;

/**
 * Created at: 2022-04-13
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PresentAsyncTaskHandler extends AbstractEventHandler<PresentAsyncTask> {

    @Autowired(required = false)
    PresentAsyncTaskMsgSender presentAsyncTaskMsgSender;

    @Override
    protected CanalEvent<PresentAsyncTask> getConcreteType(String event) {
        return JSON.parseObject(event, new TypeReference<CanalEvent<PresentAsyncTask>>() {});
    }

    @Override
    protected boolean childAccept(CanalEvent<PresentAsyncTask> event) {
        if (CanalEventUtil.isInsert(event.getEventType())) {
            return true;
        }
        Integer beforeInQueueValue = event.getRowBefore().getInQueue();
        Integer afterInQueueValue = event.getRowAfter().getInQueue();
        Integer beforeExecCount = event.getRowBefore().getExecCount();
        Integer afterExecCount = event.getRowAfter().getExecCount();
        //任务状态由在队列中变为不在队列且执行次数发生变化时才需要处理异步任务
        return PresentAsyncTask.TASK_IN_QUEUE_YES == beforeInQueueValue
            && PresentAsyncTask.TASK_IN_QUEUE_NO == afterInQueueValue
            && !Objects.equals(beforeExecCount, afterExecCount);
    }

    @Override
    protected void doHandleEvent(CanalEvent<PresentAsyncTask> event) {
        boolean sendResult = presentAsyncTaskMsgSender.doSend(event.getRowAfter());
        if (!sendResult) {
            throw new RuntimeException("send present async task msg failed");
        }
    }

    @Override
    public String getTableName() {
        return "present_async_task";
    }

}
