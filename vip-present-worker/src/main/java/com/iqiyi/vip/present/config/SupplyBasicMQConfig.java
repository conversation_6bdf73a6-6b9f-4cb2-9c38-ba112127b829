package com.iqiyi.vip.present.config;

import com.iqiyi.lego.rocketmq.core.ProducerConfigOptions;
import com.iqiyi.lego.rocketmq.core.StringRocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * <AUTHOR>
 * @date 2024/3/20
 * @apiNote
 */
@Configuration
@Profile({"!i18n"})
public class SupplyBasicMQConfig {

    @Value("${rmq.basic.vip.supply.url:}")
    private String supplyBasicUrl;
    @Value("${rmq.basic.vip.supply.topic:}")
    private String supplyBasicTopic;
    @Value("${rmq.basic.vip.supply.group:}")
    private String supplyBasicGroup;
    @Value("${rmq.basic.vip.supply.token:}")
    private String supplyBasicToken;

    @Bean(name = "supplyBasicVipRmqTemplate", initMethod = "init", destroyMethod = "destroy")
    public StringRocketMQTemplate supplyBasicVipRmqTemplate() {
        return buildStringRocketMqTemplate(supplyBasicUrl, supplyBasicTopic, supplyBasicGroup, supplyBasicToken, null);
    }

    private StringRocketMQTemplate buildStringRocketMqTemplate(String addr, String topic, String group, String token, String tag) {
        StringRocketMQTemplate rocketMQTemplate = new StringRocketMQTemplate();
        rocketMQTemplate.setNameSrvAddr(addr);
        rocketMQTemplate.setDefaultTopic(topic);
        rocketMQTemplate.setProducerGroup(group);
        rocketMQTemplate.setToken(token);
        ProducerConfigOptions producerConfigOptions = new ProducerConfigOptions();
        producerConfigOptions
                .setSendMsgTimeout(3000)
                .setCompressMsgBodyOverHowmuch(4 * 1024)
                .setRetryTimesWhenSendFailed(3)
                .setRetryTimesWhenSendAsyncFailed(2)
                .setMaxMessageSize(4 * 1024 * 1024)
                .setDefaultTags(tag);
        rocketMQTemplate.setProducerConfigOptions(producerConfigOptions);
        return rocketMQTemplate;
    }
}
