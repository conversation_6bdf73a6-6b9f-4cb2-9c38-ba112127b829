package com.iqiyi.vip.present.consumer;

import com.iqiyi.vip.present.consts.PresentConstants;
import com.iqiyi.vip.present.consts.TypeOfOrderEnum;
import com.iqiyi.vip.present.consts.VipTypeEnum;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.model.PresentConfig;
import com.iqiyi.vip.present.model.ProcessResult;
import com.iqiyi.vip.present.utils.CloudConfigUtil;
import com.iqiyi.vip.present.utils.JacksonUtils;
import com.iqiyi.vip.present.utils.PresentConfigCacheComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public abstract class BaseMessageConsumer implements BaseMessageListener<OrderMessage>{

    @Value("${DIAMOND_VIP_PIDS}")
    private String diamond_vip_pids;

    @Override
    public boolean match(OrderMessage orderMessage) {
        //过滤权益转移的订单
        if (TypeOfOrderEnum.isVipRightTransfer(orderMessage.getType())) {
            return false;
        }

        if(PresentConfigCacheComponent.configMap == null){
            log.error("BaseMessageConsumer [msgId:{}] [orderMessage:{}]规则还没有初始化", orderMessage.getMsgId(), JacksonUtils.toJsonString(orderMessage));
            return false;
        }

        String buyVipType = orderMessage.getProductSubtype();
        // 特殊处理学生会员和极速版会员 (免费付费黄金都送学生、免费付费黄金都送极速版白银会员)
        if (String.valueOf(VipTypeEnum.gold.getVipType()).equals(buyVipType)) {
            List<PresentConfig> configList = PresentConfigCacheComponent.configMap.get(buyVipType);
            for (PresentConfig presentConfig : configList) {
                if (isYouth(presentConfig.getPvipType())) {
                    return this.doMatch(orderMessage);
                }
            }
        }

        // 忽略actcode为赠送订单的消息
        if (PresentConstants.ACTCODE.equals(orderMessage.getActCode())) {
            return false;
        }
        boolean isTvDiamond = PresentConstants.TV_DIAMOND_VIP_TYPE.equals(orderMessage.getProductSubtype());
        // 忽略赠送订单的消息，可通过payType区分
        Set<Integer> effectiveNotNeedPresentPayTypeSet = CloudConfigUtil.getEffectiveNotNeedPresentPayTypeSet();
        Set<String> diamondVipPids = StringUtils.isBlank(diamond_vip_pids)?PresentConstants.DIAMOND_VIP_PIDS:Arrays.stream(diamond_vip_pids.split(",")).collect(Collectors.toSet());

        if (effectiveNotNeedPresentPayTypeSet.contains(orderMessage.getPayType()) && !diamondVipPids.contains(orderMessage.getPid()) && !isTvDiamond) {
            return false;
        }

        return this.doMatch(orderMessage);
    }

    /**
     * 赠送的会员是学生或者极速版会员
     */
    private boolean isYouth(String vipType) {
        return String.valueOf(VipTypeEnum.youth.getVipType()).equals(vipType)
                || String.valueOf(VipTypeEnum.basic.getVipType()).equals(vipType)
                || String.valueOf(VipTypeEnum.kiwi_base.getVipType()).equals(vipType);
    }

    @Override
    public ProcessResult dealOrder(OrderMessage orderMessage) throws Exception{
        return this.doDeal(orderMessage);
    }

    protected abstract boolean doMatch(OrderMessage message);

    protected abstract ProcessResult doDeal(OrderMessage message) throws Exception;

}
