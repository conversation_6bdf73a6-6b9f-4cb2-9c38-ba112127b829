package com.iqiyi.vip.present.consumer.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import com.iqiyi.vip.present.consts.*;
import com.iqiyi.vip.present.consumer.BaseMessageConsumer;
import com.iqiyi.vip.present.consumer.special.GoldPresentYouthConsumer;
import com.iqiyi.vip.present.consumer.special.PresentBasicVipHandler;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.data.PresentRulePerform;
import com.iqiyi.vip.present.handler.PresentAsyncOrderMessageHandler;
import com.iqiyi.vip.present.model.*;
import com.iqiyi.vip.present.out.PresentVipRequest;
import com.iqiyi.vip.present.producer.PresentDelayRmqProducer;
import com.iqiyi.vip.present.service.*;
import com.iqiyi.vip.present.task.PresentVipAsyncTask;
import com.iqiyi.vip.present.utils.CloudConfigUtil;
import com.iqiyi.vip.present.utils.PackagePresentVip;
import com.iqiyi.vip.present.utils.PresentConfigCacheComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
@SuppressWarnings("all")
public class OrderPresentConsumer extends BaseMessageConsumer {

    @Autowired
    private PresentConditionService presentConditionService;
    @Autowired
    private PresentPerformService presentPerformService;

    @Autowired
    private PresentOrderService presentOrderService;
    @Resource
    private PresentRulePerformComponent presentRulePerformComponent;

    @Autowired
    private GoldPresentYouthConsumer goldPresentYouthConsumer;
    @Autowired
    private PresentRecordService presentRecordService;
    @Autowired
    private PresentConfigService presentConfigService;
    @Autowired
    PresentBasicVipHandler presentBasicVipHandler;
    @Resource
    PresentRuleComponent presentRuleComponent;
    @Autowired(required = false)
    PresentDelayRmqProducer presentDelayRmqProducer;
    @Resource
    private PresentAsyncOrderMessageHandler presentAsyncOrderMessageHandler;

    @ConfigJsonValue("${present.white.uids:}")
    private List<Long> presentWhiteUids;

    @Override
    public boolean doMatch(OrderMessage message) {
        if (!PresentConstants.PAID_STATUS.equals(message.getStatus())) {
            return false;
        }
        List<PresentConfig> presentConfigList = PresentConfigCacheComponent.configMap.get(message.getProductSubtype());
        if (CollectionUtils.isEmpty(presentConfigList)) {
            return false;
        }
        for (PresentConfig presentConfig : presentConfigList) {
            if (presentConfig.getStatus() == 0 || presentConfig.getHandlerStatus() == 0) {
                continue;
            }
            if (this.checkBuyCode(message, presentConfig)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public ProcessResult doDeal(OrderMessage message) throws Exception {
        if (StringUtils.isNotBlank(message.getPartner())) {
            //处理对外合作的订单赠送逻辑，统一入库延迟处理队列再进行排序处理
            try {
                presentAsyncOrderMessageHandler.saveAsyncOrderMessage(message);
            }catch(DuplicateKeyException de){
                log.info("partner order 订单重复唯一键冲突 message:{}", JSON.toJSONString(message));
                return ProcessResult.acceptDataPresenting("对外合作订单重复唯一键冲突");
            }
            log.info("partner order 对外合作会员订单走异步排序处理逻辑 message:{}", JSON.toJSONString(message));
            return ProcessResult.acceptDataPresenting("对外合作会员订单走异步排序处理中");
        }
        return doProcess(message);
    }

    public ProcessResult doProcess(OrderMessage message) throws Exception {
        List<PresentConfig> presentConfigList = PresentConfigCacheComponent.configMap.get(message.getProductSubtype());
        log.info("[message:{}] [presentConfigList:{}]", message, JSONObject.toJSONString(presentConfigList));
        List<PresentVipAsyncTask.TaskData> taskDatas = Lists.newArrayList();
        for (PresentConfig presentConfig : presentConfigList) {
            if (presentConfig.getStatus() == 0 || presentConfig.getHandlerStatus() == 0) {
                continue;
            }

            //优化ToDo，去除此处查询验证，依赖后续的唯一键冲突验证，减少数据库查询
//            if (checkProcessed(message, presentConfig)) {
//                continue;
//            }

            if (!this.checkBuyCode(message, presentConfig)) {
                continue;
            }

            if (!presentConfig.checkValid(message.getPayTime()) &&
                    (presentWhiteUids == null || !presentWhiteUids.contains(message.getUid()))) {
                log.info("presentConfig is not valid,config:{}", presentConfig);
                continue;
            }

            // 学生会员特殊处理
            if (String.valueOf(VipTypeEnum.gold.getVipType()).equals(message.getProductSubtype())
                    && String.valueOf(VipTypeEnum.youth.getVipType()).equals(presentConfig.getPvipType())) {
                log.info("[msgId:{}] [presentConfig:{}] [goldPresentYouth]", message.getMsgId(), JSONObject.toJSONString(presentConfig));
                goldPresentYouthConsumer.goldPresentYouth(message);
                continue;
            }
            //赠送极速版会员特殊处理、或tv基础会员
            if (String.valueOf(VipTypeEnum.basic.getVipType()).equals(presentConfig.getPvipType())
                    || String.valueOf(VipTypeEnum.kiwi_base.getVipType()).equals(presentConfig.getPvipType())) {
                if (!PresentConstants.ACTCODE.equals(message.getActCode())) {
                    log.info("[msgId:{}] [presentConfig:{}] [presentBasic]", message.getMsgId(), JSONObject.toJSONString(presentConfig));
                    presentBasicVipHandler.presentBasicVip(message, presentConfig);
                }
                continue;
            }

            //钻石会员查询pid过滤
            if (String.valueOf(VipTypeEnum.diamonds.getVipType()).equals(message.getProductSubtype())) {
                if (!PresentConfigCacheComponent.diamondPids.contains(message.getPid())) {
                    log.info("diamond pid is not valid,pid:{},message:{}", message.getPid(), message);
                    continue;
                }
            }

            PresentRulePerform rulePerform = presentRulePerformComponent.checkRulePerform(message, presentConfig);
            if (rulePerform == null) {
                continue;
            }

            //构造插入订单和赠送任务数据
            taskDatas.add(getPresentOrderTask(message, presentConfig, rulePerform.getPerform(), rulePerform.getCondition()));
        }

        if (taskDatas.isEmpty()) {
            log.info("没有符合条件的赠送配置，无需赠送 message:{}", JSON.toJSONString(message));
            return ProcessResult.noNeedPresent("没有符合条件的赠送配置，无需赠送");
        }

        // 保存present_order,present_record记录,送会员
        presentOrderService.saveOrderPresentAndTask(taskDatas);

        if (CloudConfigUtil.needProcessKiwiPresentGoldOrderRight()) {
            processKiwiPresentGoldOrder(taskDatas, message);
        }

        return ProcessResult.acceptDataPresenting("接收数据成功，处理中");
    }

    private void processKiwiPresentGoldOrder(List<PresentVipAsyncTask.TaskData> taskData, OrderMessage message) {
        boolean match = taskData.stream().anyMatch(taskData1 -> isKiwiPresentGoldOrder(taskData1));
        if (!match) {
            return;
        }

        presentDelayRmqProducer.doSend(message);
    }

    private boolean isKiwiPresentGoldOrder(PresentVipAsyncTask.TaskData taskData) {
        PresentOrder presentOrder = taskData.getPresentOrder();
        if (taskData == null || presentOrder == null || taskData.getRequest() == null) {
            return false;
        }
        Long configId = CloudConfigUtil.sendKiwiPresentGoldRightConfigId();
        return String.valueOf(VipTypeEnum.kiwi.getVipType()).equals(presentOrder.getBuyType())
                && String.valueOf(VipTypeEnum.gold.getVipType()).equals(presentOrder.getPresentType())
                && configId.equals(presentOrder.getPresentConfigId())
                && EnumOrderStatusCode.NOT_RECEIVE.getCode().equals(presentOrder.getStatus());

    }

    private PresentVipAsyncTask.TaskData getPresentOrderTask(OrderMessage message,
                                                             PresentConfig presentConfig, PresentPerform presentPerform, PresentCondition presentCondition) {

        PresentVipRequest request = PackagePresentVip.packagePresentVipRequest(message, presentConfig);
        log.info("[message:{}] [present-vip-request:{}]", message, JSONObject.toJSONString(request));

        Integer status = this.getOrderStatus(message, presentConfig, presentPerform);
        PresentOrder presentOrder = PackagePresentVip.packagePresentOrderRequest(message, presentConfig,
                status, null, request, EnumOrderTypeCode.YES.getCode());
        presentOrder.setPresentConditionId(presentCondition.getId());
        presentOrder.setPresentPerformId(presentPerform.getId());

        if (presentPerform.getAmount() != null) {
            presentOrder.setProductAmount(presentPerform.getAmount());
        }
        log.info("[presentOrder:{}]", JSONObject.toJSONString(presentOrder));

        PresentVipAsyncTask.TaskData taskData = new PresentVipAsyncTask.TaskData();
        taskData.setRequest(request);
        taskData.setPresentOrder(presentOrder);
        return taskData;
    }

    /**
     * 获取订单的状态
     */
    protected Integer getOrderStatus(OrderMessage message, PresentConfig presentConfig, PresentPerform presentPerform) {
        Integer status = EnumOrderStatusCode.NO_PRESENT.getCode();
        if (EnumVipDirectlySendCode.RECEIVE_EVERY.getCode().equals(presentPerform.getIsDirectlySend())) {
            status = EnumOrderStatusCode.NOT_RECEIVE.getCode();
        }
        // 一组的判断如果组内领取过一次,则直接赠送
        else if (EnumVipDirectlySendCode.RECEIVE_ONE.getCode().equals(presentPerform.getIsDirectlySend())) {
            //组装在一个group里的买赠关系，构建一个recordList，判断是否都领取过
            List<PresentConfig> configListByGroup = presentConfigService.queryConfigListByGroup(presentConfig.getGrouping());
            boolean flag = presentRecordService.queryByGroup(configListByGroup, message.getUid());
            if (!flag) {
                status = EnumOrderStatusCode.NOT_RECEIVE.getCode();
            }
        }
        return status;
    }

    /**
     * 校验是否已经处理过
     */
    protected boolean checkProcessed(OrderMessage message, PresentConfig presentConfig) {
        PresentOrder presentOrderRequest = new PresentOrder();
        presentOrderRequest.setOrderCode(message.getOrderCode());
        presentOrderRequest.setUid(message.getUid());
        presentOrderRequest.setPresentConfigId(presentConfig.getId());
        List<PresentOrder> list = presentOrderService.queryOrderByParams(presentOrderRequest);
        if (CollectionUtils.isNotEmpty(list)) {
            log.info("[message:{}] 此订单已经处理过了 ", message);
            return true;
        }
        return false;
    }

    private Boolean checkBuyCode(OrderMessage orderMessage, PresentConfig presentConfig) {
        String buyCode = presentConfig.getBuyCode();
        // 兼容buycode为空的
        if (StringUtils.isBlank(buyCode) || buyCode.equals(orderMessage.getPid())) {
            return true;
        }
        // 学生会员特殊处理
        if (String.valueOf(VipTypeEnum.gold.getVipType()).equals(orderMessage.getProductSubtype())
                && String.valueOf(VipTypeEnum.youth.getVipType()).equals(presentConfig.getPvipType())) {
            return true;
        }
        return false;
    }

}

