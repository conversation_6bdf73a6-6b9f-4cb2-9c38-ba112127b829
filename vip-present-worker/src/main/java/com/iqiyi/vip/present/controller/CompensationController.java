package com.iqiyi.vip.present.controller;

import com.iqiyi.vip.present.apiresponse.BaseResponse;
import com.iqiyi.vip.present.consts.EnumResultCode;
import com.iqiyi.vip.present.consumer.BaseMessageListener;
import com.iqiyi.vip.present.consumer.impl.OrderCancelConsumer;
import com.iqiyi.vip.present.consumer.impl.OrderPresentConsumer;
import com.iqiyi.vip.present.consumer.impl.VodOrderPresentConsumer;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.enums.CompensationType;
import com.iqiyi.vip.present.model.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 补偿处理Controller
 * 为vip-present-web提供HTTP接口来处理补偿逻辑
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
@RestController
@RequestMapping("/compensation")
@Slf4j
public class CompensationController {

    @Resource
    private OrderPresentConsumer orderPresentConsumer;

    @Resource
    private OrderCancelConsumer orderCancelConsumer;

    @Resource
    private VodOrderPresentConsumer vodOrderPresentConsumer;

    /**
     * 通用补偿处理接口
     * 根据策略类型自动选择对应的处理器
     */
    @PostMapping("/handle")
    public BaseResponse<String> handleCompensation(@RequestBody OrderMessage orderMessage,
                                                  @RequestParam String strategyType) {
        try {
            log.info("开始处理补偿, strategyType: {}, orderMessage: {}", strategyType, orderMessage);

            // 1. 验证策略类型
            CompensationType compensationType = validateStrategyType(strategyType);
            if (compensationType == null) {
                return createNotMatchResponse("未知的策略类型: " + strategyType);
            }

            // 2. 获取处理器
            BaseMessageListener<OrderMessage> handler = getHandlerByCompensationType(compensationType);
            if (handler == null) {
                log.warn("未找到对应的处理器, compensationType: {}", compensationType);
                return createNotMatchResponse("未找到对应的处理类");
            }

            // 3. 检查是否匹配处理条件 - 提前返回不匹配的情况
            if (!handler.match(orderMessage)) {
                log.info("订单不匹配处理条件, compensationType: {}, orderCode: {}", compensationType, orderMessage.getOrderCode());
                return createNotMatchResponse("不符合处理类的匹配逻辑");
            }

            // 4. 执行处理逻辑
            return processCompensation(handler, compensationType, orderMessage);

        } catch (Exception e) {
            log.error("补偿处理失败, strategyType: {}, orderMessage: {}", strategyType, orderMessage, e);
            return BaseResponse.createParamError("补偿处理失败: " + e.getMessage());
        }
    }

    /**
     * 验证策略类型
     */
    private CompensationType validateStrategyType(String strategyType) {
        try {
            return CompensationType.fromCode(strategyType);
        } catch (IllegalArgumentException e) {
            log.warn("未知的策略类型: {}", strategyType);
            return null;
        }
    }

    /**
     * 创建不匹配响应
     */
    private BaseResponse<String> createNotMatchResponse(String message) {
        BaseResponse<String> response = new BaseResponse<>(EnumResultCode.NOT_MATCH);
        response.setData(message);
        return response;
    }

    /**
     * 处理补偿逻辑
     */
    private BaseResponse<String> processCompensation(BaseMessageListener<OrderMessage> handler,
                                                   CompensationType compensationType,
                                                   OrderMessage orderMessage) throws Exception {
        ProcessResult result = handler.dealOrder(orderMessage);

        // 处理null结果
        if (result == null) {
            log.error("补偿处理返回null结果, compensationType: {}, orderCode: {}", compensationType, orderMessage.getOrderCode());
            return BaseResponse.createParamError("补偿处理返回null结果");
        }

        // 处理成功结果
        if (result.isSuccess()) {
            log.info("补偿处理成功, compensationType: {}, orderCode: {}", compensationType, orderMessage.getOrderCode());
            return BaseResponse.createSuccess("补偿处理成功");
        }

        // 处理其他状态码
        log.info("补偿处理返回特定状态, compensationType: {}, orderCode: {}, resultCode: {}, message: {}",
            compensationType, orderMessage.getOrderCode(), result.getResultCode().getCode(), result.getMessage());
        BaseResponse<String> response = new BaseResponse<>(result.getResultCode());
        response.setData(result.getMessage());
        return response;
    }

    /**
     * 根据CompensationType获取对应的处理器
     */
    private BaseMessageListener<OrderMessage> getHandlerByCompensationType(CompensationType compensationType) {
        switch (compensationType) {
            case ORDER_PRESENT:
                return orderPresentConsumer;
            case ORDER_CANCEL:
                return orderCancelConsumer;
            case VOD_ORDER_PRESENT:
                return vodOrderPresentConsumer;
            default:
                return null;
        }
    }
}
