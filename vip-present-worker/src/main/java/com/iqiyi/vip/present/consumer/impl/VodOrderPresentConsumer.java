package com.iqiyi.vip.present.consumer.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.iqiyi.vip.present.api.QiyueApi;
import com.iqiyi.vip.present.consts.EnumOrderTypeCode;
import com.iqiyi.vip.present.consts.PresentConstants;
import com.iqiyi.vip.present.dao.PresentSinglePidDao;
import com.iqiyi.vip.present.data.MultilingualData;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.data.PresentRulePerform;
import com.iqiyi.vip.present.model.*;
import com.iqiyi.vip.present.out.PresentVipRequest;
import com.iqiyi.vip.present.service.PresentOrderService;
import com.iqiyi.vip.present.service.PresentRulePerformComponent;
import com.iqiyi.vip.present.task.PresentVipAsyncTask;
import com.iqiyi.vip.present.utils.JacksonUtils;
import com.iqiyi.vip.present.utils.PackagePresentVip;
import com.iqiyi.vip.present.utils.PresentConfigCacheComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Profile({"!i18n"})
public class VodOrderPresentConsumer extends OrderPresentConsumer {
    /**
     * 单点vipTpye
     */
    public static final String VIPTYPE_VOD_SINGLE = "single";

    @Autowired
    private PresentSinglePidDao presentSinglePidDao;
    @Autowired
    private PresentOrderService presentOrderService;
    @Autowired
    private QiyueApi qiyueApi;
    @Resource
    private PresentRulePerformComponent presentRulePerformComponent;

    @Override
    public boolean doMatch(OrderMessage message) {
        if (!PresentConstants.PAID_STATUS.equals(message.getStatus()) || !checkVodOrder(message.getPid())) {
            return false;
        }
        //map主键是bvip 不能为null 所以需要配置一个常量给单点
        List<PresentConfig> presentConfigList = PresentConfigCacheComponent.configMap.get(VIPTYPE_VOD_SINGLE);
        for (PresentConfig presentConfig : presentConfigList) {
            if (presentConfig.getStatus() == 0 || presentConfig.getHandlerStatus() == 0) {
                continue;
            }
            return true;
        }
        return false;
    }

    @Override
    public ProcessResult doDeal(OrderMessage message) {
        List<PresentConfig> presentConfigList = PresentConfigCacheComponent.configMap.get(VIPTYPE_VOD_SINGLE);
        log.info("[message:{}] [presentConfigList:{}]", message, JacksonUtils.toJsonString(presentConfigList));
        List<PresentVipAsyncTask.TaskData> taskDatas = Lists.newArrayList();
        if (null == presentConfigList || 0 == presentConfigList.size()) {
            return ProcessResult.noNeedPresent("配置列表为空，无需赠送");
        }
        /* 单点买赠只能配置一个config 因为赠送的产品和配置无关 不可重复赠送 */
        PresentConfig presentConfig = presentConfigList.get(0);
        if (presentConfig.getStatus() == 0 || presentConfig.getHandlerStatus() == 0) {
            return ProcessResult.noNeedPresent("配置状态无效，无需赠送");
        }
        if (checkProcessed(message, presentConfig)) {
            return ProcessResult.noNeedPresent("订单已处理过，无需赠送");
        }
        if (!presentConfig.checkValid(message.getPayTime())) {
            log.info("presentConfig is not valid,config:{}", presentConfig);
            return ProcessResult.noNeedPresent("配置时间无效，无需赠送");
        }
//        //条件匹配
//        PresentCondition targetCondition = presentRuleComponent.getTargetCondition(message, presentConfig);
//        if (targetCondition == null) {
//            log.info("[条件无效，什么也不做,条件:empty]");
//            return;
//        }
//        //找到执行规则
//        PresentPerform presentPerform = PresentConfigCacheComponent.performMap.get(targetCondition.getPerformId());
//        if (presentPerform == null || EnumPerformStatusCode.NONE.getCode().equals(presentPerform.getStatus())) {
//            log.info("[执行规则无效，什么也不做,执行规则:{}]", targetCondition.getPerformId());
//            return;
//        }
        PresentRulePerform rulePerform = presentRulePerformComponent.checkRulePerform(message, presentConfig);
        if (rulePerform == null) {
            return ProcessResult.noNeedPresent("规则执行器为空，无需赠送");
        }
        //构造插入订单和赠送任务数据
        List<MultilingualData> multilingualDatas = qiyueApi.getMultilingualData(message);
        log.info("[multilingual data][order:][{}][data:][{}]", message.getOrderCode(),
                JSON.toJSONString(multilingualDatas));
        if (null == multilingualDatas || 0 == multilingualDatas.size()) {
            return ProcessResult.noNeedPresent("多语言数据为空，无需赠送");
        }
        for (MultilingualData d : multilingualDatas) {
            taskDatas.add(getPresentOrderTask(message, presentConfig, rulePerform.getPerform(), rulePerform.getCondition(), d));
        }
        // 保存present_order,present_record记录,送会员
        presentOrderService.saveOrderPresentAndTask(taskDatas);

        return ProcessResult.acceptDataPresenting("接收数据成功，处理中");
    }

    private Boolean checkVodOrder(String pid) {
        Boolean result = Boolean.FALSE;
        if (!StringUtils.isBlank(pid)) {
            PresentSinglePid p = presentSinglePidDao.getPresentSinglePidMapper().selectByPid(pid);
            result = (null == p) ? Boolean.FALSE : Boolean.TRUE;
        }
        log.info("[check vod order][pid:][{}][result:][{}]", pid, result);
        return result;
    }

    private PresentVipAsyncTask.TaskData getPresentOrderTask(OrderMessage message,
                                                             PresentConfig presentConfig,
                                                             PresentPerform presentPerform,
                                                             PresentCondition presentCondition,
                                                             MultilingualData data) {

        PresentVipRequest request = PackagePresentVip.packageVodPresentVipRequest(message, presentConfig, data);
        log.info("[message:{}] [present-vip-request:{}]", message, JacksonUtils.toJsonString(request));
        Integer status = this.getOrderStatus(message, presentConfig, presentPerform);
        PresentOrder presentOrder = PackagePresentVip.packageVodPresentOrderRequest(message, presentConfig, status,
                null, request, EnumOrderTypeCode.YES.getCode());
        presentOrder.setPresentConditionId(presentCondition.getId());
        presentOrder.setPresentPerformId(presentPerform.getId());
        log.info("[presentOrder:{}]", JacksonUtils.toJsonString(presentOrder));

        PresentVipAsyncTask.TaskData taskData = new PresentVipAsyncTask.TaskData();
        taskData.setRequest(request);
        taskData.setPresentOrder(presentOrder);
        return taskData;
    }
}
