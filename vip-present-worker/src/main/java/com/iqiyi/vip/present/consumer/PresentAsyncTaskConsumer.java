package com.iqiyi.vip.present.consumer;

import com.google.common.base.Charsets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.present.dao.PresentAsyncTaskDao;
import com.iqiyi.vip.present.exception.CustomJsonException;
import com.iqiyi.vip.present.model.PresentAsyncTask;
import com.iqiyi.vip.present.task.async.Task;
import com.iqiyi.vip.present.utils.JacksonUtils;
import com.iqiyi.vip.present.utils.TaskRecovery;

/**
 * Created at: 2022-04-13
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PresentAsyncTaskConsumer implements MessageListenerConcurrently {

    @Resource
    PresentAsyncTaskDao presentAsyncTaskDao;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt messageExt : msgs) {
            StopWatch stopWatch = StopWatch.createStarted();
            String msgBody = new String(messageExt.getBody(), Charsets.UTF_8);
            try {
                PresentAsyncTask asyncTask = JacksonUtils.parseObject(messageExt.getBody(), PresentAsyncTask.class);
                handleOneMsg(messageExt.getMsgId(), asyncTask);
            } catch (Exception e) {
                log.error("[{} deal error] [topic:{}] [msgId:{}] [msgBody:{}] [cost:{}]", getClass().getSimpleName(),
                    messageExt.getTopic(), messageExt.getMsgId(), msgBody, stopWatch.getTime(), e);
                if (e instanceof CustomJsonException) {
                    continue;
                }
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
            log.info("[{} deal end] [topic:{}] [msgId:{}] [msgBody:{}] [cost:{}]", getClass().getSimpleName(),
                messageExt.getTopic(), messageExt.getMsgId(), msgBody, stopWatch.getTime());
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private void handleOneMsg(String msgId, PresentAsyncTask asyncTask) {
        boolean processing = presentAsyncTaskDao.makeTaskProcessing(asyncTask.getId());
        if (!processing) {
            log.warn("PresentAsyncTask is processing, msgId:{} primaryKey:{} taskId:{}", msgId, asyncTask.getId(), asyncTask.getTaskId());
            return;
        }
        Task task = TaskRecovery.recoverTask(asyncTask, presentAsyncTaskDao);
        if (task == null) {
            //重置任务状态，以便消息重试时，任务可以继续执行
            presentAsyncTaskDao.makeTaskUnProcess(asyncTask.getId());
            throw new RuntimeException("recoverTask failed, retry later. taskId:" + asyncTask.getTaskId());
        }

        try {
            task.run();
        } catch (Exception e) {
            log.error("async task execute error!, asyncTask:{}", asyncTask, e);
        }
    }
}
