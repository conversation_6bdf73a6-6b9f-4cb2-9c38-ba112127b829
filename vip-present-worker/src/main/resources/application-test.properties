# \u547D\u540D\u683C\u5F0F\uFF1A\u5E94\u7528\u540D-test
spring.application.name=vip-present-worker-test
server.port=8080

eureka.instance.hostname=${HOST}
eureka.instance.non-secure-port=${PORT_8080}
eureka.instance.instance-id=${eureka.instance.hostname}:${eureka.instance.non-secure-port}
# \u79DF\u671F\u66F4\u65B0\u65F6\u95F4\u95F4\u9694\uFF08\u9ED8\u8BA430\u79D2\uFF09
eureka.instance.lease-renewal-interval-in-seconds=5
# \u79DF\u671F\u5230\u671F\u65F6\u95F4\uFF08\u9ED8\u8BA490\u79D2\uFF09
eureka.instance.lease-expiration-duration-in-seconds=10
# \u662F\u5426\u6CE8\u518C\u5230\u6CE8\u518C\u4E2D\u5FC3\uFF0C\u5982\u679C\u4E0D\u9700\u8981\u53EF\u4EE5\u8BBE\u7F6E\u4E3Afalse
eureka.client.register-with-eureka=true
# \u6CE8\u518C\u4E2D\u5FC3\u914D\u7F6E
eureka.client.serviceUrl.defaultZone=http://************:8080/eureka/
# \u5F00\u542F\u5065\u5EB7\u68C0\u67E5\uFF08\u9700\u8981spring-boot-starter-actuator\u4F9D\u8D56\uFF09
eureka.client.healthcheck.enabled=true
spring.cloud.netflix.metrics.enabled=false

# actuator config
endpoints.health.sensitive=false
endpoints.enabled=false
management.endpoints.web.exposure.include=health,info
management.endpoints.web.exposure.exclude=
management.security.enabled=true
management.health.defaults.enabled=true
management.health.mail.enabled=false
management.health.redis.enabled=false
management.health.eureka.enabled=false
management.endpoint.health.show-details=always
management.info.git.mode=full

spring.shardingsphere.datasource.names=ds
spring.shardingsphere.datasource.ds.type=com.zaxxer.hikari.HikariDataSource
spring.shardingsphere.datasource.ds.driver-class-name=com.mysql.jdbc.Driver
spring.shardingsphere.datasource.ds.jdbc-url=**********************************************************************************************************************************
spring.shardingsphere.datasource.ds.username=vipqa_iqiyi
spring.shardingsphere.datasource.ds.password=vipqa@IQIYI
spring.shardingsphere.datasource.ds.connection-test-query=select NOW()
spring.shardingsphere.datasource.ds.connection-timeout=30000
spring.shardingsphere.datasource.ds.idle-timeout=60000
spring.shardingsphere.datasource.ds.max-lifetime=18000


spring.shardingsphere.sharding.default-data-source-name=ds

spring.shardingsphere.sharding.tables.present_order.actual-data-nodes=ds.present_order_$->{0..19}
spring.shardingsphere.sharding.tables.present_order.table-strategy.standard.sharding-column=uid
spring.shardingsphere.sharding.tables.present_order.table-strategy.standard.precise-algorithm-class-name=com.iqiyi.vip.present.sharding.HashPreciseShardingAlgorithm

spring.shardingsphere.sharding.tables.present_record.actual-data-nodes=ds.present_record_$->{0..19}
spring.shardingsphere.sharding.tables.present_record.table-strategy.standard.sharding-column=buy_uid
spring.shardingsphere.sharding.tables.present_record.table-strategy.standard.precise-algorithm-class-name=com.iqiyi.vip.present.sharding.HashPreciseShardingAlgorithm

spring.shardingsphere.sharding.tables.supply_present_record.actual-data-nodes=ds.supply_present_record_${def tmp=[];(0..99).each {e->tmp.add(String.format("%02d",e))};return tmp}
spring.shardingsphere.sharding.tables.supply_present_record.table-strategy.standard.sharding-column=uid
spring.shardingsphere.sharding.tables.supply_present_record.table-strategy.standard.precise-algorithm-class-name=com.iqiyi.vip.present.sharding.HashPreciseShardingAlgorithm


hystrix.apollo.metrics.projectId=108977
hystrix.apollo.metrics.projectName=felixsun
hystrix.apollo.metrics.cluster=BJDXT9_CLUSTER1

spring.jpa.show-sql=true

###########################################################
# other setting
###########################################################
debug=false

bizsource.map.mbd_views=mbd_views_test
bizsource.map.giftcard=giftcard_test
bizsource.map.vippresent=taskcenter_test

limit.config.fileName=springmvcLimitConfigTest

metrics.http.switch=false

######send score###########
send.score.url=http://*************/growth-api/apis/score/add
######send score end#######
sign.in.url=http://************:9012/taskCenter/internal/userSign
internal.pay.key=123456
internal.growth.key=1df3ec6fa2
qiyue.refund.sign=1c8c1d2cbfe5e5695476aacf3ad52f96
qiyue.refund.serviceCode=lyksc7aq36aedndk

vinfo.get.url=http://vip-info-server-test/internal/vip_users
######## send vip end ########
#refund.url=http://VIPTRADE-REFUNDSERVICE-API-TEST/refundService/order/partnerRefundOrder.action

refund.url=http://VIPTRADE-REFUNDSERVICE-API-TEST/refundService/order/common/refund
refund.url.lb=true
######## send vip ########
dopay.url=http://VIPTRADE-FREE-ORDER-TEST/api/internal/free-pay/dopay.action
dopay.url.lb=true
consumer.appname=vip-present-worker

gold.present.youth=919a2ea53ffa9779
vipTradeSdkSignKey=989c12e2b1772218b29ba025248eaf94
tradeapiServerUrl=http://tradeapi.vip.qiyi.domain

############# task cluster config start ###############
zookeeper.url=cnhb4.public-test-baoding.dev002.zk.qiyi.middle:2181,cnhb4.public-test-baoding.dev003.zk.qiyi.middle:2181,cnhb4.public-test-baoding.dev005.zk.qiyi.middle:2181,cnhb4.public-test-baoding.dev001.zk.qiyi.middle:2181,cnhb4.public-test-baoding.dev004.zk.qiyi.middle:2181
zookeeper.user=
zookeeper.password=

async.task.execute=true
async.task.execute.cluster=true
async.task.zookeeper.root=/present-task
async.task.poolType.exe=1,2,3,4
#async.task.pooltype.failover=4

async.task.pool.1.name=PresentVipAsyncTask
async.task.pool.1.corePoolSize=4
async.task.pool.1.maximumPoolSize=8
async.task.pool.1.workQueueNum=1000

async.task.pool.2.name=RefundVipAsyncTask
async.task.pool.2.corePoolSize=6
async.task.pool.2.maximumPoolSize=10
async.task.pool.2.workQueueNum=1000

async.task.pool.3.name=SendVipTask
async.task.pool.3.corePoolSize=6
async.task.pool.3.maximumPoolSize=10
async.task.pool.3.workQueueNum=1000

async.task.pool.4.name=PRESENT_ASYNC_ORDER_MESSAGE
async.task.pool.4.corePoolSize=5
async.task.pool.4.maximumPoolSize=10
async.task.pool.4.workQueueNum=1000
############# task cluster config end #################
file_name=qiyue_core_config_test



# product rmq
rocketmq.consumer.product.groupname=CG-OCM_OSS_EXPCARD_DEV_CONSUMER_GROUP
rocketmq.consumer.product.address=service-cloud-dc-research-rocketmq-dev001-whdx.qiyi.virtual:9876;service-cloud-dc-research-rocketmq-dev003-whdx.qiyi.virtual:9876
rocketmq.consumer.product.token=CT-7b097983-d7a7-4552-8db8-cc68205d9917
rocketmq.consumer.product.topics=vipProduct
rocketmq.consumer.product.tags=*
rocketmq.consumer.product.consumethreadmin=20
rocketmq.consumer.product.consumethreadmax=32
rocketmq.consumer.product.consumemessagebatchmaxsize=1
#order finish
rocketmq.consumer.orderfinish.groupname=CG-vip_present_msg_order_fulfill_finished
rocketmq.consumer.orderfinish.address=dc-resource-fb597f31-10.qiyi.virtual:9876;dc-resource-fb597f31-8.qiyi.virtual:9876
rocketmq.consumer.orderfinish.token=CT-d341ca1b-e877-438f-a1b8-76a10a3b058b
rocketmq.consumer.orderfinish.topics=vip_trade_msg_order_fulfill_finished
rocketmq.consumer.orderfinish.tags=*
rocketmq.consumer.orderfinish.consumethreadmin=20
rocketmq.consumer.orderfinish.consumethreadmax=32
rocketmq.consumer.orderfinish.consumemessagebatchmaxsize=1
#single pid
rocketmq.consumer.single-pid.groupname=CG-singleProduct-present
rocketmq.consumer.single-pid.address=service-cloud-dc-research-rocketmq-dev001-whdx.qiyi.virtual:9876;service-cloud-dc-research-rocketmq-dev003-whdx.qiyi.virtual:9876
rocketmq.consumer.single-pid.token=CT-40f19b4d-4862-4c3b-9aaf-cdbbcea29ebc
rocketmq.consumer.single-pid.topics=singleProduct
rocketmq.consumer.single-pid.tags=*
rocketmq.consumer.single-pid.consumethreadmin=20
rocketmq.consumer.single-pid.consumethreadmax=32
rocketmq.consumer.single-pid.consumemessagebatchmaxsize=1
#presentOrder rmq
rmq.present.delay.prod.address=hb-az-4.bj-public-test.dev001.rocketmq.qiyi.middle:9876;hb-az-4.bj-public-test.dev002.rocketmq.qiyi.middle:9876
rmq.present.delay.prod.groupname=PG-vip_present_delay_order
rmq.present.delay.prod.token=PT-852ac78c-f6cd-42b3-a7c7-c819aec50549

rmq.present.delay.consumer.address=hb-az-4.bj-public-test.dev001.rocketmq.qiyi.middle:9876;hb-az-4.bj-public-test.dev002.rocketmq.qiyi.middle:9876
rmq.present.delay.consumer.groupname=CG-vip_present_delay_order
rmq.present.delay.consumer.token=CT-1934397c-87e2-42dc-b459-14d84eb2fa79
rmq.present.delay.consumer.topics=vip_present_delay_order

#right transfer order finished rmq
rmq.right.transfer.order.finished.consumer.address=dc-resource-fb597f31-10.qiyi.virtual:9876;dc-resource-fb597f31-8.qiyi.virtual:9876
rmq.right.transfer.order.finished.consumer.groupname=CG-vip_present_right_transfer_order_finished
rmq.right.transfer.order.finished.consumer.token=CT-79a6533a-d309-41fc-921b-5c09c5b8ebad
rmq.right.transfer.order.finished.consumer.topics=vip_trade_msg_right_transfer_order_finished

#exist gitv user need present order rmq
exist.gitv.need.present.order.consumer.address=dc-resource-fb597f31-10.qiyi.virtual:9876;dc-resource-fb597f31-8.qiyi.virtual:9876
exist.gitv.need.present.order.consumer.groupname=CG-viptrade_exist_gitv_need_present_order
exist.gitv.need.present.order.consumer.token=CT-7fe7072e-2b0d-479c-a4b2-17959c8036e3
exist.gitv.need.present.order.consumer.topics=exist_gitv_user_need_present_order

#present async task binlog consumer rmq
present.async.task.binlog.consumer.address=dc-resource-fb597f31-10.qiyi.virtual:9876;dc-resource-fb597f31-8.qiyi.virtual:9876
present.async.task.binlog.consumer.groupname=CG-present_async_task_binlog
present.async.task.binlog.consumer.token=CT-ab6de8d0-006e-4bdf-95a0-df755a485a58
present.async.task.binlog.consumer.topics=present_async_task_binlog

#present async task producer rmq
present.async.task.producer.address=dc-resource-fb597f31-10.qiyi.virtual:9876;dc-resource-fb597f31-8.qiyi.virtual:9876
present.async.task.producer.groupname=PG-present_async_task
present.async.task.producer.token=PT-a5c5f6d2-c49a-400e-ac20-d33a27345a5e

#present async task consumer rmq
present.async.task.consumer.address=dc-resource-fb597f31-10.qiyi.virtual:9876;dc-resource-fb597f31-8.qiyi.virtual:9876
present.async.task.consumer.groupname=CG-present_async_task
present.async.task.consumer.token=CT-1d4c4d46-4408-4776-8f70-3cc9b340be92
present.async.task.consumer.topics=present_async_task

rmq.basic.vip.supply.consumer.address=hb-az-4.bj-public-test.dev001.rocketmq.qiyi.middle:9876;hb-az-4.bj-public-test.dev002.rocketmq.qiyi.middle:9876
rmq.basic.vip.supply.consumer.topics=supply_basic_vip
rmq.basic.vip.supply.consumer.groupname=CG-supply_basic_vip
rmq.basic.vip.supply.consumer.token=CT-4dad9cc4-4d8b-4231-80f6-edf752744208


present.vip.rights.grant.producer.address=dc-resource-fb597f31-10.qiyi.virtual:9876;dc-resource-fb597f31-8.qiyi.virtual:9876
present.vip.rights.grant.producer.groupname=PG-present_vip_rights_grant
present.vip.rights.grant.producer.token=PT-76c0b37f-be9f-4571-bb4d-d5bb97667036
present.vip.rights.grant.producer.topic=present_vip_rights_grant

present.vip.rights.grant.consumer.address=dc-resource-fb597f31-10.qiyi.virtual:9876;dc-resource-fb597f31-8.qiyi.virtual:9876
present.vip.rights.grant.consumer.groupname=CG-present_vip_rights_grant
present.vip.rights.grant.consumer.token=CT-7fce4ad2-a56b-48d4-bcae-804eea76caf1
present.vip.rights.grant.consumer.topics=present_vip_rights_grant

#\u57FA\u7840\u4F1A\u5458\u8865\u8D60\u6743\u76CA\u65F6\u957Frmq\u914D\u7F6E
rmq.basic.vip.supply.url=hb-az-4.bj-public-test.dev001.rocketmq.qiyi.middle:9876;hb-az-4.bj-public-test.dev002.rocketmq.qiyi.middle:9876
rmq.basic.vip.supply.topic=supply_basic_vip
rmq.basic.vip.supply.group=PG-supply_basic_vip
rmq.basic.vip.supply.token=PT-00256f89-756d-4fd6-ad84-445ee9337d76

############# present sport or book or \u5947\u9047 vip config start ##############
sport.api.key=123456789
sport.present.url=http://cooperation.ssports.com/diamond/give
sport.cancel.url=http://cooperation.ssports.com/diamond/refund

book.api.key=123456789
book.present.url=http://*************/book/monthly/presentDiamondVip
book.cancel.url=http://*************/book/monthly/cancelPresentDiamondVip
qiyu.api.key=0294716aaca12efae81cdf46a30fe7b4
qiyu.present.url=http://test.vraioapi.qiyi.domain/qiyu-vip/present
qiyu.cancel.url=http://test.vraioapi.qiyi.domain/qiyu-vip/reclaim
############# present sport or book vip config end ################
########### qiyue multilingual api start ################
qiyue.multilingual.url=http://VIP-CONTENT-QUERY-SERVER-TEST/multilingual/contents/query
qiyue.multilingual.bizSource=buy-present
qiyue.multilingual.sourceKey=111111
########### qiyue multilingual api end ################

# passport \uFFFD\uFFFD\u057E\uFFFD\u04FF\uFFFD
passport.user.info=http://passport.qiyi.domain/apis/profile/info.action
passport.url.byUid=http://passport.qiyi.domain/apis/plaintext/byUid.action

# passport \uFFFD\uFFFD\uFFFD\uFFFD\u057E\uFFFD\u04FF\uFFFD
#passport.user.info=http://intl-passport.qiyi.domain/intl/user/info.action
#passport.url.byUid=http://intl-passport.qiyi.domain/intl/inner/user/byUid.action

free.order.url=http://VIPTRADE-FREE-ORDER-TEST/api/internal/free-pay/dopay.action
free.order.signKey=123456

appEnv=test
appName=vip-present
appRegion=default

table.present.order.total.count=20

spring.redisson.clusterServers=redis://viptradetest.1.qiyi.redis:7502,redis://viptradetest.2.qiyi.redis:7502
spring.redisson.password=ip61PDObJw63

# order
order.sharding.database.urls=*****************************************************************************,*****************************************************************************
order.sharding.database.username=vip_order_test
order.sharding.database.password=agh3!schingood5TR$
order.sharding.tableSize=2