# \u547D\u540D\u683C\u5F0F\uFF1A\u5E94\u7528\u540D-online
spring.application.name=vip-present-worker-online
server.port=8080

eureka.instance.hostname=${HOST}
eureka.instance.non-secure-port=${PORT_8080}
eureka.instance.instance-id=${eureka.instance.hostname}:${eureka.instance.non-secure-port}

# \u5E94\u7528\u6240\u5728zone\uFF0C\u6839\u636E\u5E94\u7528\u673A\u623F\u586B\u5199\uFF0C\u5177\u4F53\u53C2\u8003\uFF1Ahttp://wiki.qiyi.domain/pages/viewpage.action?pageId=1193148821
eureka.instance.metadata-map.zone=zone-aws
# \u79DF\u671F\u66F4\u65B0\u65F6\u95F4\u95F4\u9694\uFF08\u9ED8\u8BA430\u79D2\uFF09
eureka.instance.lease-renewal-interval-in-seconds=5
# \u79DF\u671F\u5230\u671F\u65F6\u95F4\uFF08\u9ED8\u8BA490\u79D2\uFF09
eureka.instance.lease-expiration-duration-in-seconds=10
# \u662F\u5426\u6CE8\u518C\u5230\u6CE8\u518C\u4E2D\u5FC3\uFF0C\u5982\u679C\u4E0D\u9700\u8981\u53EF\u4EE5\u8BBE\u7F6E\u4E3Afalse
eureka.client.register-with-eureka=true
# \u5E94\u7528\u6240\u5728region\uFF0C\u5317\u4EAC\u3001\u4E2D\u7ECF\u4E91\u3001\u4E2D\u4E91\u4FE1\u673A\u623F\u586B\u5199region-bj\uFF0C\u534E\u4E2D\u673A\u623F\u586B\u5199region-hz\uFF0C\u6D77\u5916\u673A\u623F\u586B\u5199region-sea
eureka.client.region=region-sea
eureka.client.availability-zones.region-sea=zone-aws
eureka.client.service-url.zone-aws=http://aws.eureka.vip.qiyi.domain:8080/eureka

# \u5F00\u542F\u5065\u5EB7\u68C0\u67E5\uFF08\u9700\u8981spring-boot-starter-actuator\u4F9D\u8D56\uFF09
eureka.client.healthcheck.enabled=true
spring.cloud.netflix.metrics.enabled=false

# actuator config
endpoints.health.sensitive=false
endpoints.enabled=true
management.endpoints.web.exposure.include=health,info
management.endpoints.web.exposure.exclude=
management.security.enabled=true
management.health.defaults.enabled=true
management.health.mail.enabled=false
management.health.redis.enabled=false
management.health.eureka.enabled=false
management.endpoint.health.show-details=simple


###### \u5982\u679C\u9700\u8981\u91CD\u8BD5\u529F\u80FD\uFF0C\u53EF\u4EE5\u589E\u52A0\u5982\u4E0B\u914D\u7F6E #######
# \u5F00\u542F\u91CD\u8BD5\uFF0C\u9700\u8981\u6DFB\u52A0Spring-Retry\u4F9D\u8D56\uFF08https://mvnrepository.com/artifact/org.springframework.retry/spring-retry\uFF09
spring.cloud.loadbalancer.retry.enabled=true
# Ribbon\u9ED8\u8BA4\u53EA\u5BF9GET\u8BF7\u6C42\u8FDB\u884C\u91CD\u8BD5\uFF0C\u5982\u679C\u60F3\u5BF9\u6240\u6709\u64CD\u4F5C\u8BF7\u6C42\u90FD\u8FDB\u884C\u91CD\u8BD5\uFF0C\u53EF\u4EE5\u914D\u7F6E\u8BE5\u5C5E\u6027\uFF08\u5982\u679CPOST\u63A5\u53E3\u4E0D\u652F\u6301\u5E42\u7B49\u7684\u8BDD\u614E\u7528\uFF09\u4E3Atrue
ribbon.OkToRetryOnAllOperations=false
# \u5BF9\u54CD\u5E94\u4E2D\u8FD4\u56DE\u67D0\u4E9B\u72B6\u6001\u7801\u65F6\u91CD\u8BD5\u8BF7\u6C42
#ribbon.retryableStatusCodes=500,502
# \u540C\u4E00\u4E2AServer\u91CD\u8BD5\u6B21\u6570
ribbon.MaxAutoRetries=0
# \u6700\u591A\u91CD\u8BD5\u51E0\u4E2AServer
ribbon.MaxAutoRetriesNextServer=1
# \u4E5F\u53EF\u4EE5\u6309\u670D\u52A1\u63D0\u4F9B\u8005\u5206\u522B\u914D\u7F6E
# serviceId1.ribbon.MaxAutoRetries=0


spring.shardingsphere.datasource.names=ds
spring.shardingsphere.datasource.ds.type=com.zaxxer.hikari.HikariDataSource
spring.shardingsphere.datasource.ds.driver-class-name=com.mysql.jdbc.Driver
spring.shardingsphere.datasource.ds.jdbc-url=*******************************************************************************************************************************************
spring.shardingsphere.datasource.ds.username=vip_persent
spring.shardingsphere.datasource.ds.password=@9BXv72NKj
spring.shardingsphere.datasource.ds.connection-test-query=select NOW()
spring.shardingsphere.datasource.ds.connection-timeout=3000
spring.shardingsphere.datasource.ds.idle-timeout=60000
spring.shardingsphere.datasource.ds.max-lifetime=18000

spring.shardingsphere.sharding.default-data-source-name=ds
spring.shardingsphere.sharding.tables.supply_present_record.actual-data-nodes=ds.supply_present_record_${def tmp=[];(0..99).each {e->tmp.add(String.format("%02d",e))};return tmp}
spring.shardingsphere.sharding.tables.supply_present_record.table-strategy.standard.sharding-column=uid
spring.shardingsphere.sharding.tables.supply_present_record.table-strategy.standard.precise-algorithm-class-name=com.iqiyi.vip.present.sharding.HashPreciseShardingAlgorithm

spring.shardingsphere.sharding.tables.present_order.actual-data-nodes=ds.present_order_$->{0..99}
spring.shardingsphere.sharding.tables.present_order.table-strategy.standard.sharding-column=uid
spring.shardingsphere.sharding.tables.present_order.table-strategy.standard.precise-algorithm-class-name=com.iqiyi.vip.present.sharding.HashPreciseShardingAlgorithm

spring.shardingsphere.sharding.tables.present_record.actual-data-nodes=ds.present_record_$->{0..99}
spring.shardingsphere.sharding.tables.present_record.table-strategy.standard.sharding-column=buy_uid
spring.shardingsphere.sharding.tables.present_record.table-strategy.standard.precise-algorithm-class-name=com.iqiyi.vip.present.sharding.HashPreciseShardingAlgorithm



hystrix.apollo.metrics.projectId=108977
hystrix.apollo.metrics.projectName=felixsun
hystrix.apollo.metrics.cluster=BJDXT9_CLUSTER1

spring.jpa.show-sql=true

###########################################################
# other setting
###########################################################
debug=false

bizsource.map.mbd_views=aRSDpkD9aRTCypBhqd4bWJYbTDOIZOix
bizsource.map.giftcard=eAVSpkD9aRTCypBhqd4bWJYbTDOIZOix
bizsource.map.vippresent=tSNXpkD9aRTCypBhqd4bWJYbTDOIZOix

limit.config.fileName=springmvcLimitConfig

metrics.http.switch=true


internal.pay.key=1df3ec6fa2
internal.growth.key=1df3ec6fa2


qiyue.refund.sign=yjasf9ad7ea8jfa0admh2gapkh25js
qiyue.refund.serviceCode=lyksc7aq36aedndk

vinfo.get.url=http://intl-vinfo.vip.iqiyi.com/internal/vip_users

######## send vip end ########
refund.url=http://intl-qiyue.qiyi.domain/refundService/order/partnerRefundOrder.action
refund.url.lb=false
######## send vip ########
dopay.url=http://global.vip.qiyi.domain/vip-global-trade/internal/freePay/dopay
dopay.url.lb=false
consumer.appname=vip-present-worker

gold.present.youth=919a2ea53ffa9779
vipTradeSdkSignKey=b78dc854afc05d8062558b0bc22141be
tradeapiServerUrl=http://intl-tradeapi.vip.qiyi.domain

############# task cluster config start ###############
zookeeper.url=apse1.intl-vip-sea-taskcluster-v2.online001.zk.qiyi.middle:2181,apse1.intl-vip-sea-taskcluster-v2.online002.zk.qiyi.middle:2181,apse1.intl-vip-sea-taskcluster-v2.online003.zk.qiyi.middle:2181,apse1.intl-vip-sea-taskcluster-v2.online004.zk.qiyi.middle:2181,apse1.intl-vip-sea-taskcluster-v2.online005.zk.qiyi.middle:2181
zookeeper.user=intl_vip_sea_zk
zookeeper.password=intl_vip_sea_zk

async.task.execute=true
async.task.execute.cluster=true
async.task.zookeeper.root=/present-task
async.task.poolType.exe=1,2,3
#async.task.pooltype.failover=3

async.task.pool.1.name=PresentVipAsyncTask
async.task.pool.1.corePoolSize=4
async.task.pool.1.maximumPoolSize=8
async.task.pool.1.workQueueNum=1000

async.task.pool.2.name=RefundVipAsyncTask
async.task.pool.2.corePoolSize=6
async.task.pool.2.maximumPoolSize=10
async.task.pool.2.workQueueNum=1000

async.task.pool.3.name=SendVipTask
async.task.pool.3.corePoolSize=6
async.task.pool.3.maximumPoolSize=10
async.task.pool.3.workQueueNum=1000
############# task cluster config end #################
file_name=qiyue_core_config

# product rmq
rocketmq.consumer.product.groupname=CG-vip-present
rocketmq.consumer.product.address=aws-apse1-az1.intl-vip-basisdata.online001.rocketmq.qiyi.middle:9876;aws-apse1-az1.intl-vip-basisdata.online002.rocketmq.qiyi.middle:9876
rocketmq.consumer.product.token=CT-47d69986-4a66-411f-9e4a-95d296b55a42
rocketmq.consumer.product.topics=vipProduct
rocketmq.consumer.product.tags=*
rocketmq.consumer.product.consumethreadmin=20
rocketmq.consumer.product.consumethreadmax=32
rocketmq.consumer.product.consumemessagebatchmaxsize=1
#order finish
rocketmq.consumer.orderfinish.groupname=CG-vip_present_msg_order_finished
rocketmq.consumer.orderfinish.address=aws-apse1-az1.intl-vip-trade-order-msg.online001.rocketmq.qiyi.middle:9876;aws-apse1-az1.intl-vip-trade-order-msg.online002.rocketmq.qiyi.middle:9876
rocketmq.consumer.orderfinish.token=CT-f8f9eb52-6754-4d01-a29a-1d6e4cf67a5d
rocketmq.consumer.orderfinish.topics=vip_trade_msg_order_finished
rocketmq.consumer.orderfinish.tags=*
rocketmq.consumer.orderfinish.consumethreadmin=20
rocketmq.consumer.orderfinish.consumethreadmax=32
rocketmq.consumer.orderfinish.consumemessagebatchmaxsize=1
#single pid
rocketmq.consumer.singlePid.groupname=CG-present
rocketmq.consumer.singlePid.address=aws-apse1-az1.intl-vip-basisdata.online001.rocketmq.qiyi.middle:9876;aws-apse1-az1.intl-vip-basisdata.online002.rocketmq.qiyi.middle:9876
rocketmq.consumer.singlePid.token=CT-3674cca9-b73d-47a6-9bdb-60a3ea55c0ac
rocketmq.consumer.singlePid.topics=singleProduct
rocketmq.consumer.singlePid.tags=*
rocketmq.consumer.singlePid.consumethreadmin=20
rocketmq.consumer.singlePid.consumethreadmax=32
rocketmq.consumer.singlePid.consumemessagebatchmaxsize=1
########### qiyue multilingual api start ################
qiyue.multilingual.url=
qiyue.multilingual.bizSource=
qiyue.multilingual.sourceKey=
########### qiyue multilingual api end ################

# passport \uFFFD\uFFFD\uFFFD\uFFFD\u057E\uFFFD\u04FF\uFFFD
passport.user.info=http://intl-passport.qiyi.domain/intl/user/info.action
passport.url.byUid=http://intl-passport.qiyi.domain/intl/inner/user/byUid.action
passport.source=boss

appEnv=pro
appName=intl-vip-present
appRegion=intl

#\u4F1A\u5458\u76D1\u63A7\u9E70\u773C
endpoints.prometheus.sensitive=false
management.context-path=/actuator
management.port=8099

table.present.order.total.count=100