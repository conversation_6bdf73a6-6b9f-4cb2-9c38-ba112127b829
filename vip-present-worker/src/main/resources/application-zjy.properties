# \u547D\u540D\u683C\u5F0F\uFF1A\u5E94\u7528\u540D-online
spring.application.name=vip-present-worker-online
server.port=8080

eureka.instance.hostname=${HOST}
eureka.instance.non-secure-port=${PORT_8080}
eureka.instance.instance-id=${eureka.instance.hostname}:${eureka.instance.non-secure-port}

# \u5E94\u7528\u6240\u5728zone\uFF0C\u6839\u636E\u5E94\u7528\u673A\u623F\u586B\u5199
eureka.instance.metadata-map.zone=zone-zjy
# \u79DF\u671F\u66F4\u65B0\u65F6\u95F4\u95F4\u9694\uFF08\u9ED8\u8BA430\u79D2\uFF09
eureka.instance.lease-renewal-interval-in-seconds=5
# \u79DF\u671F\u5230\u671F\u65F6\u95F4\uFF08\u9ED8\u8BA490\u79D2\uFF09
eureka.instance.lease-expiration-duration-in-seconds=10
# \u662F\u5426\u6CE8\u518C\u5230\u6CE8\u518C\u4E2D\u5FC3\uFF0C\u5982\u679C\u4E0D\u9700\u8981\u53EF\u4EE5\u8BBE\u7F6E\u4E3Afalse
eureka.client.register-with-eureka=true
# \u5E94\u7528\u6240\u5728region\uFF0C\u5317\u4EAC\u3001\u4E2D\u7ECF\u4E91\u3001\u4E2D\u4E91\u4FE1\u673A\u623F\u586B\u5199region-bj\uFF0C\u534E\u4E2D\u673A\u623F\u586B\u5199region-hz\uFF0C\u6D77\u5916\u673A\u623F\u586B\u5199region-sea
eureka.client.region=region-bj
# region\u5185\u53EF\u7528zone\u5217\u8868
eureka.client.availability-zones.region-bj=zone-zjy,zone-zyx,zone-bj,zone-hz
# \u6CE8\u518C\u4E2D\u5FC3\u914D\u7F6E\uFF0Cregion-bj\u4F7F\u7528\u5982\u4E0B\u914D\u7F6E
eureka.client.service-url.zone-zjy=http://zjy.eureka.vip.qiyi.domain:8080/eureka

# \u5F00\u542F\u5065\u5EB7\u68C0\u67E5\uFF08\u9700\u8981spring-boot-starter-actuator\u4F9D\u8D56\uFF09
eureka.client.healthcheck.enabled=true
spring.cloud.netflix.metrics.enabled=false

# actuator config
endpoints.health.sensitive=false
endpoints.enabled=true
management.endpoints.web.exposure.include=health,info
management.endpoints.web.exposure.exclude=
management.security.enabled=true
management.health.defaults.enabled=true
management.health.mail.enabled=false
management.health.redis.enabled=false
management.health.eureka.enabled=false
management.endpoint.health.show-details=simple


# \u901A\u8FC7ribbon\u8FDB\u884C\u8D1F\u8F7D\u5747\u8861
spring.cloud.loadbalancer.ribbon.enabled=true
ribbon.eureka.enabled=true
# \u5F00\u542FRibbon\u7684\u9965\u997F\u52A0\u8F7D\u6A21\u5F0F\uFF0C\u51CF\u5C11\u7B2C\u4E00\u6B21\u8C03\u7528\u65F6\u7531\u4E8E\u521B\u5EFARibbonClient\u5BFC\u81F4\u7684\u8D85\u65F6\u95EE\u9898
ribbon.eager-load.enabled=true
# \u9700\u8981\u9965\u997F\u52A0\u8F7D\u7684\u670D\u52A1\u63D0\u4F9B\u8005\u7684 instace-name \u5217\u8868
ribbon.eager-load.clients=vip-info-server-online,VIPTRADE-REFUNDSERVICE-API,VIPTRADE-FREE-ORDER,VIP-CONTENT-QUERY-SERVER-ONLINE
ribbon.okhttp.enabled=true

###### \u5982\u679C\u9700\u8981\u91CD\u8BD5\u529F\u80FD\uFF0C\u53EF\u4EE5\u589E\u52A0\u5982\u4E0B\u914D\u7F6E #######
# \u5F00\u542F\u91CD\u8BD5\uFF0C\u9700\u8981\u6DFB\u52A0Spring-Retry\u4F9D\u8D56\uFF08https://mvnrepository.com/artifact/org.springframework.retry/spring-retry\uFF09
spring.cloud.loadbalancer.retry.enabled=true
# Ribbon\u9ED8\u8BA4\u53EA\u5BF9GET\u8BF7\u6C42\u8FDB\u884C\u91CD\u8BD5\uFF0C\u5982\u679C\u60F3\u5BF9\u6240\u6709\u64CD\u4F5C\u8BF7\u6C42\u90FD\u8FDB\u884C\u91CD\u8BD5\uFF0C\u53EF\u4EE5\u914D\u7F6E\u8BE5\u5C5E\u6027\uFF08\u5982\u679CPOST\u63A5\u53E3\u4E0D\u652F\u6301\u5E42\u7B49\u7684\u8BDD\u614E\u7528\uFF09\u4E3Atrue
ribbon.OkToRetryOnAllOperations=false
# \u5BF9\u54CD\u5E94\u4E2D\u8FD4\u56DE\u67D0\u4E9B\u72B6\u6001\u7801\u65F6\u91CD\u8BD5\u8BF7\u6C42
#ribbon.retryableStatusCodes=500,502
# \u540C\u4E00\u4E2AServer\u91CD\u8BD5\u6B21\u6570
ribbon.MaxAutoRetries=0
# \u6700\u591A\u91CD\u8BD5\u51E0\u4E2AServer
ribbon.MaxAutoRetriesNextServer=1
# \u4E5F\u53EF\u4EE5\u6309\u670D\u52A1\u63D0\u4F9B\u8005\u5206\u522B\u914D\u7F6E
# serviceId1.ribbon.MaxAutoRetries=0


spring.shardingsphere.datasource.names=ds
spring.shardingsphere.datasource.ds.type=com.zaxxer.hikari.HikariDataSource
spring.shardingsphere.datasource.ds.driver-class-name=com.mysql.jdbc.Driver
spring.shardingsphere.datasource.ds.jdbc-url=***************************************************************************************************************************************
spring.shardingsphere.datasource.ds.username=vip_present
spring.shardingsphere.datasource.ds.password=B@7fcd296b631
spring.shardingsphere.datasource.ds.connection-test-query=select NOW()
spring.shardingsphere.datasource.ds.connection-timeout=3000
spring.shardingsphere.datasource.ds.idle-timeout=60000
spring.shardingsphere.datasource.ds.max-lifetime=18000


spring.shardingsphere.sharding.default-data-source-name=ds
spring.shardingsphere.sharding.tables.supply_present_record.actual-data-nodes=ds.supply_present_record_${def tmp=[];(0..99).each {e->tmp.add(String.format("%02d",e))};return tmp}
spring.shardingsphere.sharding.tables.supply_present_record.table-strategy.standard.sharding-column=uid
spring.shardingsphere.sharding.tables.supply_present_record.table-strategy.standard.precise-algorithm-class-name=com.iqiyi.vip.present.sharding.HashPreciseShardingAlgorithm


spring.shardingsphere.sharding.tables.present_order.actual-data-nodes=ds.present_order_$->{0..99}
spring.shardingsphere.sharding.tables.present_order.table-strategy.standard.sharding-column=uid
spring.shardingsphere.sharding.tables.present_order.table-strategy.standard.precise-algorithm-class-name=com.iqiyi.vip.present.sharding.HashPreciseShardingAlgorithm

spring.shardingsphere.sharding.tables.present_record.actual-data-nodes=ds.present_record_$->{0..99}
spring.shardingsphere.sharding.tables.present_record.table-strategy.standard.sharding-column=buy_uid
spring.shardingsphere.sharding.tables.present_record.table-strategy.standard.precise-algorithm-class-name=com.iqiyi.vip.present.sharding.HashPreciseShardingAlgorithm

hystrix.apollo.metrics.projectId=108977
hystrix.apollo.metrics.projectName=felixsun
hystrix.apollo.metrics.cluster=BJDXT9_CLUSTER1

spring.jpa.show-sql=true

###########################################################
# other setting
###########################################################
debug=false

bizsource.map.mbd_views=aRSDpkD9aRTCypBhqd4bWJYbTDOIZOix
bizsource.map.giftcard=eAVSpkD9aRTCypBhqd4bWJYbTDOIZOix
bizsource.map.vippresent=tSNXpkD9aRTCypBhqd4bWJYbTDOIZOix

limit.config.fileName=springmvcLimitConfig

metrics.http.switch=true

######send score###########
send.score.url=http://growth.qiyi.domain/growth-api/apis/score/add
######send score end######
internal.pay.key=1df3ec6fa2
internal.growth.key=1df3ec6fa2

sign.in.url=http://tc.vip.qiyi.domain/taskCenter/internal/userSign
qiyue.refund.sign=ff732b68a867d553c6c564a606cd57a2
qiyue.refund.serviceCode=lyksc7aq36aedndk

vinfo.get.url=http://vip-info-server-online/internal/vip_users

######## send vip end ########
##refund.url=http://VIPTRADE-REFUNDSERVICE-API/refundService/order/partnerRefundOrder.action
##refund.url.lb=true

refund.url=http://VIPTRADE-REFUNDSERVICE-API/refundService/order/common/refund
refund.url.lb=true
######## send vip ########
dopay.url=http://VIPTRADE-FREE-ORDER/api/internal/free-pay/dopay.action
dopay.url.lb=true
consumer.appname=vip-present-worker

gold.present.youth=919a2ea53ffa9779
vipTradeSdkSignKey=b78dc854afc05d8062558b0bc22141be
tradeapiServerUrl=http://tradeapi.vip.qiyi.domain

############# task cluster config start ###############
zookeeper.url=cnhb4.vip-async-task.online001.zk.qiyi.middle:2181,cnhb4.vip-async-task.online002.zk.qiyi.middle:2181,cnhb4.vip-async-task.online003.zk.qiyi.middle:2181,cnhb4.vip-async-task.online004.zk.qiyi.middle:2181,cnhb4.vip-async-task.online005.zk.qiyi.middle:2181
zookeeper.user=vipasynctask
zookeeper.password=YGIP5o2YeODf

async.task.execute=true
async.task.execute.cluster=true
async.task.zookeeper.root=/present-task
async.task.poolType.exe=1,2,3,4
#async.task.pooltype.failover=3

async.task.pool.1.name=PresentVipAsyncTask
async.task.pool.1.corePoolSize=6
async.task.pool.1.maximumPoolSize=10
async.task.pool.1.workQueueNum=2000

async.task.pool.2.name=RefundVipAsyncTask
async.task.pool.2.corePoolSize=6
async.task.pool.2.maximumPoolSize=10
async.task.pool.2.workQueueNum=1000

async.task.pool.3.name=SendVipTask
async.task.pool.3.corePoolSize=6
async.task.pool.3.maximumPoolSize=10
async.task.pool.3.workQueueNum=1000

async.task.pool.4.name=PRESENT_ASYNC_ORDER_MESSAGE
async.task.pool.4.corePoolSize=5
async.task.pool.4.maximumPoolSize=10
async.task.pool.4.workQueueNum=1000
############# task cluster config end #################
file_name=qiyue_core_config

# product rmq
rocketmq.consumer.product.groupname=CG-vip-present
rocketmq.consumer.product.address=dc-resource-worker-online003-hbaz4.qiyi.virtual:9876;dc-resource-worker-online203-hbaz2.qiyi.virtual:9876;dc-resource-worker-online147-hbaz1.qiyi.virtual:9876
rocketmq.consumer.product.token=CT-f28bb08d-3384-4bcd-850f-a51e72787909
rocketmq.consumer.product.topics=vipProduct
rocketmq.consumer.product.tags=*
rocketmq.consumer.product.consumethreadmin=20
rocketmq.consumer.product.consumethreadmax=32
rocketmq.consumer.product.consumemessagebatchmaxsize=1
#order finish
rocketmq.consumer.orderfinish.groupname=CG-vip_present_msg_order_fulfill_finished
rocketmq.consumer.orderfinish.address=dc-resource-worker-online176-hbaz1.qiyi.virtual:9876;dc-resource-worker-online334-hbaz2.qiyi.virtual:9876
rocketmq.consumer.orderfinish.token=CT-d77b5251-811b-42b6-8fe7-5460c805e09f
rocketmq.consumer.orderfinish.topics=vip_trade_msg_order_fulfill_finished
rocketmq.consumer.orderfinish.tags=*
rocketmq.consumer.orderfinish.consumethreadmin=20
rocketmq.consumer.orderfinish.consumethreadmax=32
rocketmq.consumer.orderfinish.consumemessagebatchmaxsize=1
#single pid
rocketmq.consumer.single-pid.groupname=CG-present
rocketmq.consumer.single-pid.address=dc-resource-worker-online003-hbaz4.qiyi.virtual:9876;dc-resource-worker-online203-hbaz2.qiyi.virtual:9876;dc-resource-worker-online147-hbaz1.qiyi.virtual:9876
rocketmq.consumer.single-pid.token=CT-92b48c9f-c95f-42d2-9141-264f0656a8c2
rocketmq.consumer.single-pid.topics=singleProduct
rocketmq.consumer.single-pid.tags=*
rocketmq.consumer.single-pid.consumethreadmin=20
rocketmq.consumer.single-pid.consumethreadmax=32
rocketmq.consumer.single-pid.consumemessagebatchmaxsize=1

rmq.present.delay.prod.address=vip-trade-rocketmq-online001-bdwg.qiyi.virtual:9876;vip-trade-rocketmq-online001-bjdxt9.qiyi.virtual:9876
rmq.present.delay.prod.groupname=PG-vip_present_delay_order
rmq.present.delay.prod.token=PT-2f065523-8d68-4fb9-9678-548998d67d8b

rmq.present.delay.consumer.address=vip-trade-rocketmq-online001-bdwg.qiyi.virtual:9876;vip-trade-rocketmq-online001-bjdxt9.qiyi.virtual:9876
rmq.present.delay.consumer.groupname=CG-vip_present_delay_order
rmq.present.delay.consumer.token=CT-60eb8fcb-c5c7-4a18-85e4-42d52a736bb5
rmq.present.delay.consumer.topics=vip_present_delay_order

#right transfer order finished rmq
rmq.right.transfer.order.finished.consumer.address=dc-resource-worker-online176-hbaz1.qiyi.virtual:9876;dc-resource-worker-online334-hbaz2.qiyi.virtual:9876
rmq.right.transfer.order.finished.consumer.groupname=CG-vip_present_right_transfer_order_finished
rmq.right.transfer.order.finished.consumer.token=CT-f718f589-cd0f-4be8-8945-9b5a60d0a6c8
rmq.right.transfer.order.finished.consumer.topics=vip_trade_msg_right_transfer_order_finished

#exist gitv user need present order rmq
exist.gitv.need.present.order.consumer.address=vip-trade-rocketmq-online001-bdwg.qiyi.virtual:9876;vip-trade-rocketmq-online001-bjdxt9.qiyi.virtual:9876
exist.gitv.need.present.order.consumer.groupname=CG-viptrade_exist_gitv_need_present_order
exist.gitv.need.present.order.consumer.token=CT-bbda24b8-7f64-47bd-a984-0add6ad12b7d
exist.gitv.need.present.order.consumer.topics=exist_gitv_user_need_present_order

#present async task binlog consumer rmq
present.async.task.binlog.consumer.address=vip-trade-rocketmq-online001-bdwg.qiyi.virtual:9876;vip-trade-rocketmq-online001-bjdxt9.qiyi.virtual:9876
present.async.task.binlog.consumer.groupname=CG-present_async_task_binlog
present.async.task.binlog.consumer.token=CT-4774c478-dcb8-4851-8f20-14b3e8b69c52
present.async.task.binlog.consumer.topics=present_async_task_binlog

#present async task producer rmq
present.async.task.producer.address=vip-trade-rocketmq-online001-bdwg.qiyi.virtual:9876;vip-trade-rocketmq-online001-bjdxt9.qiyi.virtual:9876
present.async.task.producer.groupname=PG-present_async_task
present.async.task.producer.token=PT-e93b8a7b-71fd-47f7-8111-0e8edf3ac734

#exist gitv user present task consumer rmq
present.async.task.consumer.address=vip-trade-rocketmq-online001-bdwg.qiyi.virtual:9876;vip-trade-rocketmq-online001-bjdxt9.qiyi.virtual:9876
present.async.task.consumer.groupname=CG-present_async_task
present.async.task.consumer.token=CT-73b56b3b-61d5-466c-a87f-a6b30336bfb9
present.async.task.consumer.topics=present_async_task

present.vip.rights.grant.producer.address=vip-trade-rocketmq-online001-bdwg.qiyi.virtual:9876;vip-trade-rocketmq-online001-bjdxt9.qiyi.virtual:9876
present.vip.rights.grant.producer.groupname=PG-present_vip_rights_grant
present.vip.rights.grant.producer.token=PT-012223bc-8b05-4d2a-a744-38e2a0ae18e6
present.vip.rights.grant.producer.topic=present_vip_rights_grant

present.vip.rights.grant.consumer.address=vip-trade-rocketmq-online001-bdwg.qiyi.virtual:9876;vip-trade-rocketmq-online001-bjdxt9.qiyi.virtual:9876
present.vip.rights.grant.consumer.groupname=CG-present_vip_rights_grant
present.vip.rights.grant.consumer.token=CT-1c1cc86f-ae54-403e-b0b5-08830460b6e1
present.vip.rights.grant.consumer.topics=present_vip_rights_grant

rmq.basic.vip.supply.consumer.address=vip-task-unlock-rocketmq-online001-bdwg.qiyi.virtual:9876;vip-task-unlock-rocketmq-online002-bdwg.qiyi.virtual:9876
rmq.basic.vip.supply.consumer.topics=supply_basic_vip
rmq.basic.vip.supply.consumer.groupname=CG-supply_basic_vip
rmq.basic.vip.supply.consumer.token=CT-bf391d6d-6222-4c55-bd80-a42777987a08

#\u57FA\u7840\u4F1A\u5458\u8865\u8D60\u6743\u76CA\u65F6\u957Frmq\u914D\u7F6E
rmq.basic.vip.supply.url=vip-task-unlock-rocketmq-online001-bdwg.qiyi.virtual:9876;vip-task-unlock-rocketmq-online002-bdwg.qiyi.virtual:9876
rmq.basic.vip.supply.topic=supply_basic_vip
rmq.basic.vip.supply.group=PG-supply_basic_vip
rmq.basic.vip.supply.token=PT-13c8d69a-93de-4a71-b48d-1a713449280d

############# present sport or book or \u5947\u9047 vip config start ##############
sport.api.key=9e19b44131975c88157ae62d1ba766c0
sport.present.url=http://cooperation.ssports.com/diamond/give
sport.present.url.lb=false
sport.cancel.url=http://cooperation.ssports.com/diamond/refund
sport.cancel.url.lb=false

book.api.key=e53ed251941a6ad7c889d9a735ef5530
book.present.url=http://api-yuedu.iqiyi.com/book/monthly/presentDiamondVip
book.present.url.lb=false
book.cancel.url=http://api-yuedu.iqiyi.com/book/monthly/cancelPresentDiamondVip
book.cancel.url.lb=false
qiyu.api.key=0294716aaca12efae81cdf46a30fe7b4
qiyu.present.url=http://vraioapi.qiyi.domain/qiyu-vip/present
qiyu.present.url.lb=false
qiyu.cancel.url=http://vraioapi.qiyi.domain/qiyu-vip/reclaim
qiyu.cancel.url.lb=false
############# present sport or book vip config end ################
########### qiyue multilingual api start ################
qiyue.multilingual.url=http://VIP-CONTENT-QUERY-SERVER-ONLINE/multilingual/contents/query
qiyue.multilingual.bizSource=buy-present
qiyue.multilingual.sourceKey=F92B4FBAAEEA7E4B3EDF76BEE670C6B8
########### qiyue multilingual api end ################

# passport \uFFFD\uFFFD\u057E\uFFFD\u04FF\uFFFD
passport.user.info=http://passport.qiyi.domain/apis/profile/info.action
passport.url.byUid=http://passport.qiyi.domain/apis/plaintext/byUid.action

free.order.url=http://VIPTRADE-FREE-ORDER/api/internal/free-pay/dopay.action
free.order.signKey=1df3ec6fa2

appEnv=pro
appName=vip-present
appRegion=default

#\u4F1A\u5458\u76D1\u63A7\u9E70\u773C
endpoints.prometheus.sensitive=false
management.context-path=/actuator
management.port=8099

table.present.order.total.count=100

spring.redisson.clusterServers=redis://viprightscentercache.1.qiyi.redis:8750,redis://viprightscentercache.2.qiyi.redis:8750
spring.redisson.password=3mjxH4N5MN9f

# order
order.sharding.database.urls=*************************************************************************,*************************************************************************,*************************************************************************,*************************************************************************,*************************************************************************,*************************************************************************,*************************************************************************,*************************************************************************,*************************************************************************,**************************************************************************,**************************************************************************,**************************************************************************,**************************************************************************,**************************************************************************,**************************************************************************,**************************************************************************
order.sharding.database.username=vip_order_pro
order.sharding.database.password=agujmw59!chinagooD8b%
order.sharding.tableSize=64