#HikariCP
hikari.minimum-idle=5
hikari.maximum-pool-size=15
hikari.idle-timeout=30000
hikari.max-lifetime=1800000
hikari.connection-timeout=30000
hikari.connection-test-query=SELECT 1
hikari.validation-timeout=300000
#spring.profiles.active=@activatedProperties@
management.endpoints.enabled-by-default=false
endpoints.enabled=false
management.endpoints.web.exposure.include=health,info
management.endpoints.web.exposure.exclude=
endpoints.health.sensitive=false
management.security.enabled=true
management.health.defaults.enabled=true
management.health.redis.enabled=false
management.health.eureka.enabled=false
management.health.db.enabled=false
management.endpoint.health.show-details=always
management.info.git.mode=full
security.user.name=vip-trade
security.user.password=vip-trade
eureka.client.healthcheck.enabled=false
hystrix.apollo.metrics.projectId=109543
hystrix.apollo.metrics.projectName=vip-dmp
hystrix.apollo.metrics.cluster=BJDXT9_CLUSTER1

server.port=8080
server.context-path=/
server.tomcat.uri-encoding=UTF-8
server.session-timeout=1200
server.tomcat.max-threads=200

spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
spring.http.encoding.force=true
spring.jackson.serialization.indent_output=true

iqiyi.cloud.config.bootstrap.enabled=true
iqiyi.cloud.config.bootstrap.namespaces=application


mybatis.mapper-locations=classpath:/mybatis-mapper/*Mapper.xml
mybatis.type-aliases-package=com.iqiyi.vip.present.model

vipinfo.users.url=http://vinfo.vip.qiyi.domain/internal/vip_users

# passport
passport.source=boss
passport.agent.type=167
passport.secret.key=bSsIlop6JD8tW7BdPLQ2foAL

######## send vip card ########
card.add.url=http://serv.vip.qiyi.domain/internal/card/add
card.service.invoke.key=tSNXpkD9aRTCypBhqd4bWJYbTDOIZOix
######## send vip card end ########

######## send growth ########
send.growth.url=http://serv.vip.qiyi.domain/api/internal/vip-growth/present.action
######## send growth end ########

spring.mvc.view.prefix=/WEB-INF/views/
spring.mvc.view.suffix=.jsp
async.task.table.name=async_task
async.task.save=true
async.task.failureTable.name=async_failure_task
async.task.serverName=${MESOS_TASK_ID:}

spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=true

# Redisson?????
spring.redisson.masterConnectionPoolSize=64
spring.redisson.slaveConnectionPoolSize=64
spring.redisson.masterConnectionMinimumIdleSize=10
spring.redisson.slaveConnectionMinimumIdleSize=10
spring.redisson.connectTimeout=10000
spring.redisson.timeout=3000
spring.redisson.retryAttempts=3
spring.redisson.retryInterval=1500
spring.redisson.pingConnectionInterval=30000
spring.redisson.idleConnectionTimeout=10000