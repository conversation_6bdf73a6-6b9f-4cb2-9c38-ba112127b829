<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>vip-present</artifactId>
        <groupId>com.iqiyi.vip.present</groupId>
        <version>${present.version}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>vip-present-worker</artifactId>
    <packaging>jar</packaging>
    <version>${parent.version}</version>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.iqiyi.vip.present</groupId>
            <artifactId>vip-present-core</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
              <exclusion>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
              </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>iqiyi.hystrix</groupId>
            <artifactId>iqiyi-hystrix-metric</artifactId>
            <version>${iqiyi.hystrix.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.iqiyi.apollo</groupId>
                    <artifactId>apollo-metric-sender</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>