# Spring Boot 2.x Bean definition overriding configuration
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=true

hikari.minimum-idle=10
hikari.maximum-pool-size=50
hikari.idle-timeout=30000
hikari.max-lifetime=1200000
hikari.connection-timeout=30000
hikari.connection-test-query=SELECT 1
hikari.validation-timeout=300000
mybatis.mapper-locations=classpath:/mybatis-mapper/*Mapper.xml
mybatis.type-aliases-package=com.iqiyi.vip.present.model
consumer.appname=vip-present-web
async.task.execute=false
async.task.save=true
async.task.failureTable.name=async_failure_task
management.endpoints.enabled-by-default=false

iqiyi.cloud.config.bootstrap.enabled=true
iqiyi.cloud.config.bootstrap.namespaces=application

spring.datasource.order.hikari.minimumIdle=25
spring.datasource.order.hikari.maximumPoolSize=25
spring.datasource.order.hikari.maxLifetime=1800000
spring.datasource.order.hikari.idleTimeout=600000
spring.datasource.order.hikari.connectionTimeout=30000

endpoints.enabled=false
management.endpoints.web.exposure.include=health,info
management.endpoints.web.exposure.exclude=
endpoints.health.sensitive=false
management.security.enabled=true
management.health.defaults.enabled=true
management.health.redis.enabled=false
management.health.db.enabled=false
management.health.eureka.enabled=false
management.endpoint.health.show-details=always

# Redisson?????
spring.redisson.masterConnectionPoolSize=64
spring.redisson.slaveConnectionPoolSize=64
spring.redisson.masterConnectionMinimumIdleSize=10
spring.redisson.slaveConnectionMinimumIdleSize=10
spring.redisson.connectTimeout=10000
spring.redisson.timeout=3000
spring.redisson.retryAttempts=3
spring.redisson.retryInterval=1500
spring.redisson.pingConnectionInterval=30000
spring.redisson.idleConnectionTimeout=10000