# \u547D\u540D\u683C\u5F0F\uFF1A\u5E94\u7528\u540D-online
spring.application.name=vip-present-web-online
server.port=8080

eureka.instance.hostname=${HOST}
eureka.instance.non-secure-port=${PORT_8080}
eureka.instance.instance-id=${eureka.instance.hostname}:${eureka.instance.non-secure-port}

# \u5E94\u7528\u6240\u5728zone\uFF0C\u6839\u636E\u5E94\u7528\u673A\u623F\u586B\u5199\uFF0C\u5177\u4F53\u53C2\u8003\uFF1Ahttp://wiki.qiyi.domain/pages/viewpage.action?pageId=1193148821
eureka.instance.metadata-map.zone=zone-aws
# \u79DF\u671F\u66F4\u65B0\u65F6\u95F4\u95F4\u9694\uFF08\u9ED8\u8BA430\u79D2\uFF09
eureka.instance.lease-renewal-interval-in-seconds=5
# \u79DF\u671F\u5230\u671F\u65F6\u95F4\uFF08\u9ED8\u8BA490\u79D2\uFF09
eureka.instance.lease-expiration-duration-in-seconds=10
# \u662F\u5426\u6CE8\u518C\u5230\u6CE8\u518C\u4E2D\u5FC3\uFF0C\u5982\u679C\u4E0D\u9700\u8981\u53EF\u4EE5\u8BBE\u7F6E\u4E3Afalse
eureka.client.register-with-eureka=true
# \u5E94\u7528\u6240\u5728region\uFF0C\u5317\u4EAC\u3001\u4E2D\u7ECF\u4E91\u3001\u4E2D\u4E91\u4FE1\u673A\u623F\u586B\u5199region-bj\uFF0C\u534E\u4E2D\u673A\u623F\u586B\u5199region-hz\uFF0C\u6D77\u5916\u673A\u623F\u586B\u5199region-sea
eureka.client.region=region-sea
eureka.client.availability-zones.region-sea=zone-aws
eureka.client.service-url.zone-aws=http://aws.eureka.vip.qiyi.domain:8080/eureka

# \u5F00\u542F\u5065\u5EB7\u68C0\u67E5\uFF08\u9700\u8981spring-boot-starter-actuator\u4F9D\u8D56\uFF09
eureka.client.healthcheck.enabled=true
spring.cloud.netflix.metrics.enabled=false

# actuator config
endpoints.health.sensitive=false
endpoints.enabled=true
management.endpoints.web.exposure.include=health,info
management.endpoints.web.exposure.exclude=
management.security.enabled=true
management.health.defaults.enabled=true
management.health.mail.enabled=false
management.health.redis.enabled=false
management.health.eureka.enabled=false
management.endpoint.health.show-details=simple


datasource.url=***************************************************************************************************************************************
datasource.driver-class-name=com.mysql.jdbc.Driver
datasource.username=vip_present
datasource.password=B@7fcd296b631

dopay.url=http://global.vip.qiyi.domain/vip-global-trade/internal/freePay/dopay
internal.pay.key=1df3ec6fa2
vinfo.get.url=http://intl-vinfo.vip.iqiyi.com/internal/vip_users
vipTradeSdkSignKey=b78dc854afc05d8062558b0bc22141be
tradeapiServerUrl=http://intl-tradeapi.vip.qiyi.domain

######## vip-job\u914D\u7F6E ###########
##\u8C03\u5EA6\u4E2D\u5FC3\u90E8\u7F72\u8DDF\u5730\u5740\uFF1A\u5982\u8C03\u5EA6\u4E2D\u5FC3\u96C6\u7FA4\u90E8\u7F72\u5B58\u5728\u591A\u4E2A\u5730\u5740\u5219\u7528\u9017\u53F7\u5206\u9694\u3002
vip.job.admin.addresses=http://intl-vip-job.online.qiyi.qae
##\u5E94\u7528"AppName"\uFF0C\u5E94\u7528\u5FC3\u8DF3\u6CE8\u518C\u5206\u7EC4\u4F9D\u636E\uFF0CappName\u9700\u8981\u8054\u7CFB\u4F1A\u5458\u8425\u9500\u56E2\u961F\u5728\u8C03\u5EA6\u4E2D\u5FC3\u6CE8\u518C
vip.job.executor.appname=vip-present
##\u6267\u884C\u5668\u9ED8\u8BA4\u7AEF\u53E3\u4E3A9999\uFF0C\u5355\u673A\u90E8\u7F72\u591A\u4E2A\u6267\u884C\u5668\u65F6\uFF0C\u6CE8\u610F\u8981\u914D\u7F6E\u4E0D\u540C\u6267\u884C\u5668\u7AEF\u53E3
vip.job.executor.port=9083
##\u6267\u884C\u5668\u8FD0\u884C\u65E5\u5FD7\u6587\u4EF6\u5B58\u50A8\u7684\u78C1\u76D8\u4F4D\u7F6E\uFF0C\u9700\u8981\u5BF9\u8BE5\u8DEF\u5F84\u62E5\u6709\u8BFB\u5199\u6743\u9650\uFF0C\u82E5job\u4E0D\u9700\u8981\u5355\u72EC\u6587\u4EF6\u6253\u5370\u5219\u4E0D\u9700\u8981\u914D\u7F6E
vip.job.executor.logpath=/data/logs/vip-present-web/job/
##\u6267\u884C\u5668\u901A\u8BAFTOKEN\uFF0C\u5411\u4F1A\u5458\u8425\u9500\u56E2\u961F\u7533\u8BF7
vip.job.accessToken=be6c5b5c82b541dbbb286e9563cefc1b
## \u63A5\u5165\u65B9\u5F0F\uFF0C\u9ED8\u8BA4\u865A\u673A
vip.job.access.way=qae
##QAE\u5E94\u7528API Access Key\uFF0C\u591A\u4E2Aaccess.key\u7528\u9017\u53F7\u5206\u9694
vip.job.qae.api.access.key=7c2df0079687641c03d8b4fcd3a658afd3670459
##QAE\u5E94\u7528API URL
vip.job.qae.api.url=http://qae.qiyi.virtual/api/v3/apps
##QAE\u5E94\u7528\u540D\u79F0\uFF0C\u591Aappid\u7528\u9017\u53F7\u5206\u9694\uFF0CQAE\u5E94\u7528\u540D\u79F0\u548Caccess.key\u6570\u91CF\u5FC5\u987B\u4E00\u81F4
vip.job.qae.appid=
##QAE\u6CE8\u518C\u5F00\u5173
vip.job.qae.switch=on

qiyue.singlePid.url=http://admin.vip.qiyi.domain/vip-operation-api/basisdata/singleProduct/list
qiyue.singlePid.sys=buyPresent
qiyue.singlePid.secretKey=pd03f77aar4ctxyw9jvibs0ex6ngfeh2

# passport \uFFFD\uFFFD\uFFFD\uFFFD\u057E\uFFFD\u04FF\uFFFD
passport.user.info=http://intl-passport.qiyi.domain/intl/user/info.action
passport.url.byUid=http://intl-passport.qiyi.domain/intl/inner/user/byUid.action

appEnv=pro
appName=intl-vip-present
appRegion=intl


#\u4F1A\u5458\u76D1\u63A7\u9E70\u773C
endpoints.prometheus.sensitive=false
management.context-path=/actuator
management.port=8099

table.present.order.total.count=100