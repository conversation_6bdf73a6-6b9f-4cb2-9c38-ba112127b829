spring.application.name=vip-present-web-online
server.port=8080

eureka.instance.hostname=${HOST}
eureka.instance.non-secure-port=${PORT_8080}
eureka.instance.instance-id=${eureka.instance.hostname}:${eureka.instance.non-secure-port}

# \u5E94\u7528\u6240\u5728zone\uFF0C\u6839\u636E\u5E94\u7528\u673A\u623F\u586B\u5199
eureka.instance.metadata-map.zone=zone-bj
# \u79DF\u671F\u66F4\u65B0\u65F6\u95F4\u95F4\u9694\uFF08\u9ED8\u8BA430\u79D2\uFF09
eureka.instance.lease-renewal-interval-in-seconds=5
# \u79DF\u671F\u5230\u671F\u65F6\u95F4\uFF08\u9ED8\u8BA490\u79D2\uFF09
eureka.instance.lease-expiration-duration-in-seconds=10
# \u662F\u5426\u6CE8\u518C\u5230\u6CE8\u518C\u4E2D\u5FC3\uFF0C\u5982\u679C\u4E0D\u9700\u8981\u53EF\u4EE5\u8BBE\u7F6E\u4E3Afalse
eureka.client.register-with-eureka=true
# \u5E94\u7528\u6240\u5728region\uFF0C\u5317\u4EAC\u3001\u4E2D\u7ECF\u4E91\u3001\u4E2D\u4E91\u4FE1\u673A\u623F\u586B\u5199region-bj\uFF0C\u534E\u4E2D\u673A\u623F\u586B\u5199region-hz\uFF0C\u6D77\u5916\u673A\u623F\u586B\u5199region-sea
eureka.client.region=region-bj
# region\u5185\u53EF\u7528zone\u5217\u8868
eureka.client.availability-zones.region-bj=zone-bj,zone-zjy,zone-zyx,zone-hz
# \u6CE8\u518C\u4E2D\u5FC3\u914D\u7F6E\uFF0Cregion-bj\u4F7F\u7528\u5982\u4E0B\u914D\u7F6E
eureka.client.service-url.zone-bj=http://bj.eureka.vip.qiyi.domain:8080/eureka

# \u5F00\u542F\u5065\u5EB7\u68C0\u67E5\uFF08\u9700\u8981spring-boot-starter-actuator\u4F9D\u8D56\uFF09
eureka.client.healthcheck.enabled=true
spring.cloud.netflix.metrics.enabled=false

# actuator config
endpoints.health.sensitive=false
endpoints.enabled=true
management.endpoints.web.exposure.include=health,info
management.endpoints.web.exposure.exclude=
management.security.enabled=true
management.health.defaults.enabled=true
management.health.mail.enabled=false
management.health.redis.enabled=false
management.health.eureka.enabled=false
management.endpoint.health.show-details=simple


spring.shardingsphere.datasource.names=ds
spring.shardingsphere.datasource.ds.type=com.zaxxer.hikari.HikariDataSource
spring.shardingsphere.datasource.ds.driver-class-name=com.mysql.jdbc.Driver
spring.shardingsphere.datasource.ds.jdbc-url=***************************************************************************************************************************************
spring.shardingsphere.datasource.ds.username=vip_present
spring.shardingsphere.datasource.ds.password=B@7fcd296b631
spring.shardingsphere.datasource.ds.connection-test-query=select NOW()
spring.shardingsphere.datasource.ds.connection-timeout=3000
spring.shardingsphere.datasource.ds.idle-timeout=60000
spring.shardingsphere.datasource.ds.max-lifetime=18000

spring.shardingsphere.sharding.default-data-source-name=ds
spring.shardingsphere.sharding.tables.supply_present_record.actual-data-nodes=ds.supply_present_record_${def tmp=[];(0..99).each {e->tmp.add(String.format("%02d",e))};return tmp}
spring.shardingsphere.sharding.tables.supply_present_record.table-strategy.standard.sharding-column=uid
spring.shardingsphere.sharding.tables.supply_present_record.table-strategy.standard.precise-algorithm-class-name=com.iqiyi.vip.present.sharding.HashPreciseShardingAlgorithm

spring.shardingsphere.sharding.tables.present_order.actual-data-nodes=ds.present_order_$->{0..99}
#spring.shardingsphere.sharding.tables.present_order.table-strategy.inline.sharding-column=uid
spring.shardingsphere.sharding.tables.present_order.table-strategy.standard.sharding-column=uid
spring.shardingsphere.sharding.tables.present_order.table-strategy.standard.precise-algorithm-class-name=com.iqiyi.vip.present.sharding.HashPreciseShardingAlgorithm
#spring.shardingsphere.sharding.tables.present_order.table-strategy.inline.algorithm-expression=present_order_$->{uid % 2}

spring.shardingsphere.sharding.tables.present_record.actual-data-nodes=ds.present_record_$->{0..99}
#spring.shardingsphere.sharding.tables.present_record.table-strategy.inline.sharding-column=buy_uid
spring.shardingsphere.sharding.tables.present_record.table-strategy.standard.sharding-column=buy_uid
spring.shardingsphere.sharding.tables.present_record.table-strategy.standard.precise-algorithm-class-name=com.iqiyi.vip.present.sharding.HashPreciseShardingAlgorithm
#spring.shardingsphere.sharding.tables.present_record.table-strategy.inline.algorithm-expression=present_record_$->{buy_uid % 2}


dopay.url=http://i.vip.qiyi.domain/api/internal/free-pay/dopay.action
internal.pay.key=1df3ec6fa2
vinfo.get.url=http://vip-info-server-online/internal/vip_users
vipTradeSdkSignKey=b78dc854afc05d8062558b0bc22141be
tradeapiServerUrl=http://tradeapi.vip.qiyi.domain

#??????
compensation.url=http://VIP-PRESENT-WORKER-ONLINE/compensation/handle
compensation.url.lb=true

######## vip-job\u914D\u7F6E ###########
##\u8C03\u5EA6\u4E2D\u5FC3\u90E8\u7F72\u8DDF\u5730\u5740\uFF1A\u5982\u8C03\u5EA6\u4E2D\u5FC3\u96C6\u7FA4\u90E8\u7F72\u5B58\u5728\u591A\u4E2A\u5730\u5740\u5219\u7528\u9017\u53F7\u5206\u9694\u3002
vip.job.admin.addresses=http://qiyi-job-admin.qiyi.domain
##\u5E94\u7528"AppName"\uFF0C\u5E94\u7528\u5FC3\u8DF3\u6CE8\u518C\u5206\u7EC4\u4F9D\u636E\uFF0CappName\u9700\u8981\u8054\u7CFB\u4F1A\u5458\u8425\u9500\u56E2\u961F\u5728\u8C03\u5EA6\u4E2D\u5FC3\u6CE8\u518C
vip.job.executor.appname=vip-present
##\u6267\u884C\u5668\u9ED8\u8BA4\u7AEF\u53E3\u4E3A9999\uFF0C\u5355\u673A\u90E8\u7F72\u591A\u4E2A\u6267\u884C\u5668\u65F6\uFF0C\u6CE8\u610F\u8981\u914D\u7F6E\u4E0D\u540C\u6267\u884C\u5668\u7AEF\u53E3
vip.job.executor.port=9083
##\u6267\u884C\u5668\u8FD0\u884C\u65E5\u5FD7\u6587\u4EF6\u5B58\u50A8\u7684\u78C1\u76D8\u4F4D\u7F6E\uFF0C\u9700\u8981\u5BF9\u8BE5\u8DEF\u5F84\u62E5\u6709\u8BFB\u5199\u6743\u9650\uFF0C\u82E5job\u4E0D\u9700\u8981\u5355\u72EC\u6587\u4EF6\u6253\u5370\u5219\u4E0D\u9700\u8981\u914D\u7F6E
vip.job.executor.logpath=/data/logs/vip-present-web/job/
### \u6267\u884C\u5668\u6267\u884C\u65E5\u5FD7\u6E05\u7406\u65F6\u95F4\uFF0C\u9ED8\u8BA430\u5929
vip.job.executor.logretentiondays=5
##\u6267\u884C\u5668\u901A\u8BAFTOKEN\uFF0C\u5411\u4F1A\u5458\u8425\u9500\u56E2\u961F\u7533\u8BF7
vip.job.accessToken=a881dd40dfa045fc812f627aa35a9112
## \u63A5\u5165\u65B9\u5F0F\uFF0C\u9ED8\u8BA4\u865A\u673A
vip.job.access.way=qke
##QKE\u5E94\u7528\u7684id
vip.job.qke.app.id=16485

# \u7CBE\u51C6\u89E6\u8FBE RMQ \u914D\u7F6E
rmq.remind-to-ens.url=boss-rocketmq-online001-bjdxt9qbs.qiyi.virtual:9876;boss-rocketmq-online003-bjdxt9qbs.qiyi.virtual:9876
rmq.remind-to-ens.topic=PARTNER_PRESENT_ORDER_REMIND_TO_ENS
rmq.remind-to-ens.group=PG-PARTNER_PRESENT_ORDER_REMIND_TO_ENS
rmq.remind-to-ens.token=PT-05ce7538-2036-4677-8751-5c6afbf45832

#\u57FA\u7840\u4F1A\u5458\u8865\u8D60\u6743\u76CA\u65F6\u957Frmq\u914D\u7F6E
rmq.basic.vip.supply.url=vip-task-unlock-rocketmq-online001-bdwg.qiyi.virtual:9876;vip-task-unlock-rocketmq-online002-bdwg.qiyi.virtual:9876
rmq.basic.vip.supply.topic=supply_basic_vip
rmq.basic.vip.supply.group=PG-supply_basic_vip
rmq.basic.vip.supply.token=PT-13c8d69a-93de-4a71-b48d-1a713449280d

present.vip.rights.grant.producer.address=vip-trade-rocketmq-online001-bdwg.qiyi.virtual:9876;vip-trade-rocketmq-online001-bjdxt9.qiyi.virtual:9876
present.vip.rights.grant.producer.groupname=PG-present_vip_rights_grant
present.vip.rights.grant.producer.token=PT-012223bc-8b05-4d2a-a744-38e2a0ae18e6
present.vip.rights.grant.producer.topic=present_vip_rights_grant

#qiyue single pid api
qiyue.singlePid.url=http://admin.vip.qiyi.domain/vip-operation-api/basisdata/singleProduct/list
qiyue.singlePid.sys=buyPresent
qiyue.singlePid.secretKey=pd03f77aar4ctxyw9jvibs0ex6ngfeh2

# passport \uFFFD\uFFFD\u057E\uFFFD\u04FF\uFFFD
passport.user.info=http://passport.qiyi.domain/apis/profile/info.action
passport.url.byUid=http://passport.qiyi.domain/apis/plaintext/byUid.action

appEnv=pro
appName=vip-present
appRegion=default

#\u4F1A\u5458\u76D1\u63A7\u9E70\u773C
endpoints.prometheus.sensitive=false
management.context-path=/actuator
management.port=8099

table.present.order.total.count=100

spring.redisson.clusterServers=redis://viprightscentercache.1.qiyi.redis:8750,redis://viprightscentercache.2.qiyi.redis:8750
spring.redisson.password=3mjxH4N5MN9f

# order
order.sharding.database.urls=*************************************************************************,*************************************************************************,*************************************************************************,*************************************************************************,*************************************************************************,*************************************************************************,*************************************************************************,*************************************************************************,*************************************************************************,**************************************************************************,**************************************************************************,**************************************************************************,**************************************************************************,**************************************************************************,**************************************************************************,**************************************************************************
order.sharding.database.username=vip_order_pro
order.sharding.database.password=agujmw59!chinagooD8b%
order.sharding.tableSize=64