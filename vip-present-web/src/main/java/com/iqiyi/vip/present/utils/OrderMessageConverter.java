package com.iqiyi.vip.present.utils;

import com.iqiyi.vip.present.api.OrderMessageApi;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.entity.Order;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * Order对象与OrderMessage对象转换工具类（web模块版本）
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Slf4j
@Component
public class OrderMessageConverter {
    @Resource
    private OrderMessageApi orderMessageApi;

    /**
     * 从消息系统获取订单消息
     *
     * @param order 订单对象
     * @return 订单消息对象
     */
    public OrderMessage getOrderMessage(Order order) {
        if (order == null || StringUtils.isBlank(order.getOrderCode())) {
            log.warn("订单或订单编号为空，无法从消息系统获取订单消息");
            return null;
        }

        try {
            // 调用消息系统API获取订单消息Map
            Map<String, String> messageMap = orderMessageApi.getOrderMessage(order.getOrderCode());
            if (messageMap != null && !messageMap.isEmpty()) {
                log.info("从消息系统获取订单消息成功，orderCode: {}", order.getOrderCode());
                // 直接使用JacksonUtils将Map转换为OrderMessage对象
                return JacksonUtils.mapToBean(messageMap, OrderMessage.class);
            }
        } catch (Exception e) {
            log.error("从消息系统获取订单消息异常，orderCode: {}", order.getOrderCode(), e);
        }

        return null;
    }

}
