package com.iqiyi.vip.present.controller;

import com.iqiyi.vip.present.api.QiyueApi;
import com.iqiyi.vip.present.dao.PresentSinglePidDao;
import com.iqiyi.vip.present.model.PresentSinglePid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fix")
@Slf4j
public class FixController {
    @Resource
    private PresentSinglePidDao presentSinglePidDao;
    @Autowired
    private QiyueApi qiyueApi;

    @RequestMapping("/saveSinglePid")
    public String saveSinglePid(Integer start, Integer end) throws InterruptedException {
        log.info("[save single pid start]");
        List<PresentSinglePid> result = new ArrayList<>();
        int num = 0;
        int quit = 5;
        for (int i = start; i < end; i++) {
            if (quit < 0) {
                break;
            }
            Thread.sleep(20L);
            List<PresentSinglePid> pids = qiyueApi.getSinglePid(i, 100);
            if (null == pids) {
                log.warn("[get single pid err.][pageNo:][{}]", i);
                quit = --quit;
                continue;
            }
            if (0 == pids.size()) {
                quit = --quit;
                continue;
            }
            result.addAll(pids);
            log.info("[get single pid][pageNo:][{}]", i);
        }
        for (PresentSinglePid p : result) {
            presentSinglePidDao.insertOrUpdate(p);
            num = num + 1;
        }
        log.info("[save single pid end][num:][{}]", num);
        return "success. sum:" + num;
    }
}
