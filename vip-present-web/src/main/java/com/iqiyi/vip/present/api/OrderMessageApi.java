package com.iqiyi.vip.present.api;

import com.iqiyi.vip.present.apiresponse.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 订单消息API，用于从消息系统获取订单消息
 */
@Service
@Slf4j
public class OrderMessageApi extends BaseApi { 

    @Resource
    private RestTemplate restTemplate;

    @Value("${order.message.url:http://viptrade-message.qsm.qiyi.middle/message/getMsgBody}")
    private String orderMessageUrl;

    /**
     * 从消息系统获取订单消息
     *
     * @param orderCode 订单编号
     * @return 订单消息Map结构，由下游自己处理转换
     */
    public Map<String, String> getOrderMessage(String orderCode) {
        if (StringUtils.isBlank(orderCode)) {
            log.warn("订单编号为空，无法获取订单消息");
            return null;
        }

        Map<String, Object> params = new HashMap<>();
        params.put("orderCode", orderCode);
        params.put("topicName", "vip_trade_msg_order_fulfill_finished");

        try {
            ParameterizedTypeReference<BaseResponse<Map<String, String>>> typeReference =
                new ParameterizedTypeReference<BaseResponse<Map<String, String>>>() {};
            
            BaseResponse<Map<String, String>> response = doGet(restTemplate, orderMessageUrl, params,
                "获取订单消息", typeReference);
            
            if (response == null || !BaseResponse.isSuccess(response.getCode()) || response.getData() == null) {
                log.warn("获取订单消息失败，orderCode: {}, response: {}", orderCode, response);
                return null;
            }
            
            Map<String, String> messageData = response.getData();
            log.info("获取订单消息成功，orderCode: {}, messageData: {}", orderCode, messageData);
            
            // 直接返回Map结构，由下游自己处理转换
            return messageData;
        } catch (Exception e) {
            log.error("获取订单消息异常，orderCode: {}", orderCode, e);
            return null;
        }
    }
} 