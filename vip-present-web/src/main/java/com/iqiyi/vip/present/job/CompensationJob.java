package com.iqiyi.vip.present.job;

import com.google.common.collect.Lists;
import com.iqiyi.job.core.context.JobHelper;
import com.iqiyi.job.core.handler.annotation.Job;
import com.iqiyi.vip.present.api.FeiShuApi;
import com.iqiyi.vip.present.config.datasource.DynamicDataSourceContextHolder;
import com.iqiyi.vip.present.constants.ShardingConstants;
import com.iqiyi.vip.present.consts.VipTypeEnum;
import com.iqiyi.vip.present.entity.*;
import com.iqiyi.vip.present.enums.AutoExecuteEnum;
import com.iqiyi.vip.present.enums.CheckRecordStatusEnum;
import com.iqiyi.vip.present.manager.CheckRecordManager;
import com.iqiyi.vip.present.manager.CheckRuleManager;
import com.iqiyi.vip.present.manager.CompensationStrategyManager;
import com.iqiyi.vip.present.model.PresentOrder;
import com.iqiyi.vip.present.model.PresentProduct;
import com.iqiyi.vip.present.model.dto.ProcessContext;
import com.iqiyi.vip.present.model.dto.ResetProcessContext;
import com.iqiyi.vip.present.out.VipUser;
import com.iqiyi.vip.present.service.*;
import com.iqiyi.vip.present.utils.CloudConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Component
@Slf4j
public class CompensationJob {


    @Resource
    OrderRepositoryService orderRepositoryService;

    @Resource
    CheckRuleManager checkRuleManager;

    @Resource
    CompensateService compensateService;

    @Resource
    private CheckRecordManager checkRecordManager;

    @Resource
    private AlterService alterService;

    @Resource
    private FeiShuApi feiShuApi;

    @Resource
    private CompensationStatusService compensationStatusService;

    @Resource
    private CompensationStrategyManager compensationStrategyManager;

    @Resource
    private PresentProductService productService;

    @Resource
    private VipInfoService vipInfoService;

    /**
     * 删除N天前的检查记录
     * N为Job的入参，如果为空，默认为7天
     */
    @Job("deleteOldCheckRecordsJobHandler")
    public void deleteOldCheckRecords() {
        try {
            long startTime = System.currentTimeMillis();
            log.info("开始删除历史检查记录...");

            // 解析Job参数，获取天数
            String params = JobHelper.getJobParam();
            String daysStr = parseJobParams(params, "days");
            int days = 7; // 默认7天

            if (StringUtils.isNotBlank(daysStr)) {
                try {
                    days = Integer.parseInt(daysStr);
                    if (days <= 0) {
                        log.warn("无效的天数参数: {}, 使用默认值7", daysStr);
                        days = 7;
                    }
                } catch (NumberFormatException e) {
                    log.warn("无效的天数参数格式: {}, 使用默认值7", daysStr, e);
                }
            }

            // 计算N天前的日期
            LocalDate cutoffDate = LocalDate.now().minusDays(days);
            Date cutoffDateTime = Date.from(cutoffDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

            // 删除记录
            int deletedCount = checkRecordManager.deleteRecordsBefore(cutoffDateTime);

            log.info("历史检查记录删除完成，删除了{}条记录，删除{}天前({})的记录，耗时:{}ms",
                    deletedCount, days, cutoffDateTime, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("删除历史检查记录失败", e);
            throw e;
        }
    }

    @Job("resetRecordStatusJobHandler")
    public void resetRecordStatus() {
        try {
            long st = System.currentTimeMillis();
            log.info("开始处理补偿记录状态重置...");
            ResetProcessContext processContext = new ResetProcessContext();
            List<CheckRecord> checkRecords = checkRecordManager.findRecordToReset();
            if (CollectionUtils.isEmpty(checkRecords)) {
                log.info("无待处理的补偿记录,任务结束");
                return;
            }
            processContext.setCheckRecords(checkRecords);
            compensateService.checkAndRest(processContext);
            log.info("补偿记录重置任务执行完成，耗时:{}", System.currentTimeMillis() - st);
        } catch (Exception e) {
            log.error("补偿记录状态校验任务执行失败.", e);
            throw e;
        }
    }

    @Job("commonJobHandler")
    public void checkOrderJobHandler() {
        try {
            ProcessContext processContext = initProcessContext();

            // 加载订单数据
            loadOrder(processContext);

            // 保存检查记录
            saveCheckRecord(processContext.getCheckRule(), processContext.getCheckRecords());

            // 执行补偿（包含调用统一补偿接口并更新CheckRecord状态）
            compensateService.compensate(processContext);

            // 补偿后告警：仅告警 WAITING、COMPENSATING、COMPENSATE_FAIL 状态的订单
            alterAfterCompensate(processContext);
        } catch (Exception e) {
            log.error("Common job handler error.", e);
            throw e;
        }
    }


    /**
     * 补偿后进行告警，仅告警状态为 WAITING、COMPENSATING、COMPENSATE_FAIL 的订单
     */
    private void alterAfterCompensate(ProcessContext processContext) {
        if (CollectionUtils.isEmpty(processContext.getOrderList())) {
            return;
        }
        CheckRule checkRule = processContext.getCheckRule();
        if (checkRule == null) {
            return;
        }

        List<String> toAlertOrderCodes = new ArrayList<>();
        for (Order order : processContext.getOrderList()) {
            try {
                CheckRecord checkRecord = checkRecordManager.findByOrderCodeAndRuleId(order.getOrderCode(), checkRule.getId());
                if (checkRecord == null) {
                    continue;
                }
                Integer status = checkRecord.getStatus();
                if (CheckRecordStatusEnum.getCompensationProcessStatuses().contains(status)) {
                    toAlertOrderCodes.add(order.getOrderCode());
                }
            } catch (Exception ex) {
                log.error("查询CheckRecord进行告警筛选异常, orderCode: {}", order.getOrderCode(), ex);
            }
        }

        if (CollectionUtils.isNotEmpty(toAlertOrderCodes)) {
            alterService.buildAndSendRuleAlter(toAlertOrderCodes, processContext);
        }
    }

    /**
     * 保存check记录
     */
    private void saveCheckRecord(CheckRule checkRule, List<CheckRecord> checkRecordList) {
        checkRecordManager.saveCheckRecord(checkRecordList);
    }

    /**
     * 加载订单
     */
    private void loadOrder(ProcessContext processContext) {
        long st = System.currentTimeMillis();
        List<Order> toCheckOrder = Lists.newArrayList();
        CheckRule checkRule = processContext.getCheckRule();

        // 获取补偿策略，用于构建查询参数
        List<CompensationStrategy> compensationStrategies = compensationStrategyManager.findByRuleId(checkRule.getId());
        CompensationStrategyHandler handler = null;
        if (!compensationStrategies.isEmpty()) {
            handler = compensationStrategyManager.getHandler(compensationStrategies.get(0).getClassName());
        }
        // 记录当前时间
        LocalDateTime now = LocalDateTime.now();
        // 获取结束时间的整数值
        LocalDateTime endTime = now.minusSeconds(checkRule.getOffset());
        LocalDateTime startTime = endTime.minusSeconds(checkRule.getWidth());
        // 获取时间范围的开始时间

        // 开始时间额外多10s
        LocalDateTime finalStartTime = startTime.minusSeconds(10);
        Timestamp finalStartTimeStamp = Timestamp.valueOf(finalStartTime);
        Timestamp endTimeStamp = Timestamp.valueOf(endTime);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        List<Order> toCheck = new ArrayList<>();
        String orderCodes = processContext.getOrderCodes();
        if (StringUtils.isNotBlank(orderCodes)) {
            fetchOrdersByCodes(orderCodes, toCheck);
        } else {
            fetchOrdersByTimeRange(processContext, finalStartTimeStamp, endTimeStamp, checkRule, toCheck);
        }
        log.info("捞取订单总耗时：{} ms", System.currentTimeMillis() - st);
        // 对捞取到的订单进行统一过滤处理
        filterAndProcessOrders(processContext, checkRule, handler, formatter, st, toCheck, toCheckOrder);
    }

    /**
     * 过滤并处理订单
     */
    private void filterAndProcessOrders(ProcessContext processContext, CheckRule checkRule,
                                        CompensationStrategyHandler handler,
                                        DateTimeFormatter formatter, long st,
                                        List<Order> toCheck, List<Order> toCheckOrder) {
        // 如果订单列表为空，直接返回
        if (CollectionUtils.isEmpty(toCheck)) {
            log.info("捞取并过滤订单总耗时：{} ms", System.currentTimeMillis() - st);
            processContext.setOrderList(Lists.newArrayList());
            processContext.setCheckRecords(Lists.newArrayList());
            return;
        }

        // 获取配置参数
        final Set<String> partnerSkuIds = CloudConfigUtil.compensationPartnerSkuFilterIds();
        final Set<Long> partnerProductIds = CloudConfigUtil.compensationPartnerProductFilterIds();
        final Set<String> referKeywords = CloudConfigUtil.compensationReferFilterKeywords();
        final Set<Integer> filterVipTypes = CloudConfigUtil.compensationFilterVipTypes();

        // 合并过滤逻辑到一个循环中
        List<Order> filteredOrders = Lists.newArrayList();
        for (Order order : toCheck) {
            try {
                // 过滤partner不为空，且skuId在指定列表中的订单
                // 如果skuId为空，则根据productId做判断
                String partner = order.getPartner();
                String skuId = order.getSkuId();
                Long productId = order.getProductId();

                if (StringUtils.isNotBlank(partner)) {
                    if (StringUtils.isNotBlank(skuId) && partnerSkuIds.contains(skuId)) {
                        continue;
                    } else if (productId != null && partnerProductIds.contains(productId)) {
                        continue;
                    }
                }

                // 获取商品信息用于后续过滤逻辑
                PresentProduct product = productService.getById(order.getProductId());
                if (product == null) {
                    continue;
                }

                // 过滤refer包含指定关键字的订单
                // 如果是黄金会员，则不过滤 refer中包含vip_present的订单（学生和基础会员的赠送依赖于黄金会员订单）
                String refer = order.getRefer();
                if (StringUtils.isNotBlank(refer)) {
                    boolean shouldFilterByRefer = false;
                    boolean isGoldVip = Integer.valueOf(VipTypeEnum.gold.getVipType()).equals(product.getSubType());
                    
                    for (String keyword : referKeywords) {
                        if (refer.contains(keyword)) {
                            // 如果是黄金会员且关键字是vip_present，则不过滤
                            if (isGoldVip && "vip_present".equals(keyword)) {
                                continue;
                            }
                            shouldFilterByRefer = true;
                            break;
                        }
                    }
                    if (shouldFilterByRefer) {
                        continue;
                    }
                }

                // 过滤指定会员类型的订单
                if (CollectionUtils.isNotEmpty(filterVipTypes)) {
                    if (filterVipTypes.contains(product.getSubType())) {
                        continue;
                    }
                }

                // 如果订单上关联的商品的会员类型是黄金会员，则需要检查用户的会员身份
                // 如果不是学生会员，则过滤该订单
                if (Integer.valueOf(VipTypeEnum.gold.getVipType()).equals(product.getSubType())) {
                    try {
                        VipUser vipUser = vipInfoService.getVipInfo(order.getUserId(), String.valueOf(VipTypeEnum.youth.getVipType()), order.getOrderCode());
                        // 如果用户没有学生会员身份或学生会员已过期，则过滤该订单
                        if (vipUser == null || !"1".equals(vipUser.getType())) {
                            log.info("过滤黄金会员订单，用户不是有效学生会员, orderCode: {}, userId: {}, vipUser: {}", 
                                order.getOrderCode(), order.getUserId(), vipUser);
                            continue;
                        }
                    } catch (Exception ex) {
                        log.error("查询用户学生会员身份异常, orderCode: {}, userId: {}", order.getOrderCode(), order.getUserId(), ex);
                        // 异常情况下不处理订单，等待下次捞取
                        continue;
                    }
                }

                // 过滤已补偿过的订单
                CheckRecord checkRecord = null;
                try {
                    checkRecord = checkRecordManager.findByOrderCodeAndRuleId(order.getOrderCode(), checkRule.getId());
                } catch (Exception ex) {
                    log.error("查询CheckRecord失败, orderCode: {}, ruleId: {}", order.getOrderCode(), checkRule.getId(), ex);
                }

                // 根据补偿策略构建查询参数
                PresentOrder queryPresentOrderParam = null;
                if (handler != null) {
                    queryPresentOrderParam = handler.buildQueryPresentOrderParam(order);
                }
                boolean shouldSkip = compensationStatusService.checkCompensationStatus(order, checkRecord, queryPresentOrderParam);

                if (!shouldSkip) {
                    filteredOrders.add(order);
                }

            } catch (Exception e) {
                log.error("检查订单补偿状态异常，保留订单进行后续处理, orderCode: {}", order.getOrderCode(), e);
            }
        }
        toCheckOrder.addAll(filteredOrders);

        log.info("捞取并过滤订单总耗时：{} ms", System.currentTimeMillis() - st);

        // 如果最终订单列表为空，直接返回
        if (CollectionUtils.isEmpty(toCheckOrder)) {
            processContext.setOrderList(Lists.newArrayList());
            processContext.setCheckRecords(Lists.newArrayList());
            return;
        }

        // toCheckOrder基于payTime排序， 找出最大、最小的payTime
        Optional<LocalDateTime> minTimeOpt = toCheckOrder.stream()
                .map(Order::getPayTime)
                .map(date -> LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()))
                .min(LocalDateTime::compareTo);

        Optional<LocalDateTime> maxTimeOpt = toCheckOrder.stream()
                .map(Order::getPayTime)
                .map(date -> LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()))
                .max(LocalDateTime::compareTo);

        if (minTimeOpt.isPresent() && maxTimeOpt.isPresent()) {
            LocalDateTime minTime = minTimeOpt.get();
            LocalDateTime maxTime = maxTimeOpt.get();
            String minFormattedTime = minTime.format(formatter);
            String maxFormattedTime = maxTime.format(formatter);
            processContext.setTimeRange(minFormattedTime.substring(11) + "-" + maxFormattedTime.substring(11));
        }

        // 将toCheckOrder按照startTime升序排序
        toCheckOrder.sort(Comparator.comparing(Order::getStartTime));

        List<Order> validOrderList = Lists.newArrayList();
        List<CheckRecord> checkRecordList = Lists.newArrayList();
        for (Order order : toCheckOrder) {
            String orderCode = order.getOrderCode();
            CheckRecord checkRecord = CheckRecord.builder()
                    .ruleId(checkRule.getId())
                    .orderCode(orderCode)
                    .createTime(new Date())
                    .updateTime(new Date())
                    .status(CheckRecordStatusEnum.WAITING.getStatus())
                    .userId(order.getUserId())
                    .build();
            checkRecord.buildCertificateInfoFromOrder(order);
            checkRecordList.add(checkRecord);
            validOrderList.add(order);
        }
        processContext.setOrderList(validOrderList);
        processContext.setCheckRecords(checkRecordList);
    }

    /**
     * 根据订单号列表获取订单
     */
    private void fetchOrdersByCodes(String orderCodes, List<Order> toCheck) {
        // 解析orderCodes并循环获取order。 orders以'_'分割
        String[] orderCodeArray = orderCodes.split("_");
        for (String orderCode : orderCodeArray) {
            if (StringUtils.isBlank(orderCode)) {
                continue;
            }
            try {
                // 获取订单分库分表信息
                ShardingInfo shardingInfo = orderRepositoryService.calculateShardingInfo(orderCode);
                if (shardingInfo == null) {
                    log.error("无法计算订单号 {} 的分库分表信息，跳过该订单", orderCode);
                    continue;
                }

                int dbIndex = shardingInfo.getDatabaseIndex();
                String tableName = shardingInfo.getTableName();
                String dbName = ShardingConstants.SHARDING_DATABASE_NAME_PREFIX + dbIndex;
                log.debug("查询订单: {}, 数据库索引: {}, 表名: {}, 数据库名: {}", orderCode, dbIndex, tableName, dbName);

                DynamicDataSourceContextHolder.setDataSourceKey(dbName);
                Order order;
                try {
                    order = orderRepositoryService.findByOrderCode(orderCode, tableName);
                } finally {
                    DynamicDataSourceContextHolder.removeDataSourceKey();
                }

                if (order != null) {
                    toCheck.add(order);
                    log.debug("成功查询到订单: {}", orderCode);
                } else {
                    log.warn("订单不存在: {}", orderCode);
                }
            } catch (Exception e) {
                // 检查是否是表不存在的错误
                if (e.getMessage() != null && e.getMessage().contains("doesn't exist")) {
                    log.error("数据库表不存在错误 - 订单号: {}, 错误信息: {}", orderCode, e.getMessage());
                    log.error("请检查生产环境数据库表结构是否完整，可能需要创建缺失的分表");
                } else {
                    log.error("查询订单异常, orderCode: {}", orderCode, e);
                }
            }
        }
    }

    /**
     * 根据时间范围获取订单
     */
    private void fetchOrdersByTimeRange(ProcessContext processContext, Timestamp startTime, Timestamp endTime, CheckRule checkRule, List<Order> toCheck) {
        for (int i = processContext.getDbStartIndex(); i <= processContext.getDbEndIndex(); i++) {
            String dbName = ShardingConstants.SHARDING_DATABASE_NAME_PREFIX + i;
            DynamicDataSourceContextHolder.setDataSourceKey(dbName);
            try {
                List<Order> orders = orderRepositoryService.findByOrderToCheck(i, startTime, endTime, checkRule);
                if (CollectionUtils.isNotEmpty(orders)) {
                    toCheck.addAll(orders);
                }
            } finally {
                DynamicDataSourceContextHolder.removeDataSourceKey();
            }
        }
    }

    /**
     * 解析任务参数
     */
    private String parseJobParams(String params, String key) {
        if (StringUtils.isBlank(params)) {
            return null;
        }
        String[] paramArray = params.split(",");
        for (String p : paramArray) {
            if (StringUtils.isBlank(p)) {
                continue;
            }
            String[] keyValue = p.split("=");
            if (keyValue.length == 2 && key.equals(keyValue[0].trim())) {
                return keyValue[1].trim();
            }
        }
        return null;
    }

    private ProcessContext initProcessContext() {
        ProcessContext context = new ProcessContext();
        String params = JobHelper.getJobParam();
        log.info("job execute params:{}", params);

        // 按key值获取固定参数
        String ruleIdStr = parseJobParams(params, "ruleId");
        String dbIndexStartStr = parseJobParams(params, "dbIndexStart");
        String dbIndexEndStr = parseJobParams(params, "dbIndexEnd");

        if (StringUtils.isAnyBlank(ruleIdStr, dbIndexStartStr, dbIndexEndStr)) {
            log.error("Missing required parameters: ruleId={}, dbIndexStart={}, dbIndexEnd={}", ruleIdStr, dbIndexStartStr, dbIndexEndStr);
            throw new IllegalArgumentException("Missing required parameters: ruleId, dbIndexStart, dbIndexEnd");
        }

        int checkRuleId = Integer.parseInt(ruleIdStr);
        int dbIndexStart = Integer.parseInt(dbIndexStartStr);
        int dbIndexEnd = Integer.parseInt(dbIndexEndStr);

        context.setDbStartIndex(dbIndexStart);
        context.setDbEndIndex(dbIndexEnd);
        context.setCompensateTimeType(AutoExecuteEnum.AUTO.getType());
        CheckRule checkRule = checkRuleManager.getById(checkRuleId);

        // 处理额外参数替换checkRule中的字段
        String condition = parseJobParams(params, "condition");
        if (StringUtils.isNotBlank(condition)) {
            checkRule.setCondition(condition);
        }

        String offsetStr = parseJobParams(params, "offset");
        if (StringUtils.isNotBlank(offsetStr)) {
            try {
                checkRule.setOffset(Integer.parseInt(offsetStr));
            } catch (NumberFormatException e) {
                log.warn("Invalid offset value: {}", offsetStr, e);
            }
        }

        String widthStr = parseJobParams(params, "width");
        if (StringUtils.isNotBlank(widthStr)) {
            try {
                checkRule.setWidth(Integer.parseInt(widthStr));
            } catch (NumberFormatException e) {
                log.warn("Invalid width value: {}", widthStr, e);
            }
        }

        context.setCheckRule(checkRule);

        String orderCodes = parseJobParams(params, "orderCodes");
        if (StringUtils.isNotBlank(orderCodes)) {
            context.setOrderCodes(orderCodes);
        }
        log.info("初始化ProcessContext完成，ruleId:{}, dbIndexStart:{}, dbIndexEnd:{}", checkRuleId, dbIndexStart, dbIndexEnd);
        return context;
    }

}
