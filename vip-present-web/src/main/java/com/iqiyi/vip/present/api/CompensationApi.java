package com.iqiyi.vip.present.api;

import com.iqiyi.vip.present.apiresponse.BaseResponse;
import com.iqiyi.vip.present.data.OrderMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * 调用vip-present-worker补偿接口的API客户端
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
@Component
@Slf4j
public class CompensationApi {

    @Resource
    private RestTemplate lbRestTemplate;

    @Value("${compensation.url:http://VIP-PRESENT-WORKER-ONLINE/compensation/handle}")
    private String compensationUrl;

    /**
     * 调用通用补偿接口
     */
    public BaseResponse<String> handleCompensation(OrderMessage orderMessage, String strategyType) {
        String url = compensationUrl + "?strategyType=" + strategyType;
        return callCompensationApi(url, orderMessage, "通用补偿[" + strategyType + "]");
    }

    /**
     * 通用的API调用方法
     */
    private BaseResponse<String> callCompensationApi(String url, OrderMessage orderMessage, String operationName) {
        try {
            log.info("开始调用{}接口, url: {}, orderMessage: {}", operationName, url, orderMessage);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<OrderMessage> requestEntity = new HttpEntity<>(orderMessage, headers);

            ResponseEntity<BaseResponse<String>> responseEntity = lbRestTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                new ParameterizedTypeReference<BaseResponse<String>>() {}
            );

            BaseResponse<String> response = responseEntity.getBody();
            log.info("{}接口调用成功, url: {}, response: {}", operationName, url, response);
            return response;

        } catch (Exception e) {
            log.error("{}接口调用失败, url: {}, orderMessage: {}", operationName, url, orderMessage, e);
            return BaseResponse.createParamError(operationName + "接口调用失败: " + e.getMessage());
        }
    }
}
