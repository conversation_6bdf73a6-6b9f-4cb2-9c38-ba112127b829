package com.iqiyi.vip.present.controller;

import com.iqiyi.vip.present.apiresponse.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 基础controller
 *
 * <AUTHOR>
 * @date 2019/6/3 13:42
 */
@RestController
@Slf4j
public class IndexController {

    /**
     * 处理首页请求
     */
    @RequestMapping(value = {"/"})
    @ResponseBody
    public BaseResponse<String> index() {
        return BaseResponse.createSuccess("Success");
    }

    /**
     * 健康监测使用
     */
    @RequestMapping("/status")
    @ResponseBody
    public BaseResponse<String> status() {
        return BaseResponse.createSuccess("Success");
    }
}
