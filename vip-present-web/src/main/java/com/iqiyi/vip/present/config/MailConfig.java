package com.iqiyi.vip.present.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.iqiyi.vip.uitls.mail.MailHelper;

/**
 * 发邮件<br>
 * http://wiki.qiyi.domain/pages/viewpage.action?pageId=625805291
 * http://pcell.gitlab.qiyi.domain/vip-utils/docs/api.html
 *
 * <AUTHOR> (<EMAIL>)
 * @date 2020/4/21 18:58
 */
@Configuration
public class MailConfig {

    @Bean
    public MailHelper mailHelper(){
        MailHelper mailHelper = new MailHelper();
        mailHelper.setUserName("vipmessage");
        mailHelper.setFrom("<EMAIL>");
        mailHelper.setToken("8u36q9d63g96hqlr");
        return mailHelper;
    }
}
