package com.iqiyi.vip.present.service.impl;

import com.iqiyi.vip.present.api.CompensationApi;
import com.iqiyi.vip.present.apiresponse.BaseResponse;
import com.iqiyi.vip.present.consts.EnumResultCode;
import com.iqiyi.vip.present.data.OrderMessage;
import com.iqiyi.vip.present.entity.CheckRecord;
import com.iqiyi.vip.present.entity.Order;
import com.iqiyi.vip.present.enums.CheckRecordStatusEnum;
import com.iqiyi.vip.present.enums.CompensationType;
import com.iqiyi.vip.present.manager.CheckRecordManager;
import com.iqiyi.vip.present.model.dto.CompensateDTO;
import com.iqiyi.vip.present.service.CompensationStatusService;
import com.iqiyi.vip.present.service.CompensationStrategyHandler;
import com.iqiyi.vip.present.service.PresentOrderService;
import com.iqiyi.vip.present.utils.OrderMessageConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 单点订单赠送补偿策略处理器
 * 
 * <AUTHOR>
 * @date 2024/12/20
 */
@Component
@Slf4j
public class VodOrderPresentStrategyHandler implements CompensationStrategyHandler {

    @Resource
    private CompensationApi compensationApi;

    @Resource
    private OrderMessageConverter orderMessageConverter;

    @Resource
    private PresentOrderService presentOrderService;

    @Resource
    private CheckRecordManager checkRecordManager;

    @Resource
    private CompensationStatusService compensationStatusService;

    @Override
    public void compensate(CompensateDTO compensateDTO) {
        CheckRecord checkRecord = compensateDTO.getCheckRecord();
        try {
            Order order = compensateDTO.getOrder();
            if (order == null) {
                log.warn("VodOrderPresentStrategyHandler补偿失败: Order为空");
                updateCheckRecordStatus(checkRecord, CheckRecordStatusEnum.COMPENSATE_FAIL);
                return;
            }

            // 检查是否已补偿过 - 使用自己的查询参数构建方法
            if (compensationStatusService.checkCompensationStatus(order, checkRecord, buildQueryPresentOrderParam(order))) {
                log.info("订单已补偿过，无需重复处理, orderCode: {}", order.getOrderCode());
                updateCheckRecordStatus(checkRecord, CheckRecordStatusEnum.ALREADY_PRESENTED);
                return;
            }

            log.info("开始执行单点订单赠送补偿, orderCode: {}", order.getOrderCode());

            // 将Order转换为OrderMessage
            OrderMessage orderMessage = orderMessageConverter.getOrderMessage(order);
            if (orderMessage == null) {
                log.error("VodOrderPresentStrategyHandler补偿失败: OrderMessage转换失败, orderCode: {}", order.getOrderCode());
                updateCheckRecordStatus(checkRecord, CheckRecordStatusEnum.COMPENSATE_FAIL);
                return;
            }

            // 调用worker的统一补偿接口
            BaseResponse<String> response = compensationApi.handleCompensation(orderMessage, CompensationType.VOD_ORDER_PRESENT.getCode());

            // 根据响应结果更新CheckRecord状态
            if (response != null && BaseResponse.isSuccess(response.getCode())) {
                log.info("单点订单赠送补偿执行成功, orderCode: {}, response: {}", order.getOrderCode(), response.getMsg());
                updateCheckRecordStatus(checkRecord, CheckRecordStatusEnum.COMPENSATING);
            } else if (response != null && EnumResultCode.NOT_MATCH.getCode().equals(response.getCode())) {
                log.info("订单不匹配处理条件, orderCode: {}, response: {}", order.getOrderCode(), response.getMsg());
                updateCheckRecordStatus(checkRecord, CheckRecordStatusEnum.NOT_MATCH);
            } else if (response != null && EnumResultCode.NO_NEED_PRESENT.getCode().equals(response.getCode())) {
                log.info("订单无需赠送, orderCode: {}, response: {}", order.getOrderCode(), response.getMsg());
                updateCheckRecordStatus(checkRecord, CheckRecordStatusEnum.NO_NEED_COMPENSATE);
            } else {
                log.error("单点订单赠送补偿执行失败, orderCode: {}, response: {}", order.getOrderCode(), response);
                updateCheckRecordStatus(checkRecord, CheckRecordStatusEnum.COMPENSATE_FAIL);
            }

        } catch (Exception e) {
            log.error("VodOrderPresentStrategyHandler补偿异常, orderCode: {}",
                compensateDTO.getOrder() != null ? compensateDTO.getOrder().getOrderCode() : "N/A", e);
            updateCheckRecordStatus(checkRecord, CheckRecordStatusEnum.COMPENSATE_FAIL);
        }
    }

    @Override
    public com.iqiyi.vip.present.model.PresentOrder buildQueryPresentOrderParam(com.iqiyi.vip.present.entity.Order order) {
        com.iqiyi.vip.present.model.PresentOrder queryParam = new com.iqiyi.vip.present.model.PresentOrder();
        queryParam.setUid(order.getUserId());
        queryParam.setOrderCode(order.getOrderCode());
        return queryParam;
    }

    /**
     * 更新CheckRecord状态
     */
    private void updateCheckRecordStatus(CheckRecord checkRecord, CheckRecordStatusEnum status) {
        if (checkRecord != null && checkRecordManager != null) {
            try {
                checkRecord.setStatus(status.getStatus());
                checkRecordManager.updateCheckRecord(checkRecord);
                log.info("更新CheckRecord状态成功, orderCode: {}, status: {}", checkRecord.getOrderCode(), status.getDesc());
            } catch (Exception e) {
                log.error("更新CheckRecord状态失败, orderCode: {}, status: {}",
                    checkRecord.getOrderCode(), status.getDesc(), e);
            }
        }
    }

}
