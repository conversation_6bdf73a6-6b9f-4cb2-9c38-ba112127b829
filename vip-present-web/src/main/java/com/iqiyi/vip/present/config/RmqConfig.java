package com.iqiyi.vip.present.config;

import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import com.iqiyi.lego.rocketmq.core.ProducerConfigOptions;
import com.iqiyi.lego.rocketmq.core.StringRocketMQTemplate;

/**
 * Rocket MQ 配置
 *
 * <AUTHOR>
 * @date 2020/7/7 下午5:34
 */
@Configuration
@Profile({"!i18n"})
public class RmqConfig {

    @Value("${rmq.remind-to-ens.url:boss-rocketmq-online001-bjdxt9qbs.qiyi.virtual:9876;boss-rocketmq-online003-bjdxt9qbs.qiyi.virtual:9876}")
    private String url;
    @Value("${rmq.remind-to-ens.topic:PARTNER_PRESENT_ORDER_REMIND_TO_ENS}")
    private String topic;
    @Value("${rmq.remind-to-ens.group:PG-PARTNER_PRESENT_ORDER_REMIND_TO_ENS}")
    private String group;
    @Value("${rmq.remind-to-ens.token:PT-05ce7538-2036-4677-8751-5c6afbf45832}")
    private String token;

    /**
     * 精准触达 RMQ
     */
    @Bean(name = "remindToEnsRmqTemplate", initMethod = "init", destroyMethod = "destroy")
    public StringRocketMQTemplate remindToEnsRmqTemplate() {
        return buildStringRocketMqTemplate(url, topic, group, token, null);
    }

    @Value("${rmq.basic.vip.supply.url:}")
    private String supplyBasicUrl;
    @Value("${rmq.basic.vip.supply.topic:}")
    private String supplyBasicTopic;
    @Value("${rmq.basic.vip.supply.group:}")
    private String supplyBasicGroup;
    @Value("${rmq.basic.vip.supply.token:}")
    private String supplyBasicToken;

    @Bean(name = "supplyBasicVipRmqTemplate", initMethod = "init", destroyMethod = "destroy")
    public StringRocketMQTemplate supplyBasicVipRmqTemplate() {
        return buildStringRocketMqTemplate(supplyBasicUrl, supplyBasicTopic, supplyBasicGroup, supplyBasicToken, null);
    }

    @Bean
    @Profile({"!i18n"})
    @ConfigurationProperties(prefix = "present.vip.rights.grant.producer")
    public RocketMQProperties presentVipRightsGrantProducerProperties() {
        return new RocketMQProperties();
    }

    @Profile({"!i18n"})
    @Bean(name = "vipRightsGrantProducer", initMethod = "start", destroyMethod = "shutdown")
    public DefaultMQProducer vipRightsGrantProducer() {
        RocketMQProperties properties = presentVipRightsGrantProducerProperties();
        DefaultMQProducer producer = new DefaultMQProducer(properties.getGroupname());
        producer.setNamesrvAddr(properties.getAddress());
        producer.setToken(properties.getToken());
        return producer;
    }

    private StringRocketMQTemplate buildStringRocketMqTemplate(String addr, String topic, String group, String token, String tag) {
        StringRocketMQTemplate rocketMQTemplate = new StringRocketMQTemplate();
        rocketMQTemplate.setNameSrvAddr(addr);
        rocketMQTemplate.setDefaultTopic(topic);
        rocketMQTemplate.setProducerGroup(group);
        rocketMQTemplate.setToken(token);
        ProducerConfigOptions producerConfigOptions = new ProducerConfigOptions();
        producerConfigOptions
                .setSendMsgTimeout(3000)
                .setCompressMsgBodyOverHowmuch(4 * 1024)
                .setRetryTimesWhenSendFailed(3)
                .setRetryTimesWhenSendAsyncFailed(2)
                .setMaxMessageSize(4 * 1024 * 1024)
                .setDefaultTags(tag);
        rocketMQTemplate.setProducerConfigOptions(producerConfigOptions);
        return rocketMQTemplate;
    }
}
