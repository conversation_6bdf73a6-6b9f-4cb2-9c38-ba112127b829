//package com.iqiyi.vip.present.job;
//
//import com.google.common.collect.Lists;
//import com.iqiyi.job.core.biz.model.ReturnT;
//import com.iqiyi.job.core.biz.model.vo.VipJobResp;
//import com.iqiyi.job.core.handler.IJobHandler;
//import com.iqiyi.job.core.handler.annotation.JobHander;
//import com.iqiyi.kiwi.utils.DateHelper;
//import com.iqiyi.vip.present.consts.PresentConstants;
//import com.iqiyi.vip.present.consts.VipTypeEnum;
//import com.iqiyi.vip.present.dao.PresentOrderDao;
//import com.iqiyi.vip.present.model.PresentConfig;
//import com.iqiyi.vip.present.model.PresentOrder;
//import com.iqiyi.vip.present.out.PresentVipRequest;
//import com.iqiyi.vip.present.service.PresentPerformService;
//import com.iqiyi.vip.present.task.PresentVipAsyncTask;
//import com.iqiyi.vip.present.utils.CloudConfigUtil;
//import com.iqiyi.vip.present.utils.PackagePresentVip;
//import com.iqiyi.vip.present.utils.PresentConfigCacheComponent;
//import com.iqiyi.vip.repository.ClusterAsyncTaskManager;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.sql.Timestamp;
//import java.util.Calendar;
//import java.util.Date;
//import java.util.List;
//
//@JobHander("sendUnsentKiwiPresentGoldRightJob")
//@Component
//@Slf4j
//public class SendUnsentKiwiPresentGoldRightJob extends IJobHandler {
//
//    public static final int JOB_EXECUTE_OFSET = 10;
//    @Resource
//    PresentOrderDao presentOrderDao;
//
//    @Resource
//    private ClusterAsyncTaskManager clusterAsyncTaskManager;
//
//    @Resource
//    private PresentPerformService presentPerformService;
//
//    @Value("${table.present.order.total.count}")
//    private int tableCount;
//
//    @Override
//    public ReturnT<String> execute(VipJobResp vipJob, String... strings) throws Exception {
//        long startTime = System.currentTimeMillis();
//        try {
//            log.info("[enter][startTime:{}][vipJobId:{}]", startTime, vipJob.getId());
//            Long configId = CloudConfigUtil.sendKiwiPresentGoldRightConfigId();
//            PresentConfig presentConfig = getPresentConfig(configId);
//            if (presentConfig == null) {
//                log.info("[SendUnsentKiwiPresentGoldRightJob][presentConfig is null]");
//                return ReturnT.FAIL;
//            }
//            int limitNum = CloudConfigUtil.getOneTableProcessKiwiPresentGoldOrderRightNum();
//            searchAndProcessPresentOrder(presentConfig, limitNum);
//            return ReturnT.SUCCESS;
//        } catch (Exception e) {
//            log.error("ct:{}ms", System.currentTimeMillis() - startTime, e);
//            return ReturnT.FAIL;
//        }
//    }
//
//    private void searchAndProcessPresentOrder(PresentConfig presentConfig, Integer limitNum) {
//        for (int index = 0; index < tableCount; index++) {
//            log.info("[search UnsentRighrOrders in table {}]", index);
//            List<PresentOrder> unsentRightOrders = getUnsentRightOrders(index, presentConfig.getId().intValue(), limitNum);
//            if (CollectionUtils.isEmpty(unsentRightOrders)) {
//                continue;
//            }
//            processUnsentRightPresentOrder(unsentRightOrders, presentConfig, index);
//        }
//    }
//
//    private void processUnsentRightPresentOrder(List<PresentOrder> presentOrders, PresentConfig presentConfig, Integer index) {
//        log.info("[processUnsentRightPresentOrder][orderSize:{}]", presentOrders.size());
//        if (CollectionUtils.isEmpty(presentOrders)) {
//            return;
//        }
//        Date taskRunTime = getTaskRunTime(index);
//        for (PresentOrder presentOrder : presentOrders) {
//            Integer receiveType = presentPerformService.queryReceiveTypeByIdWithDefault(presentOrder.getPresentPerformId());
//            PresentVipRequest presentVipRequest = PackagePresentVip.packagePresentVipJobRequest(presentOrder, presentConfig, receiveType);
//            clusterAsyncTaskManager.insertTask(new PresentVipAsyncTask(presentVipRequest, presentOrder), taskRunTime);
//            log.info("[insert async task][presentVipRequest:{}][presentOrder:{}]", presentVipRequest, presentOrder);
//        }
//    }
//     private Date getTaskRunTime(Integer index) {
//        return new Date(DateHelper.caculateTime(new Timestamp(System.currentTimeMillis()), (index + 1) * JOB_EXECUTE_OFSET, Calendar.MINUTE).getTime());
//     }
//
//    private PresentConfig getPresentConfig(Long presentConfigId) {
//        String buyType = String.valueOf(VipTypeEnum.kiwi.getVipType());
//        return getPresentConfigById(buyType, presentConfigId);
//    }
//
//    private List<PresentOrder> getUnsentRightOrders(int index, int configId, int limitNum) {
//        List<PresentOrder> orderList = Lists.newCopyOnWriteArrayList();
//        String startTimeStr = CloudConfigUtil.searchUnsentGoldRightPresentOrderBeginTime();
//        String endTimeStr = CloudConfigUtil.searchUnsentGoldRightPresentOrderEndTime();
//        String tableNo = String.format("%02d", index);
//        List<PresentOrder> oneDayUnsentRighrOrders = presentOrderDao.queryKiwiPresentGoldNotReceiveOrderByRange(startTimeStr, endTimeStr, tableNo, configId, limitNum);
//        orderList.addAll(oneDayUnsentRighrOrders);
//        return orderList;
//    }
//
//    private PresentConfig getPresentConfigById(String buyVipType, Long configId) {
//        List<PresentConfig> presentConfigs = PresentConfigCacheComponent.configMap.get(buyVipType);
//        if (CollectionUtils.isEmpty(presentConfigs)) {
//            return null;
//        }
//        return presentConfigs.stream().filter(presentConfig -> presentConfig.getId().equals(configId)).findFirst().orElse(null);
//    }
//
//}