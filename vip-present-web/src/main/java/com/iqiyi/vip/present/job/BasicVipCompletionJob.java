//package com.iqiyi.vip.present.job;
//
//import com.alibaba.fastjson.JSON;
//import com.iqiyi.job.core.context.JobHelper;
//import com.iqiyi.job.core.handler.annotation.Job;
//import com.iqiyi.lego.rocketmq.core.StringRocketMQTemplate;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.rocketmq.client.producer.SendResult;
//import org.apache.shardingsphere.shardingjdbc.jdbc.core.datasource.ShardingDataSource;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.jdbc.core.JdbcTemplate;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//import static java.lang.Thread.sleep;
//
///**
// * 日报任务
// *
// * <AUTHOR>
// * @date 2019/6/3 13:54
// */
//@Component
//@Slf4j
//public class BasicVipCompletionJob {
//
//    @Resource
//    private ShardingDataSource shardingDataSource;
//
//    @Autowired(required = false)
//    @Qualifier("supplyBasicVipRmqTemplate")
//    private StringRocketMQTemplate supplyBasicVipRmqTemplate;
//
//    private static String SQL_TEMPLATE = "select user_id from boss_vip_user_${suffix} where user_id > ${startUid} and user_id <= ${endUid} and type_id in (1,4,16,58,56,57,5,54,60) group by user_id having min(create_time) < '${splitTime}' order by user_id asc limit ${pageSize}";
//
//    private JdbcTemplate jdbcTemplate;
//
//    @Value("${basic.supply.split.time}")
//    private String splitTime;
//
//    @PostConstruct
//    public void init() {
//        jdbcTemplate = new JdbcTemplate(shardingDataSource.getDataSourceMap().get("ds1"));
//    }
//
//    @Job("basicVipCompletionHandler")
//    public void basicVipCompletionHandler() {
//        String jobParam = JobHelper.getJobParam();
//        log.info("basicVipCompletionHandler jobParam: " + jobParam);
//        String[] params = jobParam.split(",");
//        Integer startIndex = StringUtils.isBlank(params[0]) ? null : Integer.parseInt(params[0]);
//        Integer endIndex = StringUtils.isBlank(params[1]) ? null : Integer.parseInt(params[1]);
//        Integer pageSize = StringUtils.isBlank(params[2]) ? null : Integer.parseInt(params[2]);
//        Long startUid = StringUtils.isBlank(params[3]) ? null : Long.parseLong(params[3]);
//        Long endUid = StringUtils.isBlank(params[4]) ? null : Long.parseLong(params[4]);
//
//        log.info("basicVipCompletionHandler startIndex:{}, endIndex:{}, pageSize:{}, startUid:{}, endUid:{}, splitTime:{}", startIndex, endIndex, pageSize, startUid, endUid, splitTime);
//
//        if (startIndex == null || endIndex == null || endIndex < startIndex || pageSize == null) {
//            JobHelper.handleFail("参数错误");
//            return;
//        }
//        startUid = startUid == null ? -1L : startUid;
//        endUid = endUid == null ? 9223372036854775807L : endUid;
//
//        for (int i = startIndex; i <= endIndex; i++) {
//            long lastUid = startUid;
//            log.info("basicVipCompletionHandler 开始执行第“{}“个表", i);
//            while (true) {
//                String finalSql = SQL_TEMPLATE
//                        .replace("${suffix}", String.format("%02d", i))
//                        .replace("${startUid}", lastUid + "")
//                        .replace("${endUid}", endUid + "")
//                        .replace("${pageSize}", pageSize + "")
//                        .replace("${splitTime}", splitTime);
//                log.info("basicVipCompletionHandler 表index:{}, sql:{}", i, finalSql);
//                List<Map<String, Object>> maps = jdbcTemplate.queryForList(finalSql);
//                if (CollectionUtils.isNotEmpty(maps)) {
//                    List<Long> userIds = maps.stream().map(m -> Long.valueOf(m.get("user_id").toString())).collect(Collectors.toList());
//                    SendResult sendResult = supplyBasicVipRmqTemplate.send(JSON.toJSONString(userIds));
//                    log.info("发送消息：{}, 发送结果：{}", JSON.toJSONString(userIds), sendResult);
//                    try {
//                        sleep(20);
//                    } catch (InterruptedException e) {
//                        e.printStackTrace();
//                    }
//                }
//                if (maps.size() < pageSize) {
//                    log.info("basicVipCompletionHandler 表index：{} 已经到最后一页", i);
//                    break;
//                }
//                lastUid = Long.parseLong(maps.get(maps.size() - 1).get("user_id").toString());
//            }
//            log.info("basicVipCompletionHandler 第“{}“个表执行完成", i);
//        }
//    }
//}
