//package com.iqiyi.vip.present.job;
//
//import com.google.common.collect.Lists;
//import com.google.common.collect.Maps;
//import com.google.common.collect.Sets;
//import com.iqiyi.job.core.biz.model.ReturnT;
//import com.iqiyi.job.core.biz.model.vo.VipJobResp;
//import com.iqiyi.job.core.handler.IJobHandler;
//import com.iqiyi.job.core.handler.annotation.JobHander;
//import com.iqiyi.kiwi.utils.DateHelper;
//import com.iqiyi.vip.present.consts.EnumOrderStatusCode;
//import com.iqiyi.vip.present.consts.EnumVipDirectlySendCode;
//import com.iqiyi.vip.present.consts.VipTypeEnum;
//import com.iqiyi.vip.present.model.PresentCondition;
//import com.iqiyi.vip.present.model.PresentConfig;
//import com.iqiyi.vip.present.model.PresentOrder;
//import com.iqiyi.vip.present.model.PresentPerform;
//import com.iqiyi.vip.present.service.PresentConditionService;
//import com.iqiyi.vip.present.service.PresentConfigService;
//import com.iqiyi.vip.present.service.PresentOrderService;
//import com.iqiyi.vip.present.service.PresentPerformService;
//import com.iqiyi.vip.present.utils.DateUtils;
//import com.iqiyi.vip.uitls.mail.MailHelper;
//import com.iqiyi.vip.uitls.model.MailHeader;
//import com.iqiyi.vip.uitls.model.TableMailContent;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.*;
//
///**
// * 日报任务
// *
// * <AUTHOR>
// * @date 2019/6/3 13:54
// */
//@JobHander("dayReportJob")
//@Component
//@Slf4j
//public class DayReportJob extends IJobHandler {
//
//    @Autowired
//    private PresentConfigService presentConfigService;
//
//    @Autowired
//    private PresentOrderService presentOrderService;
//
//    @Autowired
//    private PresentConditionService presentConditionService;
//
//    @Autowired
//    private PresentPerformService presentPerformService;
//
//    @Resource
//    private MailHelper mailHelper;
//
//    private List<String> tableTitles = Lists.newArrayList(
//        "日期",
//        "买",
//        "赠",
//        "备注",
//        "类型",
//        "订单总量",
//        "已赠送订单量",
//        "待领取订单量",
//        "退款订单量",
//        "赠送用户数"
//    );
//
//    private List<String> totalTableTitles = Lists.newArrayList(
//        "日期",
//        "(总)订单总量",
//        "(总)已赠送订单量",
//        "(总)待领取订单量",
//        "(总)退款订单量",
//        "(总)赠送用户数"
//    );
//
//    @Override
//    public ReturnT<String> execute(VipJobResp vipJob, String... strings) {
//       long start = System.currentTimeMillis();
//        log.info("##########execute DayReportJob start ###########");
//        log.info("[vipJobId:{}] [params:{}]", vipJob.getId(), strings);
//
//        String today = DateHelper.getFormatDate(DateHelper.getCurrentDate(), "yyyy-MM-dd");
//        String yesterday = DateUtils.calculateTime(DateHelper.getCurrentDate(), -1);
//        try {
//            // bvipType -> 配置列表
//            Map<VipTypeEnum, List<PresentConfig>> vipTypeMap = Maps.newHashMap();
//            for (VipTypeEnum vipTypeEnum : VipTypeEnum.values()) {
//                List<PresentConfig> allConfigList = presentConfigService.queryConfigListByVipType(
//                    String.valueOf(vipTypeEnum.getVipType()));
//                vipTypeMap.put(vipTypeEnum, allConfigList);
//            }
//
//            int totalOrderAmount = 0;
//            int totalReceiveAmount = 0;
//            int totalWaitReceiveAmount = 0;
//            int totalRefundAmount = 0;
//            Set<Long> totalUidSet = Sets.newHashSet();
//
//            List<List<Object>> oneDayList = Lists.newArrayList();
//            for (Map.Entry<VipTypeEnum, List<PresentConfig>> entry : vipTypeMap.entrySet()) {
//                VipTypeEnum buyVipTypeEnum = entry.getKey();
//                List<PresentConfig> configList = entry.getValue();
//                for (PresentConfig config : configList) {
//                    List<Object> data = Lists.newArrayList();
//                    data.add(yesterday);
//                    data.add(buyVipTypeEnum.getVipTypeName());
//                    data.add(VipTypeEnum.getVipTypeName(Integer.parseInt(config.getPvipType())));
//                    data.add(config.getRemark());
//
//                    // 赠送类型
//                    PresentCondition presentCondition = presentConditionService.queryConditionByIds(
//                        config.getConditionIds()).get(0);
//                    PresentPerform presentPerform = presentPerformService.queryPresentPerformById(presentCondition.getPerformId());
//                    data.add(EnumVipDirectlySendCode.getMsg(presentPerform.getIsDirectlySend()));
//
//                    // 订单数据
//                    PresentOrder orderRequest = new PresentOrder();
//                    orderRequest.setPresentConfigId(config.getId());
//                    orderRequest.setBuyType(String.valueOf(buyVipTypeEnum.getVipType()));
//                    orderRequest.setPresentType(config.getPvipType());
//                    List<PresentOrder> presentOrderList = presentOrderService.queryByParamsAndDate(orderRequest, yesterday, today);
//                    if (presentOrderList == null) {
//                        presentOrderList = Lists.newArrayList();
//                    }
//
//                    int receiveAmount = 0;
//                    int waitReceiveAmount = 0;
//                    int refundAmount = 0;
//                    Set<Long> uidSet = Sets.newHashSet();
//                    for (PresentOrder presentOrder : presentOrderList) {
//                        uidSet.add(presentOrder.getUid());
//                        if (EnumOrderStatusCode.ALREDY_PRESENT.getCode().equals(presentOrder.getStatus())) {
//                            receiveAmount++;
//                        } else if (EnumOrderStatusCode.REFUND_SUCCESS.getCode().equals(presentOrder.getStatus())) {
//                            refundAmount++;
//                        } else if (EnumOrderStatusCode.NOT_RECEIVE.getCode().equals(presentOrder.getStatus())) {
//                            waitReceiveAmount++;
//                        }
//                    }
//                    data.add(presentOrderList.size());
//                    data.add(receiveAmount);
//                    data.add(waitReceiveAmount);
//                    data.add(refundAmount);
//                    data.add(uidSet.size());
//                    oneDayList.add(data);
//
//                    totalOrderAmount += presentOrderList.size();
//                    totalReceiveAmount += receiveAmount;
//                    totalWaitReceiveAmount += waitReceiveAmount;
//                    totalRefundAmount += refundAmount;
//                    totalUidSet.addAll(uidSet);
//                }
//            }
//
//            // 表格内容封装
//            List<List<Object>> totalList = Lists.newArrayList();
//            List<Object> data = Lists.newArrayList(yesterday, totalOrderAmount, totalReceiveAmount,
//                totalWaitReceiveAmount, totalRefundAmount, totalUidSet.size());
//            totalList.add(data);
//
//            List<TableMailContent> tableMailContentList = Lists.newArrayList();
//            TableMailContent tb = new TableMailContent(yesterday + "日总数据", totalTableTitles, totalList);
//            tableMailContentList.add(tb);
//
//            // 按第五列-订单总量  排序
//            Collections.sort(oneDayList, new Comparator<List<Object>>() {
//                @Override
//                public int compare(List<Object> o1, List<Object> o2) {
//                    int o1OrderAmount = Integer.parseInt(String.valueOf(o1.get(5)));
//                    int o2OrderAmount = Integer.parseInt(String.valueOf(o2.get(5)));
//                    return o2OrderAmount - o1OrderAmount;
//                }
//            });
//            tb = new TableMailContent(yesterday + "日数据", tableTitles, oneDayList);
//            tableMailContentList.add(tb);
//            MailHeader mailHeader = new MailHeader();
//            mailHeader.setTos(strings);
//            mailHeader.setTitle("【重要监控】买赠日报 - " + yesterday);
//
//            // 发邮件
//            mailHelper.sendMail(mailHeader, tableMailContentList);
//        } catch (Exception e) {
//            log.error("[DayReportJob exception]", e);
//            return new ReturnT(ReturnT.FAIL_CODE, e.getMessage());
//        } finally {
//            log.info("##########execute DayReportJob end, cost: {}ms #########",
//                System.currentTimeMillis() - start);
//        }
//        return ReturnT.SUCCESS;
//    }
//
//}
