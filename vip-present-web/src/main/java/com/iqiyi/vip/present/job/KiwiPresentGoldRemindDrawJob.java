//package com.iqiyi.vip.present.job;
//
//import com.alibaba.fastjson.JSON;
//import com.google.common.collect.Lists;
//import com.google.common.util.concurrent.RateLimiter;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//import org.springframework.util.CollectionUtils;
//
//import javax.annotation.Resource;
//import java.util.Date;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//import com.iqiyi.job.core.biz.model.ReturnT;
//import com.iqiyi.job.core.biz.model.vo.VipJobResp;
//import com.iqiyi.job.core.handler.IJobHandler;
//import com.iqiyi.job.core.handler.annotation.JobHander;
//import com.iqiyi.kiwi.utils.DateHelper;
//import com.iqiyi.lego.rocketmq.core.StringRocketMQTemplate;
//import com.iqiyi.vip.present.dtos.RemindDrawGoldMsg;
//import com.iqiyi.vip.present.model.PresentConfig;
//import com.iqiyi.vip.present.model.PresentOrder;
//import com.iqiyi.vip.present.service.PresentConfigService;
//import com.iqiyi.vip.present.service.PresentOrderService;
//import com.iqiyi.vip.present.service.PresentService;
//
///**
// * 奇异果送黄金，提醒用户领取黄金任务
// * http://wiki.qiyi.domain/pages/viewpage.action?pageId=300581691
// *
// * <AUTHOR>
// * @date 2019/7/1 11:36
// */
//@JobHander("kiwiPresentGoldRemindDrawJob")
//@Component
//@Slf4j
//public class KiwiPresentGoldRemindDrawJob extends IJobHandler {
//
//    @Resource
//    private PresentOrderService presentOrderService;
//    @Resource
//    private PresentService presentService;
//    @Resource
//    private PresentConfigService presentConfigService;
//
//    /**
//     * 买奇异果送黄金
//     */
//    public static final String MSG_TYPE_KIWI_PRESENT_GOLD = "free_present_gold";
//
//
//    @Resource(name = "remindToEnsRmqTemplate")
//    private StringRocketMQTemplate stringRocketMQTemplate;
//
//    private final RateLimiter rateLimiter = RateLimiter.create(500.0);
//
//    @Override
//    public ReturnT<String> execute(VipJobResp vipJob, String... strings) {
//        long startTime = System.currentTimeMillis();
//        try {
//            log.info("[enter][vipJobId:{}]", vipJob.getId());
//            List<PresentOrder> notReceiveAllOrders = presentOrderService.queryKiwiPresentGoldNotReceiveOrder();
//            if (CollectionUtils.isEmpty(notReceiveAllOrders)) {
//                log.info("[notReceiveAllOrders is empty][vipJobId:{}]", vipJob.getId());
//                return ReturnT.SUCCESS;
//            }
//
//            List<PresentOrder> notReceiveOrders = Lists.newArrayList();
//            for (PresentOrder presentOrder : notReceiveAllOrders) {
//                if (null != presentOrder.getReceiveDeadlineTime() && presentOrder.getReceiveDeadlineTime().before(new Date())) {
//                    continue;
//                }
//                notReceiveOrders.add(presentOrder);
//            }
//
//            PresentConfig presentConfig = presentConfigService.queryConfigById(50L);
//            Map<Long, List<PresentOrder>> pendingDrawOrderMap = notReceiveOrders.stream().collect(Collectors.groupingBy(PresentOrder::getUid));
//            for (Map.Entry<Long, List<PresentOrder>> entry : pendingDrawOrderMap.entrySet()) {
//                rateLimiter.acquire();
//                sendRemindDrawMq(presentConfig, entry);
//            }
//
//            log.info("[end][vipJobId:{},notReceiveOrderSize:{},ct:{}ms]", vipJob.getId(), notReceiveOrders.size(), System.currentTimeMillis() - startTime);
//            return ReturnT.SUCCESS;
//        } catch (Exception e) {
//            log.error("ct:{}ms", System.currentTimeMillis() - startTime, e);
//            return ReturnT.FAIL;
//        }
//    }
//
//    private void sendRemindDrawMq(PresentConfig presentConfig, Map.Entry<Long, List<PresentOrder>> entry) {
//        try {
//            List<PresentOrder> presentOrders = entry.getValue();
//            RemindDrawGoldMsg remindDrawGoldMsg = new RemindDrawGoldMsg();
//            remindDrawGoldMsg.setMsgtype(MSG_TYPE_KIWI_PRESENT_GOLD);
//            remindDrawGoldMsg.setUid(entry.getKey());
//            remindDrawGoldMsg.setOrderCount(presentOrders.size());
//            remindDrawGoldMsg.setPresentDays(presentService.queryTimeByMixTime(presentOrders, Lists.newArrayList(), presentConfig));
//            remindDrawGoldMsg.setOrderTime(DateHelper.getFormatDate(presentOrders.get(0).getPayTime()));
//
//            String mqMsg = JSON.toJSONString(remindDrawGoldMsg);
//            stringRocketMQTemplate.send(mqMsg);
//            log.info("[sendMq][mqMsg:{}]", mqMsg);
//        } catch (Exception e) {
//            log.error("", e);
//        }
//    }
//}
