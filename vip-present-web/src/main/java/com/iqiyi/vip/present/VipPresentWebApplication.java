package com.iqiyi.vip.present;

import com.iqiyi.vip.present.config.datasource.DynamicDataSourceRegister;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;

/**
 * web模块-定时任务,后台配置
 *
 * <AUTHOR>
 * @date 2019/6/3 13:41
 */
@SpringBootApplication
@MapperScan(value = {"com.iqiyi.vip.present.mapper"}, sqlSessionFactoryRef = "sqlSessionFactory")
@Import({DynamicDataSourceRegister.class})
@ComponentScan(value={"com.iqiyi.vip"},
    excludeFilters = {
        @ComponentScan.Filter(type= FilterType.REGEX,pattern = "com\\.iqiyi\\.vip\\.uitls\\.task\\..*")
        ,@ComponentScan.Filter(type=FilterType.REGEX,pattern = "com\\.iqiyi\\.vip\\.uitls\\.utils\\..*")}
)
public class VipPresentWebApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(VipPresentWebApplication.class, args);
    }

}
