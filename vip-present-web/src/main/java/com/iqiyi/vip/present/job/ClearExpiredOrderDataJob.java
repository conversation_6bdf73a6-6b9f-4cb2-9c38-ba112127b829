package com.iqiyi.vip.present.job;

import com.iqiyi.job.core.context.JobHelper;
import com.iqiyi.job.core.handler.annotation.Job;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.shardingsphere.shardingjdbc.jdbc.core.datasource.ShardingDataSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;

import static java.lang.Thread.sleep;

/**
 * 清理过期订单数据job
 *
 * <AUTHOR>
 * @date
 */
@Component
@Slf4j
public class ClearExpiredOrderDataJob {

    @Resource
    private ShardingDataSource shardingDataSource;

    // 分页删除sql模版
    private static String SQL_TEMPLATE = "delete FROM present_order_${suffix} where deadline_end_time < '${expireTime}' AND create_time < '${expireTime}' limit ${deleteBatchSize}";

    private JdbcTemplate jdbcTemplate;

    @Value("${order.data.keep.days:365}")
    private Integer keepDays;
    @Value("${order.data.delete.batch.size:1000}")
    private Integer deleteBatchSize;

    @PostConstruct
    public void init() {
        jdbcTemplate = new JdbcTemplate(shardingDataSource.getDataSourceMap().get("ds"));
    }

    @Job("clearExpiredOrderDataHandler")
    public void clearExpiredOrderDataHandler() {
        String jobParam = JobHelper.getJobParam();
        log.info("clearExpiredOrderDataHandler start jobParam: " + jobParam);
        String[] params = jobParam.split(",");

        int startIndex = 0;
        int endIndex = 99;
        if (params.length == 2) {
            startIndex = Integer.parseInt(params[0]);
            endIndex = Integer.parseInt(params[1]);
        }

        String expireTime = DateFormatUtils.format(DateUtils.addDays(new Date(), -keepDays), "yyyy-MM-dd HH:mm:ss");
        for (int i = startIndex; i <= endIndex; i++) {
            log.info("clearExpiredOrderDataHandler 开始执行第“{}“个表, 截止时间:{}", i, expireTime);
            String finalSql = SQL_TEMPLATE
                    .replace("${suffix}", String.format("%02d", i))
                    .replace("${expireTime}", expireTime)
                    .replace("${deleteBatchSize}", deleteBatchSize.toString());

            int affectedRows;
            do {
                log.info("clearExpiredOrderDataHandler 表index:{}, sql:{}", i, finalSql);
                affectedRows = jdbcTemplate.update(finalSql);
                log.info("clearExpiredOrderDataHandler 表index:{}, 受影响的行数:{}", i, affectedRows);
                try {
                    sleep(500);
                } catch (InterruptedException e) {
                    log.error("", e);
                }
            } while (affectedRows > 0);
            log.info("clearExpiredOrderDataHandler 当前index:{} 执行完结", i);

            try {
                sleep(1000);
            } catch (InterruptedException e) {
                log.error("", e);
            }
            log.info("clearExpiredOrderDataHandler 第“{}“个表执行完成", i);
        }
    }

}
